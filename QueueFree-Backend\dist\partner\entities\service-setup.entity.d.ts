import { Service } from './service.entity';
export declare class ServiceSetup {
    id: number;
    service: Service;
    setupData: {
        selectedDays: string[];
        availableHours?: {
            [day: string]: Array<{
                start: string;
                end: string;
            }>;
        };
        timeSlots?: Array<{
            start: string;
            end: string;
        }>;
        servingTime: string;
        basePrice: string;
        useDayWiseTimeSlots?: boolean;
        timeSlotsByDay?: {
            [day: string]: Array<{
                start: string;
                end: string;
            }>;
        };
        hasSubUnits?: boolean;
        subUnits?: Array<{
            name: string;
            availableHours: {
                [day: string]: Array<{
                    start: string;
                    end: string;
                }>;
            };
            dayWiseAvailableHours?: {
                [day: string]: Array<{
                    start: string;
                    end: string;
                }>;
            };
            avgServeTime: string;
            pricePerHead: string;
            selectedDays?: string[];
            useDayWiseTimeSlots?: boolean;
            timeSlots?: Array<{
                start: string;
                end: string;
            }>;
        }>;
    };
    leaveDays: {
        main: string[];
        subUnits: {
            [subUnitId: string]: string[];
        };
    };
    graceTime: number;
    createdAt: Date;
    updatedAt: Date;
}
