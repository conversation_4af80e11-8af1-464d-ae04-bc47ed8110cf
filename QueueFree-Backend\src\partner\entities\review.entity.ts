import { En<PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn } from 'typeorm';
import { Service } from './service.entity';

@Entity()
export class Review {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  serviceId: number;

  @ManyToOne(() => Service, service => service.reviews)
  service: Service;

  @Column()
  userId: string;

  @Column()
  userName: string;

  @Column({ nullable: true })
  userProfilePic: string;

  @Column()
  rating: number;

  @Column('text')
  comment: string;

  @CreateDateColumn()
  createdAt: Date;
}
