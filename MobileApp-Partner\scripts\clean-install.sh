#!/bin/bash

# Exit on any error
set -e

echo "Cleaning and reinstalling dependencies..."

# Remove node_modules and other cached files
rm -rf node_modules
rm -rf .expo
rm -rf android/app/build
rm -rf android/.gradle

# Install dependencies with legacy peer deps to avoid conflicts
npm install --legacy-peer-deps

# Apply patches
echo "Applying patches..."
npx patch-package

# Run prebuild
echo "Running prebuild for Android..."
npx expo prebuild --platform android --clean

echo "Setup complete!"
echo "You can now run 'npx eas build --platform android' to build the app" 