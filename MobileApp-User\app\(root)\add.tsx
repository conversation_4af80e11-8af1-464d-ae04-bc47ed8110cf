import React from "react";
import {
  Text,
  SafeAreaView,
  View,
  TextInput,
  Alert,
  TouchableOpacity,
  Image,
  Keyboard,
  StatusBar,
} from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import { images } from "@/constants";

const AddScreen = () => {
   return (
    
      <SafeAreaView className="flex-1 bg-white justify-center items-center">
        <StatusBar barStyle="dark-content" backgroundColor="white" />
        <View className="flex-1 px-8 pt-12 items-center">
          <View className="w-full flex-row  mt-4">
            <TouchableOpacity
              className="flex-row items-center absolute left-0 -top-2 border border-secondary-600 w-12 h-12 rounded-full justify-center"
              onPress={() =>router.push("/(root)/(tabs)/home")}
            >
              <Image source={images.back} className="w-4 h-4" />
            </TouchableOpacity>
            <View className="flex-1 items-center ">
              <Text className="font-poppins-medium text-2xl mb-2">
                Create Personal Queue
              </Text>
              <Text className="font-poppins-regular text-danger-500">
                Monthly Limit: 3
              </Text>
              
            </View>
          </View>
          </View>
              </SafeAreaView>
  );
};

export default AddScreen;
