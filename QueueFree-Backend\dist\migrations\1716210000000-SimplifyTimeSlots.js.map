{"version": 3, "file": "1716210000000-SimplifyTimeSlots.js", "sourceRoot": "", "sources": ["../../src/migrations/1716210000000-SimplifyTimeSlots.ts"], "names": [], "mappings": ";;;AAEA,MAAa,8BAA8B;IAA3C;QACE,SAAI,GAAG,gCAAgC,CAAC;IAmG1C,CAAC;IAjGQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAG5D,MAAM,aAAa,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAE5F,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;YAClC,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;gBAClC,IAAI,QAAQ,GAAG,KAAK,CAAC;gBAGrB,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;oBAE3B,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;wBAC9B,SAAS,CAAC,cAAc,GAAG,EAAE,CAAC;wBAC9B,QAAQ,GAAG,IAAI,CAAC;oBAClB,CAAC;oBAGD,IAAI,SAAS,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACjF,KAAK,MAAM,GAAG,IAAI,SAAS,CAAC,cAAc,EAAE,CAAC;4BAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gCACjD,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;gCAC9D,QAAQ,GAAG,IAAI,CAAC;4BAClB,CAAC;wBACH,CAAC;oBACH,CAAC;oBAGD,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;wBACpE,CAAC,CAAC,SAAS,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;wBAEtF,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC;4BAC1C,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gCACnC,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC;4BACtD,CAAC,CAAC,CAAC;4BACH,QAAQ,GAAG,IAAI,CAAC;wBAClB,CAAC;oBACH,CAAC;gBACH,CAAC;gBAGD,IAAI,SAAS,CAAC,WAAW,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;wBACnD,MAAM,IAAI,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;wBAGnC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;4BACzB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;4BACzB,QAAQ,GAAG,IAAI,CAAC;wBAClB,CAAC;wBAGD,IAAI,IAAI,CAAC,qBAAqB,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACrF,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;gCAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;oCACnD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;oCAC3D,QAAQ,GAAG,IAAI,CAAC;gCAClB,CAAC;4BACH,CAAC;wBACH,CAAC;wBAGD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;4BAC1D,CAAC,CAAC,IAAI,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;4BAE5E,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;gCACrC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oCAC9B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;gCAC5C,CAAC,CAAC,CAAC;gCACH,QAAQ,GAAG,IAAI,CAAC;4BAClB,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;gBAGD,IAAI,QAAQ,EAAE,CAAC;oBACb,MAAM,WAAW,CAAC,KAAK,CACrB,0DAA0D,EAC1D,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CACtC,CAAC;oBACF,OAAO,CAAC,GAAG,CAAC,iCAAiC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC3D,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,KAAK,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC5D,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;IAE7E,CAAC;CACF;AApGD,wEAoGC"}