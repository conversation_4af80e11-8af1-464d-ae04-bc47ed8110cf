import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUniqueSlotIdToQueue1626123401000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE queues ADD COLUMN uniqueSlotId VARCHAR(255) NULL`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE queues DROP COLUMN uniqueSlotId`);
  }
} 