import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  StatusBar,
  SafeAreaView,
  ActivityIndicator,
  Alert,
} from "react-native";
import { useRouter } from "expo-router";
import Stepper from "../../components/Stepper";
import { images } from "@/constants";
import ButtonBlueMain from "@/components/ButtonBlueMain";
import ButtonBlue from "@/components/ButtonBlue";
import * as SecureStore from "expo-secure-store";
import { useUser } from "@clerk/clerk-expo";

const nextSteps = [
  {
    title: "1. Our team will verify your application (within 24 hours)",
  },
  {
    title: "2. You'll receive email updates about your verification status",
  },
  {
    title: "3. Additional documents ma be requested if needed",
  },
];

export default function ReviewScreen() {
  const router = useRouter();
  const { user } = useUser();
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'success' | 'failed'>('pending');
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [showTermsError, setShowTermsError] = useState(false);
  const [setupComplete, setSetupComplete] = useState(false);
  const [serviceId, setServiceId] = useState<string | null>(null);

  useEffect(() => {
    const initialize = async () => {
      await loadServiceId();
      await checkVerificationStatus(); // Initial check
      await checkSetupStatus();
    };
    initialize();
    
    // Poll more frequently for better responsiveness
    const statusInterval = setInterval(checkVerificationStatus, 5000); // Every 5 seconds
    const setupInterval = setInterval(checkSetupStatus, 5000);
    
    return () => {
      clearInterval(statusInterval);
      clearInterval(setupInterval);
    };
  }, []);

  // Reset error state when terms are accepted
  useEffect(() => {
    if (termsAccepted) {
      setShowTermsError(false);
    }
  }, [termsAccepted]);

  const handleDashboardClick = async () => {
    if (!termsAccepted) {
      setShowTermsError(true);
      return;
    }
    
    try {
      const email = user?.primaryEmailAddress?.emailAddress;
      if (!email) throw new Error('Email not found');

      // Only update terms acceptance and proceed to dashboard
      const response = await fetch(
        `http://**************:3000/api/partner/accept-terms/${encodeURIComponent(email)}`,
        {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' }
        }
      );

      if (!response.ok) throw new Error('Failed to accept terms');

      // Update local storage with terms acceptance
      const currentStatus = await SecureStore.getItemAsync("serviceStatus");
      if (currentStatus) {
        const updatedStatus = {
          ...JSON.parse(currentStatus),
          termsAccepted: true
        };
        await SecureStore.setItemAsync("serviceStatus", JSON.stringify(updatedStatus));
      }
      
      // Proceed to dashboard without checking setup
      router.replace("/(root)/(tabs)/dashboard");
      
    } catch (error) {
      console.error("Error accepting terms:", error);
      Alert.alert("Error", "Failed to accept terms and conditions");
    }
  };

  const checkStatus = async () => {
    try {
      const status = await SecureStore.getItemAsync("serviceStatus");
      if (status) {
        const { verificationStatus } = JSON.parse(status);
        setVerificationStatus(verificationStatus);
      }
    } catch (error) {
      console.error("Error checking status:", error);
    }
  };

  const checkSetupStatus = async () => {
    try {
      const isComplete = await SecureStore.getItemAsync('setupComplete');
      const setupData = await SecureStore.getItemAsync('serviceSetupData');
      
      // Only consider setup complete if we have both the flag and actual data
      setSetupComplete(isComplete === 'true' && !!setupData);
    } catch (error) {
      console.error('Error checking setup status:', error);
    }
  };

  const handleResubmit = () => {
    SecureStore.deleteItemAsync("serviceStatus")
      .then(() => router.push("/service-setup/step1"))
      .catch(error => console.error("Error clearing status:", error));
  };

  const loadServiceId = async (): Promise<string | null> => {
    try {
      // First try to get it from serviceStatus
      const savedStatus = await SecureStore.getItemAsync('serviceStatus');
      console.log('Loaded service status:', savedStatus);
      
      if (savedStatus) {
        try {
          const parsedStatus = JSON.parse(savedStatus);
          if (parsedStatus.serviceId) {
            const cleanId = parsedStatus.serviceId.toString().replace(/[^0-9]/g, '');
            if (cleanId && !isNaN(parseInt(cleanId, 10)) && parseInt(cleanId, 10) > 0) {
              console.log('Using service ID from status:', cleanId);
              setServiceId(cleanId);
              return cleanId;
            }
          }
        } catch (error) {
          console.error('Error parsing service status:', error);
        }
      }

      // Fallback to direct serviceId storage
      const savedServiceId = await SecureStore.getItemAsync('serviceId');
      console.log('Direct service ID:', savedServiceId);
      
      if (savedServiceId) {
        const cleanId = savedServiceId.toString().replace(/[^0-9]/g, '');
        if (cleanId && !isNaN(parseInt(cleanId, 10)) && parseInt(cleanId, 10) > 0) {
          console.log('Using direct service ID:', cleanId);
          setServiceId(cleanId);
          return cleanId;
        }
      }

      console.error('No valid service ID found');
      router.replace('/service-setup/step1');
      return null;
    } catch (error) {
      console.error('Error loading service ID:', error);
      router.replace('/service-setup/step1');
      return null;
    }
  };

  const tryToSaveSetup = async () => {
    try {
      const setupData = await SecureStore.getItemAsync('serviceSetupData');
      const serviceId = await SecureStore.getItemAsync('serviceId');
      
      if (!setupData || !serviceId) {
        console.log('Setup data validation failed:', { hasSetupData: !!setupData, hasServiceId: !!serviceId });
        return; // Just return instead of throwing error
      }
  
      console.log('Valid setup data found, proceeding with save');
      await saveSetupToDatabase();
      await SecureStore.setItemAsync('setupComplete', 'true');
    } catch (error) {
      console.error('Failed to save setup:', error);
      // Don't show alert since this is an optional operation
    }
  };

  const saveSetupToDatabase = async () => {
    try {
      const setupData = await SecureStore.getItemAsync('serviceSetupData');
      const serviceId = await SecureStore.getItemAsync('serviceId');
  
      if (!setupData || !serviceId) {
        throw new Error('Setup data or service ID not found');
      }
  
      const response = await fetch(
        `http://**************:3000/api/partner/service-setup/${serviceId}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            setupData: JSON.parse(setupData)
          })
        }
      );
  
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save setup data');
      }
  
      console.log('Setup data saved successfully to database');
    } catch (error) {
      console.error('Error saving setup to database:', error);
      throw error; // Rethrow to be handled by caller
    }
  };

  const checkVerificationStatus = async () => {
    try {
      if (!user?.primaryEmailAddress?.emailAddress) {
        console.log('No email available');
        return;
      }
  
      const email = encodeURIComponent(user.primaryEmailAddress.emailAddress);
      console.log('Checking verification status for:', email);
  
      const response = await fetch(
        `http://**************:3000/api/partner/verification-status/${email}`
      );
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch status');
      }
  
      const { status } = await response.json();
      console.log('Received status:', status);
      
      // Update status
      const currentStatus = await SecureStore.getItemAsync("serviceStatus");
      if (currentStatus) {
        const parsedStatus = JSON.parse(currentStatus);
        const updatedStatus = {
          ...parsedStatus,
          verificationStatus: status
        };
        
        await SecureStore.setItemAsync("serviceStatus", JSON.stringify(updatedStatus));
        setVerificationStatus(status);
  
        // Only try to save setup if verification successful AND we have setup data
        if (status === 'success') {
          const setupData = await SecureStore.getItemAsync('serviceSetupData');
          if (setupData) {
            await tryToSaveSetup();
          }
        }
      }
    } catch (error) {
      console.error("Error checking verification status:", error);
    }
  };

  const handleSetupClick = async () => {
    try {
      // Check/get service ID
      const serviceId = await loadServiceId();
      if (serviceId) {
        await SecureStore.setItemAsync('serviceStatus', JSON.stringify({
          ...JSON.parse(await SecureStore.getItemAsync('serviceStatus') || '{}'),
          serviceId
        }));
        router.push('/service-setup/setup');
      } else {
        Alert.alert('Error', 'Failed to load service information');
      }
    } catch (error) {
      console.error('Error navigating to setup:', error);
      Alert.alert('Error', 'Failed to access setup screen');
    }
  };

  const renderSetupButton = () => (
    <View className="flex-col px-14 w-[350px] mt-16 justify-center items-center bg-primary-200 p-6 rounded-2xl mb-6">
      {setupComplete ? (
        <>
        <View className="flex-col w-[350px] justify-center items-center bg-primary-200 px-14 py-4 rounded-2xl">
            <View className="flex-row justify-center items-center mb-2">
              <View className="w-10 h-10 bg-primary-500/10 rounded-full items-center justify-center mr-4">
          <ActivityIndicator size="small" color="#159AFF" />
              </View>
              <View>
          <Text className="font-[Poppins-Medium] text-primary-500 text-base">
            Verification in Progress
          </Text>
          <Text className="font-[Poppins-Regular] text-sm text-gray-500">
            Estimated time: 24-48 hours
          </Text>
              </View>
            </View>
            <View className="h-[1px] w-full bg-secondary-500/20 my-2" />
          <View className="flex-row items-center my-4">
            <Image source={images.check} className="w-4 h-3 mr-2" tintColor="#263238" />
            <Text className="font-[Poppins-Medium] text-secondary-500 text-base">
              Setup Saved
            </Text>
          </View>
          <ButtonBlueMain
            label="Edit Setup"
            onPress={() => {
              router.push("/service-setup/setup");
            }}
            bgVariant="primary"
            textVariant="primary"
            className="w-48 h-10 rounded-full bg-secondary-500"
            textClassName="text-sm"
            iconClassName="w-3 h-3 mr-2"
            icon={images.tool}
          />
          </View>
        </>
      ) : (
        <>
         <View className="flex-col w-[350px] justify-center items-center bg-primary-200 px-14 py-4 rounded-2xl">
            <View className="flex-row justify-center items-center mb-2">
              <View className="w-10 h-10 bg-primary-500/10 rounded-full items-center justify-center mr-4">
                <ActivityIndicator size="small" color="#159AFF" />
              </View>
              <View>
                <Text className="font-[Poppins-Medium] text-primary-500 text-base">
                  Verification in Progress
                </Text>
                <Text className="font-[Poppins-Regular] text-sm text-gray-500">
                  Estimated time: 24-48 hours
                </Text>
              </View>
            </View>
            <View className="h-[1px] w-full bg-secondary-500/20 my-2" />
          <Text className="text-sm font-[Poppins-Medium] text-secondary-600 text-center mb-2">
            While we verify your service, you can complete your service setup
          </Text>
          <ButtonBlueMain
            label="Service Setup"
            onPress={handleSetupClick}
            bgVariant="primary"
            textVariant="primary"
            className="w-50 h-16 rounded-full bg-secondary-500"
            textClassName="text-sm"
            iconClassName="w-3 h-3 mr-2"
            icon={images.tool}
          />
          </View>
        </>
      )}
    </View>
  );

  const renderStatusContent = () => {
    switch (verificationStatus) {
      case 'success':
        return (
          <View className="items-center justify-center h-[600px] w-[300px]">
            <Image source={images.confetti} className="w-40 h-40 mb-10" />

            <Text className="text-2xl font-[Poppins-Medium] text-secondary-500 mb-2">
              Verification Successful!
            </Text>
            <Text className=" font-[Poppins-Regular] text-secondary-600 text-center mb-2">
            Your Service has been verified  Successfully!
              </Text>
            
          </View>
        );

      case 'failed':
        return (
          <View className="items-center justify-center h-[600px] w-[300px]">
            <Image source={images.failed} className="w-40 h-40 mb-10" />
          
            <Text className="text-2xl font-[Poppins-Medium] text-secondary-500 mb-2">
              Verification Failed!
            </Text>
            <Text className=" font-[Poppins-Regular] text-secondary-600 text-center mb-8">
            Unfortunately, we couldn't verify your service. Please try again.
              </Text>
              <ButtonBlue
              label="Resubmit Application"
              onPress={handleResubmit}
              bgVariant="danger"
              textVariant="primary"
              className="w-[330px]"
            />
            
          </View>
        );

      default:
        return (
          <>
            <View className="mt-8 py-5 px-6 w-[370px] rounded-2xl flex-row bg-success-100 border border-success-400 justify-start items-center">
              <Image source={images.success} className="w-[35px] h-[35px] mr-4" />
              <View className="items-start">
                <Text className="text-[16px] font-poppins-medium text-secondary-500">
                  Application Submitted!
                </Text>
                <Text className="text-sm font-[Poppins-Regular] text-secondary-500">
                  Your verification request has been received
                </Text>
              </View>
            </View>
            
            {/* Next Steps Section */}
            <View className="w-[370px] mt-6">
              <Text className="text-[14px] text-secondary-600 font-[Poppins-Medium] mb-1">
                Next Steps
              </Text>
              {nextSteps.map((step, index) => (
                <View key={index} className="flex-row items-center p-2 rounded-xl">
                  <View className="flex-1">
                    <Text className="font-[Poppins-Regular] text-secondary-600 text-[12px]">
                      {step.title}
                    </Text>
                  </View>
                </View>
              ))}
            </View>

            {/* Progress Indicator and Setup Button */}
            {renderSetupButton()}
          </>
        );
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <View className="px-8 pt-8 pb-4 border-b border-secondary-400">
        <View className="">
          <View className="w-full flex-row">
            <TouchableOpacity
              className="flex-row items-center absolute left-0 border border-secondary-600 w-12 h-12 rounded-full justify-center"
              onPress={() => router.back()}
            >
              <Image source={images.back} className="w-4 h-4" />
            </TouchableOpacity>
            <View className="flex-1 items-center mt-2 mb-4">
              <Text className="font-poppins-medium text-2xl mb-4">
                Service Verification
              </Text>
              <View className="w-[300px]">
                <Text className="font-poppins-regular text-center text-sm text-secondary-500/50">
                  Complete the verification process to get your service verified
                </Text>
              </View>
            </View>
          </View>
        </View>
        <Stepper
          currentStep={3}
          steps={["Service Info", "Documents", "Review"]}
        />
      </View>

      <ScrollView className="flex-1">
        <View className="p-5 items-center">
          {renderStatusContent()}
        </View>
      </ScrollView>

      {verificationStatus === 'success' && (
        <View>
          <View className="flex-row items-center px-10 mb-6">
            <TouchableOpacity 
              onPress={() => setTermsAccepted(!termsAccepted)}
              className="flex-row items-center"
            >
              <View 
                className={`w-6 h-6 items-center justify-center rounded-md mr-3 ${
                  termsAccepted 
                    ? 'bg-primary-500' 
                    : showTermsError 
                      ? 'border-[1.5px] border-danger-500' 
                      : 'border-[1.5px] border-gray-300'
                }`}
              >
                {termsAccepted && <Image source={images.check} className="w-4 h-3" tintColor="white" />}
              </View>
              <Text 
                className={`text-sm font-poppins-medium ${
                  showTermsError ? 'text-danger-500' : 'text-secondary-500'
                }`}
              >
                I Agree to the Terms and Conditions
              </Text>
            </TouchableOpacity>
          </View>
          <View className="p-4 border-t justify-center items-center border-gray-200">
            <ButtonBlueMain
              label="Go to Dashboard"
              onPress={handleDashboardClick}
              bgVariant="primary"
              textVariant="primary"
              className="w-[350px] h-[80px]"
            />
          </View>
        </View>
      )}
    </SafeAreaView>
  );
}
