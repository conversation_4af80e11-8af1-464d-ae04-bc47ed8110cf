import 'react-native-get-random-values';  // Add this line first
import { Stack } from "expo-router";
import { useEffect } from "react";
import { useFonts } from "expo-font";
import * as SplashScreen from "expo-splash-screen";
import { ClerkLoaded, Clerk<PERSON>rovider, useUser } from "@clerk/clerk-expo";
import { tokenCache } from "@/lib/auth";
import "../global.css";
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { TouchableOpacity } from "react-native";
import axios from "axios";

// Keep the splash screen visible until fonts are loaded
SplashScreen.preventAutoHideAsync();

// Get Clerk publishable key from environment variables
const publishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!;
if (!publishableKey) {
  throw new Error(
    "Missing Publishable Key. Please set EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY in your .env"
  );
}

// Component to update clerk ID
const UpdateClerkId = () => {
  const { user } = useUser();
  
  useEffect(() => {
    const updateClerkId = async () => {
      if (!user || !user.primaryEmailAddress) return;
      
      try {
        const email = user.primaryEmailAddress.emailAddress;
        
        // First check if the user exists in our backend by making a GET request
        try {
          const profileCheckResponse = await axios.get(
            `http://**************:3000/api/customer/profile/${encodeURIComponent(email)}`,
            {
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
              },
              timeout: 5000,
            }
          );
          
          // If user doesn't exist or request failed, try to create them
          if (!profileCheckResponse.data || !(profileCheckResponse.data as any).success) {
            console.log("User not found in our system, creating...");
            const createResponse = await axios.post(
              "http://**************:3000/api/customer/register",
              {
                email: email,
                fullName: user.fullName,
                clerkId: user.id
              },
              {
                headers: {
                  "Content-Type": "application/json",
                  Accept: "application/json",
                },
                timeout: 5000,
              }
            );
            console.log("User creation result:", createResponse.data);
          }
        } catch (checkError) {
          console.log("User check failed, will try creating:", checkError);
          // If check fails, try to create the user
          try {
            await axios.post(
              "http://**************:3000/api/customer/register",
              {
                email: email,
                fullName: user.fullName,
                clerkId: user.id
              },
              {
                headers: {
                  "Content-Type": "application/json",
                  Accept: "application/json",
                },
                timeout: 5000,
              }
            );
          } catch (createError) {
            console.log("User creation also failed:", createError);
          }
        }
        
        // Now update the clerk ID
        console.log("Updating clerk ID for:", email);
        const updateResponse = await axios.put(
          "http://**************:3000/api/customer/update-clerk-id",
          {
            email: email,
            clerkId: user.id
          },
          {
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
            },
            timeout: 10000,
          }
        );
        console.log("Updated clerk ID for user:", email, updateResponse.data);
      } catch (error) {
        console.error("Error updating clerk ID:", error);
      }
    };
    
    if (user) {
      updateClerkId();
    }
  }, [user]);
  
  return null;
};

export default function RootLayout() {
  const [loaded] = useFonts({
    "Poppins-Light": require("../assets/fonts/poppins.light.ttf"),
    "Poppins-Regular": require("../assets/fonts/poppins.regular.ttf"),
    "Poppins-Medium": require("../assets/fonts/poppins.medium.ttf"),
    "Poppins-SemiBold": require("../assets/fonts/poppins.semibold.ttf"),
    "Poppins-Bold": require("../assets/fonts/poppins.bold.ttf"),
  });



  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) return null;

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ClerkProvider tokenCache={tokenCache} publishableKey={publishableKey}>
        <ClerkLoaded>
          <UpdateClerkId />
          <Stack 
            screenOptions={{
              headerStyle: {
                backgroundColor: "#ffffff",
              },
              headerTintColor: "#000000",
              headerTitleStyle: {
                fontFamily: "Poppins-Medium",
              },
              headerShadowVisible: false,
              headerShown: false
            }} 
          >
            <Stack.Screen name="(auth)" />
            <Stack.Screen name="(root)" />
            <Stack.Screen name="(location)" />
            <Stack.Screen name="(notifications)" />
            <Stack.Screen name="(vip)" />
          </Stack>
        </ClerkLoaded>
      </ClerkProvider>
    </GestureHandlerRootView>
  );
}