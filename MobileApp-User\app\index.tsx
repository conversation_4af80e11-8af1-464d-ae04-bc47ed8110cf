import { useAuth, useUser } from "@clerk/clerk-expo";
import { Redirect } from "expo-router";
import { useEffect, useState } from "react";
import * as SecureStore from 'expo-secure-store';

export default function Page() {
  const { isSignedIn } = useAuth();
  const { isLoaded, user } = useUser();
  const [loading, setLoading] = useState(true);
  const [locationStatus, setLocationStatus] = useState<string | null>(null);

  useEffect(() => {
    const checkStatus = async () => {
      try {
        const status = await SecureStore.getItemAsync('locationStatus');
        setLocationStatus(status);
      } catch (error) {
        console.error('Error checking location status:', error);
      } finally {
        setLoading(false);
      }
    };

    if (isLoaded && isSignedIn) {
      checkStatus();
    } else {
      setLoading(false);
    }
  }, [isLoaded, isSignedIn]);

  // Show loading state
  if (loading || !isLoaded) {
    return null;
  }

  // Not signed in - go to sign in
  if (!isSignedIn) {
    return <Redirect href="/(auth)/sign-in" />;
  }

  // Signed in but location not set up
  if (!locationStatus || locationStatus === 'pending') {
    return <Redirect href="/(location)/allow-location" />;
  }

  // Everything is set up - go to home
  return <Redirect href="/(root)/(tabs)/home" />;
}
