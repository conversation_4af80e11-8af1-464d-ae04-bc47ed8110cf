import React, { useEffect, useState } from "react";
import {
  Text,
  SafeAreaView,
  StatusBar,
  View,
  FlatList,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  ScrollView
} from "react-native";
import { useRouter } from "expo-router";
import { useUser } from "@clerk/clerk-expo";
import * as SecureStore from "expo-secure-store";
import { images } from "@/constants";

interface Service {
  _id: string;
  serviceName: string;
  serviceType: string;
  address: {
    details: {
      city: string;
      state: string;
      locality: string;
    };
    coordinates: {
      latitude: number;
      longitude: number;
    };
  };
  images: string[];
  isOpen: boolean;
  rating?: number;
  queueInfo: {
    waitingTime: number;
    membersInQueue: number;
    cost: number;
    servingTime: number;
    normalCount?: number;
    vipCount?: number;
    currentTimeSlot?: string;
  };
  distance?: string;
}

interface WishlistItem {
  serviceId: string;
}

const WishlistScreen = () => {
  const router = useRouter();
  const { user } = useUser();
  const [wishlistItems, setWishlistItems] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState("");

  const fetchWishlist = async () => {
    setLoading(true);
    setError("");
    try {
      // Try to get email from SecureStore first
      let userEmail = await SecureStore.getItemAsync("userEmail");

      // If not available, try to get from user object (Clerk)
      if (!userEmail && user?.primaryEmailAddress?.emailAddress) {
        console.log("Using Clerk email as fallback");
        userEmail = user.primaryEmailAddress.emailAddress;
      }

      if (!userEmail) {
        setError("User not logged in");
        setLoading(false);
        return;
      }

      console.log("Fetching wishlist for email:", userEmail);
      const apiUrl = `http://**************:3000/api/customer/wishlist/${encodeURIComponent(userEmail)}`;

      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error(`Error fetching wishlist: ${response.status}`);
      }

      const result = await response.json();

      if (result.status === "success" && Array.isArray(result.wishlist)) {
        console.log("Found wishlist items:", result.wishlist.length);
        if (result.wishlist.length === 0) {
          setWishlistItems([]);
          return;
        }

        const servicePromises = result.wishlist.map(async (item: WishlistItem) => {
          const serviceId = item.serviceId;
          console.log("Fetching service details for:", serviceId);
          const serviceApiUrl = `http://**************:3000/api/partner/services/${serviceId}`;

          const serviceResponse = await fetch(serviceApiUrl);

          if (!serviceResponse.ok) {
            console.error(`Error fetching service ${serviceId}:`, serviceResponse.status);
            return null;
          }

          const serviceData = await serviceResponse.json();

          // Service data is directly returned as the object without a wrapper
          if (serviceData && serviceData._id) {
            console.log(`Found valid service data for ${serviceId}`);

            // Check if service has subunits by fetching service setup data
            let hasSubUnits = false;
            let firstSubUnitPrice = null;

            try {
              const setupResponse = await fetch(
                `http://**************:3000/api/partner/service-setup/${serviceId}`
              );

              if (setupResponse.ok) {
                const setupData = await setupResponse.json();
                console.log(`Service setup data for ${serviceId}:`, setupData.hasSetup ? "Available" : "Not available");

                if (setupData.status === "success" && setupData.hasSetup && setupData.data) {
                  // Check if service has subunits
                  if (
                    setupData.data.hasSubUnits === true &&
                    Array.isArray(setupData.data.subUnits) &&
                    setupData.data.subUnits.length > 0
                  ) {
                    hasSubUnits = true;
                    // Get the price of the first subunit
                    firstSubUnitPrice = setupData.data.subUnits[0].pricePerHead;
                    console.log(`Service ${serviceId} has subunits. Using first subunit price: ${firstSubUnitPrice}`);

                    // Update the service price with the first subunit's price
                    if (serviceData.queueInfo && firstSubUnitPrice) {
                      serviceData.queueInfo.cost = parseInt(firstSubUnitPrice);
                    }
                  }
                }
              }
            } catch (error) {
              console.error(`Error checking subunits for service ${serviceId}:`, error);
              // Continue with default price if this fails
            }

            // Fetch queue counts for each service
            const queueInfo = await fetchRedisQueueCounts(serviceId);
            console.log(`Queue counts for ${serviceId}:`, queueInfo);

            // Update the queue counts exactly like in home.tsx
            if (queueInfo && serviceData.queueInfo) {
              serviceData.queueInfo.normalCount = queueInfo.normalCount;
              serviceData.queueInfo.vipCount = queueInfo.vipCount;
              // Store the current/next time slot
              serviceData.queueInfo.currentTimeSlot = queueInfo.timeSlot;
              // Update total members in queue (combined normal + VIP count)
              serviceData.queueInfo.membersInQueue = queueInfo.normalCount + queueInfo.vipCount;
              // Update waiting time based on updated queue count and serving time
              serviceData.queueInfo.waitingTime = serviceData.queueInfo.servingTime * (queueInfo.normalCount + queueInfo.vipCount);
            } else {
              // If no queue data is available, set default values
              if (serviceData.queueInfo) {
                serviceData.queueInfo.normalCount = 0;
                serviceData.queueInfo.vipCount = 0;
                serviceData.queueInfo.membersInQueue = 0;
                serviceData.queueInfo.currentTimeSlot = '';
                // Keep the original waiting time calculation
              }
            }

            return serviceData;
          } else {
            console.error(`Invalid service data for ${serviceId}:`, serviceData);
            return null;
          }
        });

        const services = (await Promise.all(servicePromises)).filter(Boolean) as Service[];
        console.log("Successfully loaded services:", services.length);
        setWishlistItems(services);
      } else {
        setWishlistItems([]);
      }
    } catch (error) {
      console.error("Error fetching wishlist:", error);
      setError("Failed to load wishlist. Please try again.");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Updated fetchRedisQueueCounts to only show queues for today or the nearest date - based on service-details.tsx
  const fetchRedisQueueCounts = async (serviceId: string): Promise<{ normalCount: number; vipCount: number; timeSlot: string } | null> => {
    try {
      console.log(`Fetching queue counts for service ${serviceId} using Redis keys`);

      // Check if service has subunits by fetching service setup data
      let hasSubUnits = false;
      let firstSubUnitIndex = 0;

      try {
        const setupResponse = await fetch(
          `http://**************:3000/api/partner/service-setup/${serviceId}`
        );

        if (setupResponse.ok) {
          const setupData = await setupResponse.json();
          console.log(`Service setup data for ${serviceId}:`, setupData.hasSetup ? "Available" : "Not available");

          if (setupData.status === "success" && setupData.hasSetup && setupData.data) {
            // Check if service has subunits
            if (
              setupData.data.hasSubUnits === true &&
              Array.isArray(setupData.data.subUnits) &&
              setupData.data.subUnits.length > 0
            ) {
              hasSubUnits = true;
              console.log(`Service ${serviceId} has ${setupData.data.subUnits.length} subunits`);
            }
          }
        }
      } catch (error) {
        console.error(`Error checking subunits for service ${serviceId}:`, error);
        // Continue with default (no subunits) if this fails
      }

      // Determine the API endpoint based on whether we're using subunits
      let apiUrl = `http://**************:3000/api/customer/queues/redis/${serviceId}/upcoming`;

      // If using subunits, add the subunit parameter for the first subunit
      if (hasSubUnits) {
        apiUrl += `?subUnitId=${firstSubUnitIndex}`;
        console.log(`Using first subunit (index: ${firstSubUnitIndex}) for queue counts in wishlist screen`);
      }

      // Get all active queues for this service using the Redis API with /upcoming endpoint
      const response = await fetch(apiUrl);

      if (!response.ok) {
        console.error(`Failed to fetch queues from Redis: ${response.status}`);
        return null;
      }

      const data = await response.json();

      if (data.status !== "success" || !data.queues) {
        console.log("No queue data available from Redis");
        return null;
      }

      console.log(`Successfully retrieved ${data.queues.length} queues from Redis`);

      // Get today's date in YYYY-MM-DD format
      const today = new Date().toISOString().split('T')[0];

      // First, filter and group queues by date
      const dateGroups: Record<string, any[]> = {};

      data.queues.forEach((queue: any) => {
        if (!queue || !queue.date) return;

        // Extract date part in YYYY-MM-DD format
        let dateStr;
        try {
          if (typeof queue.date === 'string') {
            dateStr = queue.date.split('T')[0];
          } else {
            dateStr = new Date(queue.date).toISOString().split('T')[0];
          }
        } catch (err) {
          console.error("Error parsing queue date:", err);
          return;
        }

        if (!dateGroups[dateStr]) {
          dateGroups[dateStr] = [];
        }
        dateGroups[dateStr].push(queue);
      });

      // If no dates with queues, return null
      if (Object.keys(dateGroups).length === 0) {
        console.log("No dates with queues found");
        return { normalCount: 0, vipCount: 0, timeSlot: '' };
      }

      // Sort dates and find the earliest one (today or future)
      const availableDates = Object.keys(dateGroups)
        .filter(date => date >= today)
        .sort();

      // If no future dates, return empty result
      if (availableDates.length === 0) {
        console.log("No future dates with queues found");
        return { normalCount: 0, vipCount: 0, timeSlot: '' };
      }

      // Get the earliest date
      const earliestDate = availableDates[0];
      console.log(`Using queues for date: ${earliestDate}`);

      // Only consider queues for this date
      const relevantQueues = dateGroups[earliestDate];

      // Group queues by time slot
      const timeSlotGroups: Record<string, any[]> = {};
      relevantQueues.forEach((queue: any) => {
        if (queue && queue.timeSlot) {
          if (!timeSlotGroups[queue.timeSlot]) {
            timeSlotGroups[queue.timeSlot] = [];
          }
          timeSlotGroups[queue.timeSlot].push(queue);
        }
      });

      // Get the current time in 24 hour format
      const now = new Date();
      const currentHours = now.getHours();
      const currentMinutes = now.getMinutes();
      const currentTime = currentHours * 60 + currentMinutes; // in minutes

      // Find current or next time slot
      let selectedTimeSlot = '';
      let selectedTimeSlotQueues: any[] = [];

      const timeSlots = Object.keys(timeSlotGroups);
      if (timeSlots.length > 0) {
        // Convert time slots to comparable format and find current or next slot
        const timeSlotTimes = timeSlots.map(slot => {
          const parts = slot.split(' - ');
          const startTimePart = parts[0].trim();
          const [timeValue, period] = startTimePart.split(' ');
          const [hourStr, minuteStr] = timeValue.split(':');
          let hour = parseInt(hourStr, 10);
          const minute = parseInt(minuteStr, 10);

          if (period.toUpperCase() === 'PM' && hour < 12) {
            hour += 12;
          } else if (period.toUpperCase() === 'AM' && hour === 12) {
            hour = 0;
          }

          return {
            slot,
            time: hour * 60 + minute
          };
        });

        // Sort by time
        timeSlotTimes.sort((a, b) => a.time - b.time);

        // If today, find current time slot, otherwise use the first time slot of the earliest future date
        if (earliestDate === today) {
          // For today, find the current time slot
          let selectedSlotInfo = timeSlotTimes[0]; // Default to first

          // First, try to find a time slot that contains the current time
          for (let i = 0; i < timeSlotTimes.length; i++) {
            const currentSlot = timeSlotTimes[i];
            const nextSlot = i < timeSlotTimes.length - 1 ? timeSlotTimes[i + 1] : null;

            // Check if current time is within this slot's range
            // For the last slot or if current time is before the next slot starts
            if (!nextSlot || currentTime < nextSlot.time) {
              if (currentTime >= currentSlot.time) {
                selectedSlotInfo = currentSlot;
                console.log(`Found current time slot: ${currentSlot.slot} (current time: ${currentHours}:${currentMinutes})`);
                break;
              }
            }
          }

          // If no current slot found (e.g., we're between slots or before all slots),
          // find the next upcoming slot
          if (selectedSlotInfo.time > currentTime) {
            console.log(`No current time slot active, using next slot: ${selectedSlotInfo.slot}`);
          }

          selectedTimeSlot = selectedSlotInfo.slot;
        } else {
          // For future date, use the first time slot
          selectedTimeSlot = timeSlotTimes[0].slot;
          console.log(`Using first time slot for future date: ${selectedTimeSlot}`);
        }

        selectedTimeSlotQueues = timeSlotGroups[selectedTimeSlot] || [];
      }

      // Count normal and VIP queues for the selected time slot
      const normalCount = selectedTimeSlotQueues.filter((q: any) =>
        q &&
        q.isVIP !== true &&
        q.status !== 'cancelled' &&
        q.status !== 'completed'
      ).length;

      const vipCount = selectedTimeSlotQueues.filter((q: any) =>
        q &&
        q.isVIP === true &&
        q.status !== 'cancelled' &&
        q.status !== 'completed'
      ).length;

      console.log(`Current/next time slot for ${earliestDate}: ${selectedTimeSlot}, Normal: ${normalCount}, VIP: ${vipCount}`);

      return { normalCount, vipCount, timeSlot: selectedTimeSlot };
    } catch (error) {
      console.error(`Error fetching Redis queue counts for service ${serviceId}:`, error);
      return null;
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchWishlist();
  };

  const removeFromWishlist = async (serviceId: string) => {
    if (!user?.primaryEmailAddress?.emailAddress) return;

    try {
      const email = user.primaryEmailAddress.emailAddress;

      const response = await fetch(
        `http://**************:3000/api/customer/wishlist/remove`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email, serviceId }),
        }
      );

      if (response.ok) {
        // Remove from local state
        setWishlistItems(prev => prev.filter(item => item._id !== serviceId));
      }
    } catch (error) {
      console.error('Error removing from wishlist:', error);
    }
  };

  useEffect(() => {
    fetchWishlist();
  }, [user]);

  const renderItem = ({ item, index }: { item: Service; index: number }) => (
    <TouchableOpacity
      key={item._id}
      className={`bg-white rounded-3xl border border-primary-200 overflow-hidden drop-shadow-lg shadow-lg shadow-primary-300 ${
        index !== wishlistItems.length - 1 ? "mb-8" : ""
      }`}
      onPress={() =>
        router.push({
          pathname: "/(root)/service-details",
          params: { id: item._id },
        })
      }
    >
      <View className="p-4 w-full">
        <View className="flex-row justify-center items-center">
          <Image
            source={
              item.images && item.images.length > 0
                ? { uri: item.images[0] }
                : images.profile
            }
            className="w-20 h-20 rounded-xl"
            style={{ backgroundColor: "#f5f5f5" }}
          />
          <View className="flex-1 p-3">
            <View className="flex-row justify-between items-start">
              <View className="flex-row flex-wrap items-center mr-2 flex-1">
                <Text className="font-poppins-medium text-lg" numberOfLines={2} style={{ maxWidth: '85%' }}>
                  {item.serviceName}
                </Text>
                {/* Rating display */}
                {item.rating ? (
                  <View className="flex-row items-center  bg-warning-500/10 rounded-lg px-[5px] py-[3px] ml-2">
                    <Image
                      source={images.star}
                      className="w-[10px] h-[10px] mr-1 mb-1"
                    />
                    <Text className="text-warning-500 font-poppins-medium text-[10px]">
                      {item.rating.toFixed(1)}
                    </Text>
                  </View>
                ) : null}
              </View>
              <TouchableOpacity
                onPress={() => removeFromWishlist(item._id)}
                className="p-1"
              >
                <Image
                  source={images.wish}
                  className="w-5 h-5"
                  style={{ tintColor: '#FF3B30' }}
                />
              </TouchableOpacity>
            </View>

            <Text className="text-secondary-600 text-sm mt-1">
              {item.address?.details?.locality || ''}, {item.address?.details?.city || ''}
            </Text>
          </View>
        </View>
        <View className="flex-row pt-4 px-1 justify-between mb-1">
          <View className="flex-row bg-primary-500/5 p-3 px-6 rounded-xl items-center justify-center">
            <View className="flex-row items-center mr-6">
              <Image
                source={images.time}
                className="w-5 h-5 mr-2"
                tintColor="#159AFF"
              />
              <Text className="text-secondary-600 text-sm">
                {`${item.queueInfo?.servingTime || 0}m per person`}
              </Text>
            </View>
            <View className="flex-row items-center">
              <Image
                source={images.run}
                className="w-4 h-5 mr-2"
                tintColor="#159AFF"
              />
              <Text className="text-secondary-600 text-sm">
                {item.queueInfo?.membersInQueue || 0} in queue
              </Text>

            </View>
          </View>
          <View className="flex-row items-center px-3 rounded-xl">
            <Text className="text-primary-500 font-poppins-medium">
              ₹{item.queueInfo?.cost || 0}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      {/* Fixed Header */}
      <View className="bg-white px-6 pt-6 pb-6">
        <View className="flex w-full justify-center items-center">
          <Text className="text-xl font-poppins-medium">My Wishlist</Text>
        </View>
      </View>

      <ScrollView
        className="flex-1"
        contentContainerStyle={{ paddingBottom: 120 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={["#159AFF"]}
            tintColor="#159AFF"
          />
        }
      >
        <View className="px-6 pt-4">
          {loading ? (
            <View className="flex-1 justify-center items-center py-10">
              <ActivityIndicator size="large" color="#159AFF" />
            </View>
          ) : wishlistItems.length === 0 ? (
            <View className="flex-1 justify-center items-center py-10">
              <Text className="text-gray-500 text-center">
                Your wishlist is empty. Add services to your wishlist to see them here.
              </Text>
            </View>
          ) : (
            <FlatList
              data={wishlistItems}
              renderItem={renderItem}
              keyExtractor={(item) => item._id}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ paddingBottom: 20 }}
              scrollEnabled={false}
            />
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default WishlistScreen;
