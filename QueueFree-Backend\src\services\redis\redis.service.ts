import { Injectable } from '@nestjs/common';
import { Redis } from '@upstash/redis';

@Injectable()
export class RedisService {
  private redis: Redis;
  
  constructor() {
    // Get Redis configuration from environment variables
    const redisUrl = process.env.REDIS_URL || '';
    const redisToken = process.env.REDIS_TOKEN || '';
    
    if (!redisUrl || !redisToken) {
      throw new Error('Redis configuration missing. Please set REDIS_URL and REDIS_TOKEN environment variables.');
    }

    this.redis = new Redis({
      url: redisUrl,
      token: redisToken,
    });
  }
  
  // General get method for any key
  async get(key: string): Promise<any> {
    return this.redis.get(key);
  }
  
  // General set method for any key
  async set(key: string, value: any, options?: { ex?: number }): Promise<void> {
    if (options?.ex) {
      await this.redis.set(key, value, { ex: options.ex });
    } else {
      await this.redis.set(key, value);
    }
  }
  
  // New queue key format: queue:{queueId}
  // This is the only format we'll use going forward
  async getQueue(queueId: number | string): Promise<any> {
    const queueKey = `queue:${queueId}`;
    return this.redis.get(queueKey);
  }
  
  async saveQueue(queueId: number | string, queueData: any, ttlSeconds: number = 86400): Promise<void> {
    // Clean up any old format keys that might exist
    await this.cleanupOldQueueKeys(queueId);
    
    // Use the simple queue key format
    const queueKey = `queue:${queueId}`;
    
    // Set TTL based on status
    const status = queueData.status || 'waiting';
    let ttl = ttlSeconds;
    
    // Use longer TTL for final statuses
    if (status === 'no-show' || status === 'completed' || status === 'cancelled') {
      ttl = 86400 * 7; // 7 days for final statuses
    }
    
    // Save to the queue key
    await this.redis.set(queueKey, queueData, { ex: ttl });
    
    console.log(`Redis: Saved queue ${queueId} with status ${status} to key ${queueKey}`);
  }
  
  // Helper method to clean up old format keys
  private async cleanupOldQueueKeys(queueId: number | string): Promise<void> {
    try {
      // Find all status-specific keys for this queue
      const statusKeys = await this.redis.keys(`queue:${queueId}:status:*`);
      
      if (statusKeys && statusKeys.length > 0) {
        console.log(`Redis: Found ${statusKeys.length} old format keys for queue ${queueId}. Cleaning up...`);
        
        // Delete all status-specific keys
        for (const key of statusKeys) {
          await this.redis.del(key);
          console.log(`Redis: Deleted old format key: ${key}`);
        }
      }
    } catch (error) {
      console.error(`Redis: Error cleaning up old format keys for queue ${queueId}:`, error);
    }
  }
  
  async updateQueueStatus(queueId: number | string, status: string, isPriority: boolean = false): Promise<void> {
    // Clean up any old format keys first
    await this.cleanupOldQueueKeys(queueId);
    
    // Get existing data from the queue key
    const queueKey = `queue:${queueId}`;
    let existingData = await this.redis.get(queueKey);
    
    if (existingData) {
      // Make sure we preserve the isCheckedIn property exactly as it was
      const existingDataObj = existingData as Record<string, any>;
      const isCheckedIn = existingDataObj.isCheckedIn === true;
      const previousStatus = existingDataObj.status || 'unknown';
      const finalStatusAlreadySet = existingDataObj.finalStatusSet === true;
      
      // IMPORTANT: Prevent overwriting final statuses with non-final statuses
      // If the queue already has a final status (no-show, completed, cancelled)
      // and we're trying to set it to a non-final status (waiting, serving, active),
      // then we should not update the status unless isPriority is true
      if (finalStatusAlreadySet && 
          (previousStatus === 'no-show' || previousStatus === 'completed' || previousStatus === 'cancelled') &&
          (status === 'waiting' || status === 'serving' || status === 'active') &&
          !isPriority) {
        console.log(`Redis: Prevented overwriting final status ${previousStatus} with ${status} for queue ${queueId}`);
        return; // Exit early without updating
      }
      
      // Create a completely new object with the status explicitly set
      const updatedData: any = {
        ...JSON.parse(JSON.stringify(existingData)), // Deep clone to avoid reference issues
        status: status, // Explicitly set status
        isCheckedIn: isCheckedIn, // Explicitly include isCheckedIn
        statusLastUpdatedAt: new Date().toISOString(), // Add timestamp
        statusUpdatedAt: new Date().toISOString(), // For backward compatibility
        finalStatusSet: status === 'no-show' || status === 'completed' || status === 'cancelled', // Mark final statuses
        previousStatus: previousStatus // Track previous status
      };
      
      // Log the update for debugging
      console.log(`Redis: Updating queue ${queueId} status from ${previousStatus} to ${status}, isCheckedIn: ${isCheckedIn}`);
      
      // Set TTL based on status type
      let ttl = 86400; // 24 hours default
      if (status === 'no-show' || status === 'completed' || status === 'cancelled') {
        ttl = 86400 * 7; // 7 days for final statuses
      } else if (status === 'serving') {
        // For serving status, use a fixed 24 hour TTL regardless of priority to ensure visibility
        ttl = 86400; // 24 hours for serving status
      } else if (status === 'waiting') {
        ttl = isPriority ? 3600 : 21600; // 1 hour for priority, 6 hours for normal waiting state
      }
      
      // Save the updated data to the queue key
      await this.redis.set(queueKey, updatedData, { ex: ttl });
      
      // Verify the update was successful
      try {
        const checkData = await this.redis.get(queueKey);
        if (checkData) {
          const checkObj = checkData as Record<string, any>;
          console.log(`Redis: Verified queue ${queueId} now has status: ${checkObj.status}`);
          
          // If status doesn't match, force update one more time
          if (checkObj.status !== status) {
            console.warn(`Redis: Status mismatch detected! Force updating queue ${queueId}`);
            updatedData.status = status; // Ensure status is correct
            updatedData.statusLastUpdatedAt = new Date().toISOString();
            updatedData.forceUpdated = true;
            
            await this.redis.set(queueKey, updatedData, { ex: ttl });
            console.log(`Redis: Force-updated queue ${queueId} with status ${status}`);
          }
        }
      } catch (error) {
        console.error(`Redis: Error verifying status update for queue ${queueId}:`, error);
      }
    } else {
      console.error(`Redis: No existing data found for queue ${queueId}, cannot update status`);
    }
  }
  
  async updateQueueCheckInStatus(queueId: number | string, isCheckedIn: boolean, isPriority: boolean = false): Promise<void> {
    const redisKey = `queue:${queueId}`;
    const existingData = await this.redis.get(redisKey);
    
    if (existingData) {
      // Only update isCheckedIn, don't change status
      const updatedData: any = {
        ...existingData,
        isCheckedIn,
        checkInUpdatedAt: new Date().toISOString() // Add timestamp for check-in updates
      };
      
      // Use a longer TTL - check-in status should persist
      const ttl = isPriority ? 3600 : 21600; // 1 hour for priority, 6 hours for normal
      await this.redis.set(redisKey, updatedData, { ex: ttl });
    }
  }
  
  async deleteQueue(queueId: number | string): Promise<void> {
    // Clean up any old format keys
    await this.cleanupOldQueueKeys(queueId);
    
    // Delete the main queue key
    const queueKey = `queue:${queueId}`;
    await this.redis.del(queueKey);
    console.log(`Redis: Deleted queue key ${queueKey}`);
  }
  
  async saveQueuePosition(
    serviceId: number | string, 
    date: string, 
    timeSlot: string, 
    positionData: any
  ): Promise<void> {
    const redisKey = `service:${serviceId}:position:${date}:${timeSlot}`;
    await this.redis.set(redisKey, positionData, { ex: 600 }); // 10 minutes
  }
  
  async getQueuePosition(
    serviceId: number | string, 
    date: string, 
    timeSlot: string
  ): Promise<any> {
    const redisKey = `service:${serviceId}:position:${date}:${timeSlot}`;
    return this.redis.get(redisKey);
  }
  
  async saveServiceQueues(
    serviceId: number | string,
    date: string,
    queuesData: any
  ): Promise<void> {
    const redisKey = `service:${serviceId}:queues:${date}`;
    await this.redis.set(redisKey, queuesData, { ex: 300 });
  }
  
  async getServiceQueues(
    serviceId: number | string,
    date: string
  ): Promise<any> {
    const redisKey = `service:${serviceId}:queues:${date}`;
    return this.redis.get(redisKey);
  }
  
  async invalidateServiceQueues(serviceId: number | string, date: string): Promise<void> {
    const redisKey = `service:${serviceId}:queues:${date}`;
    await this.redis.del(redisKey);
  }
  
  // General delete method for any key
  async del(key: string): Promise<void> {
    await this.redis.del(key);
  }
  
  // Get all keys matching a pattern
  async getKeys(pattern: string): Promise<string[]> {
    return this.redis.keys(pattern);
  }
  
  // Get multiple values by keys
  async mget(...keys: string[]): Promise<any[]> {
    return this.redis.mget(...keys);
  }
  
  async invalidateQueuePosition(
    serviceId: number | string,
    date: string,
    timeSlot: string
  ): Promise<void> {
    const redisKey = `service:${serviceId}:position:${date}:${timeSlot}`;
    await this.redis.del(redisKey);
    console.log(`Invalidated queue position cache for ${redisKey}`);
  }
  
  // Method to migrate all existing keys to the new format
  async migrateAllQueueKeys(): Promise<void> {
    try {
      console.log('Starting migration of all queue keys to new format...');
      
      // Find all status-specific keys
      const statusKeys = await this.redis.keys('queue:*:status:*');
      console.log(`Found ${statusKeys.length} status-specific keys to migrate`);
      
      // Process each key
      for (const statusKey of statusKeys) {
        try {
          // Extract queue ID from the key
          const match = statusKey.match(/queue:(\d+):status:/);
          if (match && match[1]) {
            const queueId = match[1];
            const data = await this.redis.get(statusKey);
            
            if (data) {
              // Create the new format key
              const newKey = `queue:${queueId}`;
              console.log(`Migrating ${statusKey} to ${newKey}`);
              
              // Set TTL based on status
              let ttl = 86400; // 24 hours default
              const dataObj = data as Record<string, any>;
              if (dataObj.status === 'no-show' || dataObj.status === 'completed' || dataObj.status === 'cancelled') {
                ttl = 86400 * 7; // 7 days for final statuses
              }
              
              // Save to the new key
              await this.redis.set(newKey, data, { ex: ttl });
              
              // Delete the old key
              await this.redis.del(statusKey);
            }
          }
        } catch (error) {
          console.error(`Error migrating key ${statusKey}:`, error);
        }
      }
      
      console.log('Migration of queue keys completed');
    } catch (error) {
      console.error('Error during queue key migration:', error);
    }
  }
  
  /**
   * Automatically refresh TTL for all queues with serving status
   * This prevents serving queues from disappearing from Redis due to TTL expiration
   */
  async refreshServingQueuesTTL(): Promise<void> {
    try {
      // Find all keys for queues
      const queueKeys = await this.redis.keys('queue:*');
      
      if (!queueKeys || queueKeys.length === 0) {
        console.log('Redis: No queue keys found to refresh');
        return;
      }
      
      console.log(`Redis: Found ${queueKeys.length} queue keys, checking for serving status`);
      
      // Get all queue data in one call
      const queuesData = await this.mget(...queueKeys);
      let refreshedCount = 0;
      
      // Process each queue
      for (let i = 0; i < queueKeys.length; i++) {
        const key = queueKeys[i];
        const data = queuesData[i];
        
        // Skip if no data
        if (!data) continue;
        
        // Check if this is a serving queue
        if (data.status === 'serving') {
          // Refresh TTL without changing data
          await this.redis.expire(key, 86400); // 24 hours
          refreshedCount++;
          console.log(`Redis: Refreshed TTL for serving queue ${key}`);
        }
      }
      
      console.log(`Redis: Refreshed TTL for ${refreshedCount} serving queues`);
    } catch (error) {
      console.error('Redis: Error refreshing serving queues TTL:', error);
    }
  }
} 