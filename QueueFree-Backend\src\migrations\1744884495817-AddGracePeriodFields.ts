import { MigrationInterface, QueryRunner } from "typeorm";

export class AddGracePeriodFields1744884495817 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add grace period configuration to service_setups table
        await queryRunner.query(`
            ALTER TABLE "service_setups" 
            ADD COLUMN IF NOT EXISTS "graceTime" integer DEFAULT 120
        `);

        // Add fields to queues table for grace period handling
        await queryRunner.query(`
            ALTER TABLE "queues" 
            ADD COLUMN IF NOT EXISTS "graceStartedAt" TIMESTAMP WITH TIME ZONE,
            ADD COLUMN IF NOT EXISTS "confirmedPresence" boolean DEFAULT false,
            ADD COLUMN IF NOT EXISTS "inGracePeriod" boolean DEFAULT false,
            ADD COLUMN IF NOT EXISTS "currentlyServing" boolean DEFAULT false,
            ADD COLUMN IF NOT EXISTS "servingStartedAt" TIMESTAMP WITH TIME ZONE,
            ADD COLUMN IF NOT EXISTS "statusUpdatedAt" TIMESTAMP WITH TIME ZONE
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove grace period configuration from service_setups table
        await queryRunner.query(`
            ALTER TABLE "service_setups" 
            DROP COLUMN IF EXISTS "graceTime"
        `);

        // Remove fields from queues table
        await queryRunner.query(`
            ALTER TABLE "queues" 
            DROP COLUMN IF EXISTS "graceStartedAt",
            DROP COLUMN IF EXISTS "confirmedPresence",
            DROP COLUMN IF EXISTS "inGracePeriod",
            DROP COLUMN IF EXISTS "currentlyServing",
            DROP COLUMN IF EXISTS "servingStartedAt",
            DROP COLUMN IF EXISTS "statusUpdatedAt"
        `);
    }

}
