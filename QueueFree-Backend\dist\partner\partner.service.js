"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartnerService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const service_entity_1 = require("./entities/service.entity");
const service_setup_entity_1 = require("./entities/service-setup.entity");
const bank_details_entity_1 = require("./entities/bank-details.entity");
const queue_entity_1 = require("./entities/queue.entity");
const upload_service_1 = require("../services/upload.service");
const review_entity_1 = require("./entities/review.entity");
const redis_service_1 = require("../services/redis/redis.service");
const queue_flow_service_1 = require("../services/queue-flow/queue-flow.service");
let PartnerService = class PartnerService {
    constructor(serviceRepository, setupRepository, bankDetailsRepository, queueRepository, reviewRepository, uploadService, redisService, queueFlowService) {
        this.serviceRepository = serviceRepository;
        this.setupRepository = setupRepository;
        this.bankDetailsRepository = bankDetailsRepository;
        this.queueRepository = queueRepository;
        this.reviewRepository = reviewRepository;
        this.uploadService = uploadService;
        this.redisService = redisService;
        this.queueFlowService = queueFlowService;
    }
    async createPartner(email) {
        if (!email) {
            throw new common_1.BadRequestException('Email is required');
        }
        try {
            console.log('Checking for existing partner:', email);
            let service = await this.serviceRepository.findOne({ where: { email } });
            if (service) {
                console.log('Partner already exists:', email);
                return {
                    status: 'success',
                    message: 'Partner already registered',
                    isExisting: true,
                    serviceId: service.id
                };
            }
            console.log('Creating new service entry for partner:', email);
            service = this.serviceRepository.create({ email });
            const savedService = await this.serviceRepository.save(service);
            console.log('New service entry created for partner:', savedService);
            return {
                status: 'success',
                message: 'Partner registered successfully',
                isExisting: false,
                serviceId: savedService.id
            };
        }
        catch (error) {
            console.error('Error in createPartner:', error);
            throw new common_1.BadRequestException('Failed to process partner registration: ' + error.message);
        }
    }
    async registerService(serviceData) {
        try {
            const existingService = await this.serviceRepository.findOne({
                where: { email: serviceData.email }
            });
            if (!existingService) {
                throw new common_1.NotFoundException('Service not found. Please register first.');
            }
            const uploadedImageUrls = await this.uploadService.uploadMultipleImages(serviceData.images);
            let panCardImageUrl;
            if (serviceData.documents?.panCardImage?.base64) {
                panCardImageUrl = await this.uploadService.uploadImage(serviceData.documents.panCardImage, `documents/pan/${Date.now()}-${existingService.id}.jpg`);
            }
            else {
                throw new common_1.BadRequestException('PAN card image data is missing or invalid');
            }
            existingService.serviceName = serviceData.serviceName.trim();
            existingService.serviceType = serviceData.serviceType;
            existingService.businessPhone = serviceData.businessPhone?.trim();
            existingService.serviceDescription = serviceData.serviceDescription?.trim();
            existingService.address = serviceData.address;
            existingService.images = uploadedImageUrls;
            existingService.documents = {
                panNumber: serviceData.documents.panNumber,
                gstin: serviceData.documents.gstin,
                panCardImage: panCardImageUrl
            };
            existingService.verificationStatus = 'pending';
            const savedService = await this.serviceRepository.save(existingService);
            return {
                serviceId: savedService.id,
                status: 'success'
            };
        }
        catch (error) {
            console.error('Error in registerService:', error);
            throw new common_1.BadRequestException(error.message || 'Failed to register service');
        }
    }
    async updateService(serviceData) {
        try {
            const existingService = await this.serviceRepository.findOne({
                where: { email: serviceData.email }
            });
            if (!existingService) {
                throw new common_1.NotFoundException('Service not found');
            }
            existingService.serviceName = serviceData.serviceName;
            existingService.serviceType = serviceData.serviceType;
            existingService.businessPhone = serviceData.businessPhone;
            existingService.serviceDescription = serviceData.serviceDescription;
            existingService.address = serviceData.address;
            if (serviceData.images?.length) {
                const newImages = serviceData.images.filter((img) => img.base64);
                if (newImages.length) {
                    const uploadedUrls = await this.uploadService.uploadMultipleImages(newImages);
                    const oldImages = serviceData.images
                        .filter((img) => !img.base64)
                        .map((img) => img.uri);
                    existingService.images = [...oldImages, ...uploadedUrls];
                }
            }
            const savedService = await this.serviceRepository.save(existingService);
            return {
                status: 'success',
                message: 'Service updated successfully',
                service: savedService
            };
        }
        catch (error) {
            console.error('Error in updateService:', error);
            throw new common_1.BadRequestException(error.message || 'Failed to update service');
        }
    }
    async getServiceStatus(serviceId) {
        try {
            if (!serviceId || isNaN(serviceId)) {
                throw new common_1.BadRequestException('Invalid service ID');
            }
            const service = await this.serviceRepository.findOne({
                where: { id: serviceId },
                select: ['verificationStatus']
            });
            if (!service) {
                throw new common_1.NotFoundException('Service not found');
            }
            return {
                success: true,
                status: service.verificationStatus || 'pending'
            };
        }
        catch (error) {
            console.error('Error getting service status:', error);
            throw error;
        }
    }
    async updateServiceStatus(serviceId, status) {
        const service = await this.serviceRepository.findOne({
            where: { id: serviceId }
        });
        if (!service) {
            throw new common_1.NotFoundException('Service not found');
        }
        service.verificationStatus = status;
        await this.serviceRepository.save(service);
        return {
            status: 'success',
            message: 'Service status updated'
        };
    }
    async saveServiceSetup(serviceId, data) {
        console.log('Received setup request:', { serviceId, data });
        if (!serviceId || !data?.setupData) {
            throw new common_1.BadRequestException('Invalid request: missing serviceId or setupData');
        }
        const service = await this.serviceRepository.findOne({
            where: { id: serviceId },
            relations: ['setup'],
            select: ['id', 'verificationStatus', 'termsAccepted']
        });
        if (!service) {
            throw new common_1.NotFoundException(`Service not found with ID: ${serviceId}`);
        }
        if (service.verificationStatus !== 'success') {
            throw new common_1.BadRequestException('Service must be verified before saving setup');
        }
        try {
            const setupData = data.setupData;
            console.log('Service setup configuration flags:', {
                hasSubUnits: setupData.hasSubUnits === true ? 'Using sub-units' : 'Using regular service',
                useDayWiseTimeSlots: setupData.useDayWiseTimeSlots === true ? 'Using day-wise time slots' : 'Using regular time slots'
            });
            if (!this.validateSetupData(setupData)) {
                throw new common_1.BadRequestException('Invalid setup data structure');
            }
            console.log('Cleaned setup data after validation:', {
                hasSubUnits: setupData.hasSubUnits,
                useDayWiseTimeSlots: setupData.useDayWiseTimeSlots,
                hasSelectedDays: Array.isArray(setupData.selectedDays),
                hasTimeSlots: Array.isArray(setupData.timeSlots),
                hasTimeSlotsByDay: setupData.timeSlotsByDay ? 'Present' : 'Not present',
                hasSubUnitsArray: Array.isArray(setupData.subUnits),
                subUnitsCount: Array.isArray(setupData.subUnits) ? setupData.subUnits.length : 0,
                subUnitsWithDayWiseTimeSlots: Array.isArray(setupData.subUnits) ?
                    setupData.subUnits.map(unit => ({
                        name: unit.name,
                        useDayWiseTimeSlots: unit.useDayWiseTimeSlots
                    })) : []
            });
            let setup = service.setup;
            if (!setup) {
                setup = this.setupRepository.create({
                    service,
                    setupData
                });
            }
            else {
                const previousHasSubUnits = setup.setupData?.hasSubUnits === true;
                const newHasSubUnits = setupData.hasSubUnits === true;
                if (previousHasSubUnits !== newHasSubUnits) {
                    console.log(`Service type changed from ${previousHasSubUnits ? 'subunits' : 'regular'} to ${newHasSubUnits ? 'subunits' : 'regular'}, resetting leave days`);
                    if (newHasSubUnits) {
                        setup.leaveDays = {
                            main: [],
                            subUnits: {}
                        };
                    }
                    else {
                        setup.leaveDays = {
                            main: [],
                            subUnits: {}
                        };
                    }
                }
                setup.setupData = setupData;
            }
            setup = await this.setupRepository.save(setup);
            console.log('Setup saved successfully for service:', serviceId);
            return {
                status: 'success',
                message: 'Service setup data saved successfully',
                data: setup.setupData,
                termsAccepted: service.termsAccepted,
                hasSetup: true
            };
        }
        catch (error) {
            console.error('Error in saveServiceSetup:', error);
            throw new common_1.BadRequestException(error.message || 'Failed to save service setup data');
        }
    }
    validateSetupData(setupData) {
        if (!setupData)
            return false;
        console.log('Validating setup data with flags:', {
            hasSubUnits: setupData.hasSubUnits,
            useDayWiseTimeSlots: setupData.useDayWiseTimeSlots
        });
        if (setupData.hasSubUnits === true) {
            console.log('Processing sub-units configuration');
            if (!Array.isArray(setupData.subUnits) || setupData.subUnits.length === 0) {
                console.log('Invalid subUnits: must be an array with at least one entry');
                return false;
            }
            for (const unit of setupData.subUnits) {
                if (!unit.name || !unit.avgServeTime || !unit.pricePerHead) {
                    console.log('Invalid subUnit: missing required fields');
                    return false;
                }
                if (!Array.isArray(unit.selectedDays) || unit.selectedDays.length === 0) {
                    console.log('Invalid subUnit: missing selectedDays');
                    return false;
                }
                if (!unit.availableHours || typeof unit.availableHours !== 'object') {
                    console.log('Invalid subUnit: missing availableHours');
                    return false;
                }
                for (const day of unit.selectedDays) {
                    if (!unit.availableHours[day] || !Array.isArray(unit.availableHours[day]) || unit.availableHours[day].length === 0) {
                        console.log(`Invalid availableHours: missing or empty time slots for ${day}`);
                        return false;
                    }
                }
                delete unit.timeSlots;
                delete unit.dayWiseAvailableHours;
                unit.useDayWiseTimeSlots = unit.useDayWiseTimeSlots === true;
            }
            delete setupData.selectedDays;
            delete setupData.timeSlots;
            delete setupData.servingTime;
            delete setupData.basePrice;
            delete setupData.timeSlotsByDay;
            delete setupData.useDayWiseTimeSlots;
            delete setupData.availableHours;
            return true;
        }
        else {
            console.log('Processing regular service configuration');
            delete setupData.subUnits;
            const requiredFields = ['selectedDays', 'servingTime', 'basePrice'];
            for (const field of requiredFields) {
                if (!setupData[field]) {
                    console.log(`Missing required field: ${field}`);
                    return false;
                }
            }
            if (!Array.isArray(setupData.selectedDays) || setupData.selectedDays.length === 0) {
                console.log('Invalid selectedDays: must be a non-empty array');
                return false;
            }
            if (!setupData.availableHours || typeof setupData.availableHours !== 'object') {
                console.log('Invalid setup: missing availableHours object');
                return false;
            }
            for (const day of setupData.selectedDays) {
                if (!setupData.availableHours[day] || !Array.isArray(setupData.availableHours[day]) || setupData.availableHours[day].length === 0) {
                    console.log(`Invalid availableHours: missing or empty time slots for ${day}`);
                    return false;
                }
            }
            delete setupData.timeSlots;
            delete setupData.timeSlotsByDay;
            setupData.useDayWiseTimeSlots = setupData.useDayWiseTimeSlots === true;
            return true;
        }
    }
    async getServiceSetup(serviceId) {
        try {
            if (!serviceId || isNaN(serviceId)) {
                throw new common_1.BadRequestException('Invalid service ID');
            }
            const service = await this.serviceRepository.findOne({
                where: { id: serviceId },
                relations: ['setup']
            });
            if (!service) {
                throw new common_1.NotFoundException(`Service not found for ID: ${serviceId}`);
            }
            if (!service.setup) {
                return {
                    status: 'success',
                    message: 'No setup data found',
                    hasSetup: false,
                    data: null
                };
            }
            return {
                status: 'success',
                message: 'Service setup data retrieved',
                hasSetup: true,
                data: service.setup.setupData
            };
        }
        catch (error) {
            console.error('Error getting service setup:', error);
            throw error;
        }
    }
    async getServiceDetailsByEmail(email) {
        try {
            const service = await this.serviceRepository.findOne({
                where: { email },
                select: [
                    'id',
                    'serviceName',
                    'termsAccepted',
                    'serviceType',
                    'businessPhone',
                    'serviceDescription',
                    'address',
                    'images',
                    'verificationStatus',
                    'documents',
                    'isOpen'
                ]
            });
            if (!service) {
                return {
                    id: null,
                    serviceName: '',
                    termsAccepted: false,
                    serviceType: '',
                    businessPhone: '',
                    serviceDescription: '',
                    address: null,
                    images: [],
                    verificationStatus: 'pending',
                    documents: null
                };
            }
            if (!service.address) {
                service.address = {
                    details: {
                        buildingNo: '',
                        locality: '',
                        city: '',
                        state: 'Select State',
                        pincode: ''
                    },
                    coordinates: {
                        latitude: 0,
                        longitude: 0
                    }
                };
            }
            return service;
        }
        catch (error) {
            console.error('Error getting service details:', error);
            throw new common_1.BadRequestException('Failed to get service details');
        }
    }
    async checkServiceCompletion(email) {
        try {
            const service = await this.serviceRepository.findOne({
                where: { email },
                relations: ['setup'],
                select: [
                    'id',
                    'serviceName',
                    'serviceType',
                    'businessPhone',
                    'serviceDescription',
                    'address',
                    'images',
                    'verificationStatus',
                    'termsAccepted',
                    'documents'
                ]
            });
            if (!service) {
                return {
                    exists: false,
                    isVerified: false,
                    isDetailsComplete: false,
                    hasSetup: false,
                    verificationStatus: null,
                    termsAccepted: false,
                    serviceId: null,
                    setupComplete: false
                };
            }
            const isDetailsComplete = Boolean(service.serviceName &&
                service.serviceType &&
                service.businessPhone &&
                service.serviceDescription &&
                service.images?.length > 0 &&
                service.address);
            return {
                exists: true,
                isVerified: service.verificationStatus === 'success',
                isDetailsComplete,
                hasSetup: !!service.setup,
                verificationStatus: service.verificationStatus,
                termsAccepted: service.termsAccepted,
                serviceId: service.id,
                setupComplete: !!service.setup,
                serviceDetails: {
                    serviceName: service.serviceName || '',
                    serviceType: service.serviceType || '',
                    businessPhone: service.businessPhone || '',
                    serviceDescription: service.serviceDescription || '',
                    address: service.address || null,
                    images: service.images || [],
                    documents: service.documents || null
                }
            };
        }
        catch (error) {
            console.error('Error checking service completion:', error);
            throw new common_1.BadRequestException('Failed to check service completion status');
        }
    }
    async acceptTerms(email) {
        try {
            const service = await this.serviceRepository.findOne({
                where: { email }
            });
            if (!service) {
                throw new common_1.NotFoundException('Service not found');
            }
            service.termsAccepted = true;
            await this.serviceRepository.save(service);
            return {
                status: 'success',
                message: 'Terms accepted successfully'
            };
        }
        catch (error) {
            console.error('Error accepting terms:', error);
            throw new common_1.BadRequestException(error.message || 'Failed to accept terms');
        }
    }
    async getVerificationStatusByEmail(email) {
        const service = await this.serviceRepository.findOne({
            where: { email },
            select: ['verificationStatus']
        });
        if (!service) {
            throw new common_1.NotFoundException('Service not found');
        }
        return service;
    }
    async toggleServiceStatus(email, isOpen) {
        try {
            const service = await this.serviceRepository.findOne({ where: { email } });
            if (!service) {
                throw new common_1.NotFoundException('Service not found');
            }
            service.isOpen = isOpen;
            await this.serviceRepository.save(service);
            return {
                status: 'success',
                message: `Service ${isOpen ? 'opened' : 'closed'} successfully`,
                isOpen
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async getServiceOpenStatus(email) {
        const service = await this.serviceRepository.findOne({
            where: { email },
            select: ['isOpen']
        });
        if (!service) {
            throw new common_1.NotFoundException('Service not found');
        }
        return service;
    }
    async saveBankDetails(serviceId, data) {
        try {
            let service = await this.serviceRepository.findOne({
                where: { id: serviceId },
                relations: ['bankDetails']
            });
            if (!service) {
                throw new common_1.NotFoundException('Service not found');
            }
            if (!service.bankDetails) {
                const bankDetails = new bank_details_entity_1.BankDetails();
                Object.assign(bankDetails, {
                    service,
                    ...data
                });
                service.bankDetails = bankDetails;
            }
            else {
                Object.assign(service.bankDetails, data);
            }
            await this.bankDetailsRepository.save(service.bankDetails);
            return {
                status: 'success',
                message: 'Bank details saved successfully'
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async getBankDetails(serviceId) {
        const service = await this.serviceRepository.findOne({
            where: { id: serviceId },
            relations: ['bankDetails']
        });
        if (!service) {
            throw new common_1.NotFoundException('Service not found');
        }
        return {
            status: 'success',
            data: service.bankDetails
        };
    }
    async saveLeaveDays(serviceId, leaveDays) {
        try {
            const service = await this.serviceRepository.findOne({
                where: { id: serviceId },
                relations: ['setup']
            });
            if (!service || !service.setup) {
                throw new common_1.NotFoundException('Service or setup not found');
            }
            const hasSubUnits = service.setup.setupData?.hasSubUnits === true;
            let formattedLeaveDays = {
                main: [],
                subUnits: {}
            };
            if (hasSubUnits) {
                if (typeof leaveDays === 'object' && leaveDays !== null) {
                    if (leaveDays.subUnits && typeof leaveDays.subUnits === 'object') {
                        Object.keys(leaveDays.subUnits).forEach(key => {
                            if (!Array.isArray(leaveDays.subUnits[key])) {
                                leaveDays.subUnits[key] = [];
                            }
                        });
                        formattedLeaveDays.subUnits = leaveDays.subUnits;
                        formattedLeaveDays.main = [];
                    }
                }
            }
            else {
                if (typeof leaveDays === 'object' && leaveDays !== null) {
                    if (Array.isArray(leaveDays.main)) {
                        formattedLeaveDays.main = leaveDays.main;
                    }
                    else if (Array.isArray(leaveDays)) {
                        formattedLeaveDays.main = leaveDays;
                    }
                    formattedLeaveDays.subUnits = {};
                }
            }
            service.setup.leaveDays = formattedLeaveDays;
            await this.setupRepository.save(service.setup);
            return {
                status: 'success',
                message: 'Leave days saved successfully'
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async getLeaveDays(serviceId) {
        try {
            const service = await this.serviceRepository.findOne({
                where: { id: serviceId },
                relations: ['setup']
            });
            if (!service || !service.setup) {
                throw new common_1.NotFoundException('Service or setup not found');
            }
            const hasSubUnits = service.setup.setupData?.hasSubUnits === true;
            let leaveDays = service.setup.leaveDays;
            if (!leaveDays || typeof leaveDays !== 'object') {
                leaveDays = { main: [], subUnits: {} };
            }
            if (!Array.isArray(leaveDays.main)) {
                leaveDays.main = [];
            }
            if (!leaveDays.subUnits || typeof leaveDays.subUnits !== 'object') {
                leaveDays.subUnits = {};
            }
            if (hasSubUnits) {
                return {
                    status: 'success',
                    data: { subUnits: leaveDays.subUnits },
                    hasSubUnits: true
                };
            }
            else {
                return {
                    status: 'success',
                    data: leaveDays.main,
                    hasSubUnits: false
                };
            }
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async getAllServices() {
        try {
            const services = await this.serviceRepository.find({
                where: { verificationStatus: 'success' },
                relations: ['setup', 'queues'],
                select: [
                    'id',
                    'serviceName',
                    'serviceType',
                    'address',
                    'images',
                    'isOpen',
                    'rating',
                    'reviewCount',
                    'serviceDescription'
                ]
            });
            console.log('Services coordinates check:', services.map(s => ({
                id: s.id,
                coordinates: s.address?.coordinates
            })));
            const servicesWithQueueInfo = await Promise.all(services.map(async (service) => {
                if (!service.address?.coordinates?.latitude || !service.address?.coordinates?.longitude) {
                    console.warn(`Missing coordinates for service ${service.id}`);
                }
                const activeQueues = service.queues.filter(q => q.status === 'waiting' || q.status === 'serving');
                const queueCount = activeQueues.length;
                const vipCount = activeQueues.filter(q => q.isVIP).length;
                const normalCount = queueCount - vipCount;
                const servingTime = parseInt(service.setup?.setupData?.servingTime || '30');
                const waitingTime = queueCount * servingTime;
                const basePrice = parseInt(service.setup?.setupData?.basePrice || '50');
                const firstImage = Array.isArray(service.images) && service.images.length > 0
                    ? service.images[0]
                    : null;
                return {
                    _id: service.id.toString(),
                    serviceName: service.serviceName,
                    serviceType: service.serviceType,
                    address: service.address,
                    image: firstImage,
                    isOpen: service.isOpen,
                    rating: service.rating || 0,
                    reviewCount: service.reviewCount || 0,
                    serviceDescription: service.serviceDescription || '',
                    queueInfo: {
                        waitingTime,
                        membersInQueue: queueCount,
                        vipCount,
                        normalCount,
                        cost: basePrice,
                        servingTime
                    }
                };
            }));
            return servicesWithQueueInfo;
        }
        catch (error) {
            console.error('Error fetching services:', error);
            throw new common_1.BadRequestException('Failed to fetch services');
        }
    }
    async getServiceById(id) {
        try {
            const service = await this.serviceRepository.findOne({
                where: { id },
                relations: ['setup', 'queues'],
                select: [
                    'id',
                    'serviceName',
                    'serviceType',
                    'address',
                    'images',
                    'isOpen',
                    'rating',
                    'reviewCount',
                    'reviews',
                    'serviceDescription',
                    'businessPhone',
                    'email'
                ]
            });
            if (!service) {
                throw new common_1.NotFoundException('Service not found');
            }
            const activeQueues = service.queues.filter(q => q.status === 'waiting' || q.status === 'serving');
            const queueCount = activeQueues.length;
            const vipCount = activeQueues.filter(q => q.isVIP).length;
            const normalCount = queueCount - vipCount;
            const servingTime = parseInt(service.setup?.setupData?.servingTime || '30');
            const basePrice = parseInt(service.setup?.setupData?.basePrice || '50');
            const setupData = service.setup?.setupData || {};
            const timeSlots = setupData.timeSlots || [];
            const selectedDays = setupData.selectedDays || [];
            const workingHours = timeSlots.length > 0 ? {
                startTime: timeSlots[0].start,
                endTime: timeSlots[timeSlots.length - 1].end
            } : null;
            const waitingTime = queueCount * servingTime;
            const formattedImages = Array.isArray(service.images) ? service.images.filter(img => typeof img === 'string' && img.trim() !== '') : [];
            return {
                _id: service.id.toString(),
                serviceName: service.serviceName,
                serviceType: service.serviceType,
                address: service.address,
                images: formattedImages,
                isOpen: service.isOpen,
                rating: service.rating || 0,
                reviewCount: service.reviewCount || 0,
                reviews: service.reviews || [],
                serviceDescription: service.serviceDescription || '',
                businessPhone: service.businessPhone || '',
                email: service.email || '',
                workingHours,
                selectedDays,
                queueInfo: {
                    waitingTime,
                    membersInQueue: queueCount,
                    vipCount,
                    normalCount,
                    cost: basePrice,
                    servingTime
                }
            };
        }
        catch (error) {
            console.error('Error fetching service details:', error);
            throw error;
        }
    }
    async getServiceReviews(serviceId, limit = 10, offset = 0) {
        try {
            const service = await this.serviceRepository.findOne({
                where: { id: serviceId },
            });
            if (!service) {
                throw new common_1.NotFoundException(`Service with ID ${serviceId} not found`);
            }
            const totalCount = await this.reviewRepository.count({
                where: { serviceId },
            });
            const reviews = await this.reviewRepository.find({
                where: { serviceId },
                order: { createdAt: 'DESC' },
                take: limit,
                skip: offset,
            });
            const starDistribution = await this.reviewRepository
                .createQueryBuilder('review')
                .select('review.rating', 'rating')
                .addSelect('COUNT(*)', 'count')
                .where('review.serviceId = :serviceId', { serviceId })
                .groupBy('review.rating')
                .getRawMany();
            const formattedStarDistribution = {
                1: 0,
                2: 0,
                3: 0,
                4: 0,
                5: 0,
            };
            starDistribution.forEach((item) => {
                formattedStarDistribution[item.rating] = parseInt(item.count);
            });
            return {
                reviews,
                totalCount,
                starDistribution: formattedStarDistribution,
            };
        }
        catch (error) {
            console.error('Error fetching service reviews:', error);
            throw error;
        }
    }
    async addReview(serviceId, reviewData) {
        try {
            const service = await this.serviceRepository.findOne({
                where: { id: serviceId },
            });
            if (!service) {
                throw new common_1.NotFoundException(`Service with ID ${serviceId} not found`);
            }
            if (reviewData.rating < 1 || reviewData.rating > 5) {
                throw new common_1.BadRequestException('Rating must be between 1 and 5');
            }
            const newReview = this.reviewRepository.create({
                serviceId,
                userId: reviewData.userId,
                userName: reviewData.userName,
                userProfilePic: reviewData.userProfilePic,
                rating: reviewData.rating,
                comment: reviewData.comment,
            });
            await this.reviewRepository.save(newReview);
            const allReviews = await this.reviewRepository.find({
                where: { serviceId },
            });
            const totalRating = allReviews.reduce((sum, review) => sum + review.rating, 0);
            const averageRating = totalRating / allReviews.length;
            service.rating = parseFloat(averageRating.toFixed(1));
            service.reviewCount = allReviews.length;
            await this.serviceRepository.save(service);
            return newReview;
        }
        catch (error) {
            console.error('Error adding review:', error);
            throw error;
        }
    }
    async getServiceTimeSlots(serviceId, dateString, subUnitId) {
        try {
            const service = await this.serviceRepository.findOne({
                where: { id: serviceId },
                relations: ['setup', 'queues']
            });
            if (!service || !service.setup) {
                throw new common_1.NotFoundException('Service or setup not found');
            }
            const requestedDate = new Date(dateString);
            const dayOfWeek = requestedDate.getDay();
            const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
            const dayName = dayNames[dayOfWeek];
            const dayNameCapitalized = dayName.charAt(0).toUpperCase() + dayName.slice(1);
            const setupData = service.setup.setupData;
            console.log('Getting time slots with flags:', {
                hasSubUnits: setupData.hasSubUnits,
                date: dateString,
                dayName,
                subUnitId
            });
            if (setupData.hasSubUnits === true && Array.isArray(setupData.subUnits) && setupData.subUnits.length > 0) {
                console.log('Using sub-units configuration for time slots');
                let subUnitIndex = 0;
                if (subUnitId !== undefined && subUnitId >= 0 && subUnitId < setupData.subUnits.length) {
                    subUnitIndex = subUnitId;
                    console.log(`Using provided subUnitId: ${subUnitId}`);
                }
                else {
                    console.log(`Using default subUnitId: 0 (first subunit)`);
                }
                const subUnit = setupData.subUnits[subUnitIndex];
                const subUnitSelectedDays = subUnit.selectedDays?.map(day => day.toLowerCase()) || [];
                if (!subUnitSelectedDays.includes(dayName)) {
                    return {
                        status: 'error',
                        message: 'The requested date is not a working day for this service',
                        timeSlots: []
                    };
                }
                const leaveDays = service.setup.leaveDays || { main: [], subUnits: {} };
                const isSubunitLeaveDay = setupData.hasSubUnits &&
                    leaveDays.subUnits &&
                    leaveDays.subUnits[`subunit-${subUnitIndex}`] ?
                    leaveDays.subUnits[`subunit-${subUnitIndex}`].some(leaveDay => {
                        const leaveDateObj = new Date(leaveDay);
                        return leaveDateObj.toISOString().split('T')[0] === dateString;
                    }) :
                    false;
                if (isSubunitLeaveDay) {
                    return {
                        status: 'error',
                        message: 'The requested date is a leave day',
                        timeSlots: []
                    };
                }
                const subUnitDaySlots = subUnit.availableHours && subUnit.availableHours[dayNameCapitalized]
                    ? subUnit.availableHours[dayNameCapitalized]
                    : [];
                if (subUnitDaySlots.length === 0) {
                    return {
                        status: 'error',
                        message: 'No available time slots for this day',
                        timeSlots: []
                    };
                }
                const timeSlotsWithQueue = this.mapTimeSlotsWithQueues(subUnitDaySlots, service.queues, dateString, subUnitIndex);
                return {
                    status: 'success',
                    workingDay: true,
                    timeSlots: timeSlotsWithQueue
                };
            }
            else {
                console.log('Using regular service configuration for time slots');
                const selectedDays = setupData.selectedDays || [];
                const normalizedSelectedDays = selectedDays.map(day => day.toLowerCase());
                if (!normalizedSelectedDays.includes(dayName)) {
                    return {
                        status: 'error',
                        message: 'The requested date is not a working day',
                        timeSlots: []
                    };
                }
                const leaveDays = service.setup.leaveDays || { main: [], subUnits: {} };
                const isLeaveDay = leaveDays.main.some(leaveDay => {
                    const leaveDateObj = new Date(leaveDay);
                    return leaveDateObj.toISOString().split('T')[0] === dateString;
                });
                if (isLeaveDay) {
                    return {
                        status: 'error',
                        message: 'The requested date is a leave day',
                        timeSlots: []
                    };
                }
                const daySlots = setupData.availableHours && setupData.availableHours[dayNameCapitalized]
                    ? setupData.availableHours[dayNameCapitalized]
                    : [];
                if (daySlots.length === 0) {
                    let legacyTimeSlots = [];
                    if (setupData.timeSlotsByDay && setupData.timeSlotsByDay[dayNameCapitalized]) {
                        legacyTimeSlots = setupData.timeSlotsByDay[dayNameCapitalized];
                    }
                    else if (Array.isArray(setupData.timeSlots)) {
                        legacyTimeSlots = setupData.timeSlots;
                    }
                    if (legacyTimeSlots.length > 0) {
                        const timeSlotsWithQueue = this.mapTimeSlotsWithQueues(legacyTimeSlots, service.queues, dateString);
                        return {
                            status: 'success',
                            workingDay: true,
                            timeSlots: timeSlotsWithQueue,
                            isLegacy: true
                        };
                    }
                    return {
                        status: 'error',
                        message: 'No available time slots for this day',
                        timeSlots: []
                    };
                }
                const timeSlotsWithQueue = this.mapTimeSlotsWithQueues(daySlots, service.queues, dateString);
                return {
                    status: 'success',
                    workingDay: true,
                    timeSlots: timeSlotsWithQueue
                };
            }
        }
        catch (error) {
            console.error('Error in getServiceTimeSlots:', error);
            throw new common_1.BadRequestException(error.message || 'Failed to get time slots');
        }
    }
    mapTimeSlotsWithQueues(timeSlots, queues, dateString, subUnitId) {
        const queuesForDate = queues.filter(queue => {
            const queueDate = new Date(queue.date);
            const dateMatches = queueDate.toISOString().split('T')[0] === dateString;
            if (subUnitId !== undefined && 'subUnitId' in queue) {
                return dateMatches && queue.subUnitId === subUnitId;
            }
            return dateMatches;
        });
        return timeSlots.map(slot => {
            const timeSlotString = `${slot.start} - ${slot.end}`;
            const normalQueue = queuesForDate.filter(q => q.timeSlot === timeSlotString && !q.isVIP);
            const vipQueue = queuesForDate.filter(q => q.timeSlot === timeSlotString && q.isVIP);
            return {
                timeSlot: timeSlotString,
                normalQueueCount: normalQueue.length,
                vipQueueCount: vipQueue.length
            };
        });
    }
    async updateQueueStatus(queueId, status, forceUpdate = false) {
        const validStatuses = ['pending', 'active', 'completed', 'cancelled', 'no-show', 'serving', 'waiting'];
        if (!validStatuses.includes(status)) {
            throw new common_1.BadRequestException(`Invalid status: ${status}. Must be one of: ${validStatuses.join(', ')}`);
        }
        const queue = await this.queueRepository.findOne({
            where: { id: queueId },
            relations: ['service']
        });
        if (!queue) {
            throw new common_1.NotFoundException(`Queue with ID ${queueId} not found`);
        }
        const originalStatus = queue.status;
        const finalStatuses = ['no-show', 'completed', 'cancelled'];
        const nonFinalStatuses = ['waiting', 'serving', 'active', 'pending'];
        if (finalStatuses.includes(originalStatus) &&
            nonFinalStatuses.includes(status) &&
            !forceUpdate) {
            console.log(`Prevented status change from ${originalStatus} to ${status} for queue ${queueId} - status is final`);
            throw new common_1.BadRequestException(`Cannot change status from ${originalStatus} to ${status} - status is final. Use forceUpdate=true to override.`);
        }
        const isCheckedIn = queue.isCheckedIn;
        console.log(`Current DB isCheckedIn status for queue ${queueId}: ${isCheckedIn}`);
        queue.status = status;
        queue.isCheckedIn = isCheckedIn;
        const statusUpdateTime = new Date();
        if (status === 'serving') {
            console.log(`Setting servingStartedAt for queue ${queueId} to ${statusUpdateTime.toISOString()}`);
            queue.servingStartedAt = statusUpdateTime;
            queue.currentlyServing = true;
        }
        const updatedQueue = await this.queueRepository.save(queue);
        console.log(`Updated queue ${queueId} in database: status=${status}, servingStartedAt=${queue.servingStartedAt ? queue.servingStartedAt.toISOString() : 'null'}`);
        if (status === 'completed' || status === 'no-show' || status === 'cancelled' || status === 'serving') {
            try {
                console.log(`Recalculating estimated serve times for service ${queue.serviceId} after queue ${queueId} status changed to ${status}`);
                this.queueFlowService.recalculateEstimatedServeTimes(queue.serviceId, queue.date, queue.timeSlot).catch(error => {
                    console.error(`Error recalculating estimated serve times: ${error.message}`);
                });
            }
            catch (error) {
                console.error(`Error triggering recalculation of estimated serve times: ${error.message}`);
            }
        }
        let userName = '';
        let userPhone = '';
        try {
            userName = queue.userName || '';
            if ('fullName' in queue) {
                userName = queue.fullName || userName;
            }
            if ('mobileNumber' in queue) {
                userPhone = queue.mobileNumber || '';
            }
        }
        catch (error) {
            console.error('Error extracting user details:', error);
        }
        if (queue.userId && (!userName || !userPhone)) {
            try {
                const user = await this.fetchUserDetails(queue.userId);
                if (user) {
                    userName = user.fullName || userName;
                    userPhone = user.mobileNumber || userPhone;
                }
            }
            catch (error) {
                console.error(`Error fetching additional user details for queue ${queueId}:`, error);
            }
        }
        let servingTime = 15;
        if (status === 'serving') {
            try {
                const serviceSetup = await this.setupRepository.findOne({
                    where: { service: { id: queue.serviceId } }
                });
                if (serviceSetup?.setupData?.servingTime) {
                    servingTime = parseInt(serviceSetup.setupData.servingTime, 10);
                    console.log(`Using service setup serving time: ${servingTime} minutes for queue ${queueId}`);
                }
            }
            catch (error) {
                console.error(`Error getting service setup for queue ${queueId}:`, error);
            }
        }
        const queueData = {
            id: queue.id,
            serviceId: queue.serviceId,
            serviceName: queue.service?.serviceName || 'Unknown Service',
            serviceType: queue.service?.serviceType || 'Unknown Type',
            date: queue.date,
            timeSlot: queue.timeSlot,
            status,
            isVIP: queue.isVIP,
            userId: queue.userId,
            createdAt: queue.createdAt,
            updatedAt: statusUpdateTime.toISOString(),
            uniqueSlotId: queue.uniqueSlotId || Math.floor(1000 + Math.random() * 9000).toString(),
            isCheckedIn,
            fullName: userName || 'Anonymous',
            mobileNumber: userPhone || null,
            statusUpdatedAt: statusUpdateTime.toISOString(),
            finalStatusSet: (status === 'no-show' || status === 'completed' || status === 'cancelled')
        };
        if (status === 'serving' && queue.servingStartedAt) {
            const estimatedEndTime = new Date(queue.servingStartedAt);
            estimatedEndTime.setMinutes(estimatedEndTime.getMinutes() + servingTime);
            console.log(`Serving details for queue ${queueId}:`);
            console.log(`- Started at: ${queue.servingStartedAt.toISOString()}`);
            console.log(`- Estimated end time: ${estimatedEndTime.toISOString()}`);
            console.log(`- Serving time: ${servingTime} minutes`);
            queueData.servingStartedAt = queue.servingStartedAt.toISOString();
            queueData.estimatedEndTime = estimatedEndTime.toISOString();
            queueData.servingTime = servingTime;
            queueData.servingTimeMinutes = servingTime;
            queueData.remainingMinutes = servingTime;
            queueData.remainingSeconds = servingTime * 60;
            queueData.currentlyServing = true;
        }
        try {
            console.log(`Updating queue ${queueId} status to ${status} in Redis, isCheckedIn: ${isCheckedIn}`);
            const redisQueue = await this.redisService.getQueue(queueId.toString());
            let redisIsCheckedIn = isCheckedIn;
            if (redisQueue) {
                const redisQueueObj = redisQueue;
                if (redisQueueObj.isCheckedIn === true) {
                    redisIsCheckedIn = true;
                    console.log(`Redis has isCheckedIn=true for queue ${queueId}, using that value`);
                }
                if (redisQueueObj.address)
                    queueData.address = redisQueueObj.address;
                if (redisQueueObj.contactPhone)
                    queueData.contactPhone = redisQueueObj.contactPhone;
                if (redisQueueObj.images)
                    queueData.images = redisQueueObj.images;
                if (redisQueueObj.position)
                    queueData.position = redisQueueObj.position;
            }
            if (status === 'no-show' || status === 'completed' || status === 'cancelled') {
                console.log(`Special handling for final status ${status} for queue ${queueId}`);
                const statusKey = `queue:${queueId}:status:${status}`;
                await this.redisService.set(statusKey, queueData, { ex: 86400 * 7 });
                await this.redisService.set(`queue:${queueId}`, queueData, { ex: 86400 });
                const mainKeyData = await this.redisService.get(`queue:${queueId}`);
                if (mainKeyData) {
                    const mainKeyObj = mainKeyData;
                    console.log(`Main key queue:${queueId} now has status: ${mainKeyObj.status}`);
                    if (mainKeyObj.status !== status) {
                        console.log(`Status mismatch detected! Forcing update of main key queue:${queueId}`);
                        const forcedData = {
                            ...queueData,
                            status: status,
                            statusLastUpdatedAt: new Date().toISOString(),
                            forcedUpdate: true
                        };
                        await this.redisService.set(`queue:${queueId}`, forcedData, { ex: 86400 });
                    }
                }
                await this.redisService.updateQueueStatus(queueId.toString(), status, true);
            }
            else if (status === 'serving') {
                console.log(`Setting special handling for serving status on queue ${queueId}`);
                await this.redisService.set(`queue:${queueId}`, queueData, { ex: 86400 });
                await this.redisService.updateQueueStatus(queueId.toString(), status, true);
                const verifiedData = await this.redisService.getQueue(queueId.toString());
                if (verifiedData) {
                    console.log(`Verified serving status for queue ${queueId} in Redis:`);
                    console.log(`- Status: ${verifiedData.status}`);
                    console.log(`- servingStartedAt: ${verifiedData.servingStartedAt || 'not set'}`);
                    console.log(`- servingTimeMinutes: ${verifiedData.servingTimeMinutes || 'not set'}`);
                }
            }
            else {
                await this.redisService.saveQueue(queueId.toString(), queueData);
                await this.redisService.updateQueueStatus(queueId.toString(), status, true);
            }
            await this.redisService.updateQueueCheckInStatus(queueId.toString(), redisIsCheckedIn, true);
            if (queue.service) {
                const date = new Date(queue.date).toISOString().split('T')[0];
                await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), date);
                await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');
                if (status === 'no-show' || status === 'completed' || status === 'cancelled') {
                    console.log(`Explicitly invalidating active-queues keys for queue ${queueId} with final status ${status}`);
                    const activeQueuesKey = `service:${queue.serviceId}:active-queues:${date}`;
                    const allActiveQueuesKey = `service:${queue.serviceId}:active-queues:all`;
                    await this.redisService.del(activeQueuesKey);
                    await this.redisService.del(allActiveQueuesKey);
                    console.log(`Deleted active-queues keys: ${activeQueuesKey} and ${allActiveQueuesKey}`);
                }
                if (queue.timeSlot) {
                    await this.redisService.invalidateQueuePosition(queue.serviceId.toString(), date, queue.timeSlot);
                }
            }
            console.log(`Successfully updated Redis for queue ${queueId} with status ${status}`);
        }
        catch (error) {
            console.error(`Error updating Redis for queue ${queueId}:`, error);
        }
        return updatedQueue;
    }
    async fetchUserDetails(userId) {
        try {
            const queryBuilder = this.queueRepository.createQueryBuilder('queue')
                .select(['queue.fullName', 'queue.mobileNumber'])
                .where('queue.userId = :userId', { userId })
                .andWhere('queue.fullName IS NOT NULL')
                .orderBy('queue.createdAt', 'DESC')
                .limit(1);
            const result = await queryBuilder.getOne();
            return result;
        }
        catch (error) {
            console.error(`Error fetching user details for userId ${userId}:`, error);
            return null;
        }
    }
    async startGracePeriod(queueId) {
        const queue = await this.queueRepository.findOne({ where: { id: queueId } });
        if (!queue) {
            throw new common_1.NotFoundException(`Queue with ID ${queueId} not found`);
        }
        const updatedQueue = await this.queueFlowService.startGracePeriod(queueId, queue.serviceId);
        return {
            ...updatedQueue,
            message: 'Grace period started successfully'
        };
    }
    async getGracePeriodStatus(queueId) {
        const queueData = await this.redisService.getQueue(queueId.toString());
        if (!queueData) {
            const queue = await this.queueRepository.findOne({ where: { id: queueId } });
            if (!queue) {
                throw new common_1.NotFoundException(`Queue with ID ${queueId} not found`);
            }
            const serviceSetup = await this.setupRepository.findOne({
                where: { service: { id: queue.serviceId } }
            });
            const graceTime = serviceSetup?.graceTime || 120;
            let remainingSeconds = 0;
            let isExpired = true;
            if (queue.inGracePeriod && queue.graceStartedAt) {
                const now = new Date();
                const graceEndTime = new Date(queue.graceStartedAt);
                graceEndTime.setSeconds(graceEndTime.getSeconds() + graceTime);
                if (now < graceEndTime) {
                    isExpired = false;
                    remainingSeconds = Math.floor((graceEndTime.getTime() - now.getTime()) / 1000);
                }
            }
            return {
                queueId: queue.id,
                inGracePeriod: queue.inGracePeriod,
                confirmedPresence: queue.confirmedPresence,
                graceStartedAt: queue.graceStartedAt,
                graceTimeSeconds: graceTime,
                remainingSeconds: queue.inGracePeriod ? remainingSeconds : 0,
                isExpired: queue.inGracePeriod ? isExpired : false,
                status: queue.status
            };
        }
        const now = new Date();
        let remainingSeconds = 0;
        let isExpired = true;
        if (queueData.inGracePeriod && queueData.graceStartedAt && queueData.graceEndTime) {
            const graceEndTime = new Date(queueData.graceEndTime);
            if (now < graceEndTime) {
                isExpired = false;
                remainingSeconds = Math.floor((graceEndTime.getTime() - now.getTime()) / 1000);
            }
        }
        return {
            queueId: queueData.id,
            inGracePeriod: queueData.inGracePeriod || false,
            confirmedPresence: queueData.confirmedPresence || false,
            graceStartedAt: queueData.graceStartedAt,
            graceEndTime: queueData.graceEndTime,
            graceTimeSeconds: queueData.graceTimeSeconds || 120,
            remainingSeconds: queueData.inGracePeriod ? remainingSeconds : 0,
            isExpired: queueData.inGracePeriod ? isExpired : false,
            status: queueData.status
        };
    }
};
exports.PartnerService = PartnerService;
exports.PartnerService = PartnerService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(service_entity_1.Service)),
    __param(1, (0, typeorm_1.InjectRepository)(service_setup_entity_1.ServiceSetup)),
    __param(2, (0, typeorm_1.InjectRepository)(bank_details_entity_1.BankDetails)),
    __param(3, (0, typeorm_1.InjectRepository)(queue_entity_1.Queue)),
    __param(4, (0, typeorm_1.InjectRepository)(review_entity_1.Review)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        upload_service_1.UploadService,
        redis_service_1.RedisService,
        queue_flow_service_1.QueueFlowService])
], PartnerService);
//# sourceMappingURL=partner.service.js.map