import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Service } from './entities/service.entity';
import { ServiceSetup } from './entities/service-setup.entity';
import { BankDetails } from './entities/bank-details.entity';
import { Queue } from './entities/queue.entity';
import { Review } from './entities/review.entity';
import { PartnerService } from './partner.service';
import { PartnerController } from './partner.controller';
import { UploadModule } from '../services/upload.module';
import { RedisModule } from '../services/redis/redis.module';
import { QueueFlowModule } from '../services/queue-flow/queue-flow.module';
import { SchedulerModule } from '../services/scheduler/scheduler.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Service, ServiceSetup, BankDetails, Queue, Review]),
    UploadModule,
    RedisModule,
    QueueFlowModule,
    SchedulerModule,
  ],
  providers: [PartnerService],
  controllers: [PartnerController],
  exports: [PartnerService],
})
export class PartnerModule {}
