import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  TextInput,
  ScrollView,
  TouchableOpacity,
  Image,
  StatusBar,
  SafeAreaView,
  Platform,
  Keyboard,
  Alert,
  Linking,
} from "react-native";
import { useRouter } from "expo-router";
import { useUser } from "@clerk/clerk-expo";
import Stepper from "../../components/Stepper";
import { images } from "@/constants";
import ButtonBlueMain from "@/components/ButtonBlueMain";
import * as SecureStore from "expo-secure-store";
import BottomSheetModal from "@/components/BottomSheetModal";
import * as ImagePicker from 'expo-image-picker';

const serviceTypes = [
  "Select Type",
  "Hospital",
  "Bank",
  "Government",
  "Retail",
  "Restaurant",
  "Theatre",
  "Tourism",
  "Salon",
  "Other",
];

export default function ServiceInfoScreen() {
  const router = useRouter();
  const { user } = useUser();
  const [formData, setFormData] = useState({
    serviceName: "",
    serviceType: serviceTypes[0],
    businessPhone: "",
    serviceDescription: "",
    location: "",
  });
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [showPicker, setShowPicker] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<{ uri: string; base64: string | null | undefined; }[]>([]);
  const [location, setLocation] = useState<string | null>(null);
  interface AddressDetails {
    details: {
      buildingNo: string;
      locality: string;
      city: string;
      state: string;
      pincode: string;
    };
  }
  const [savedAddress, setSavedAddress] = useState<AddressDetails | null>(null);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
        setShowPicker(false); // Close picker when keyboard shows
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  useEffect(() => {
    loadSavedAddress();
  }, []);

  const loadSavedAddress = async () => {
    try {
      const addressData = await SecureStore.getItemAsync("serviceAddress");
      if (addressData) {
        setSavedAddress(JSON.parse(addressData));
        setLocation("saved"); // To indicate we have a location
      }
    } catch (error) {
      console.error("Error loading address:", error);
    }
  };

  useEffect(() => {
    loadSavedFormData();
  }, []);

  const loadSavedFormData = async () => {
    try {
      const savedFormData = await SecureStore.getItemAsync("serviceFormData");
      if (savedFormData) {
        setFormData(JSON.parse(savedFormData));
      }
    } catch (error) {
      console.error("Error loading form data:", error);
    }
  };

  const validateForm = () => {
    if (!formData.serviceName.trim()) {
      Alert.alert("Error", "Please enter your service name");
      return false;
    }

    if (formData.serviceType === "Select Type") {
      Alert.alert("Error", "Please select your service type");
      return false;
    }

    if (!formData.businessPhone.trim()) {
      Alert.alert("Error", "Please enter your business phone number");
      return false;
    }

    if (!formData.serviceDescription.trim()) {
      Alert.alert("Error", "Please describe your service");
      return false;
    }

    if (!location) {
      Alert.alert("Error", "Please add your service location");
      return false;
    }

    if (uploadedImages.length === 0) {
      Alert.alert("Error", "Please upload at least one service picture");
      return false;
    }

    return true;
  };

  const requestPermission = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Sorry, we need camera roll permissions to upload images.',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Settings',
            onPress: () => Linking.openSettings()
          }
        ]
      );
      return false;
    }
    return true;
  };

  const handleImageUpload = async () => {
    if (uploadedImages.length >= 3) {
      Alert.alert("Limit Reached", "You can only upload up to 3 images");
      return;
    }

    const hasPermission = await requestPermission();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        quality: 0.7,
        allowsMultipleSelection: true,
        selectionLimit: 3 - uploadedImages.length,
        base64: true, // Add this line
      });

      if (!result.canceled && result.assets) {
        const newImages = result.assets.map(asset => ({
          uri: asset.uri,
          base64: asset.base64
        }));
        const totalImages = [...uploadedImages, ...newImages];
        
        if (totalImages.length > 3) {
          Alert.alert("Too Many Images", "You can only upload up to 3 images");
          return;
        }

        setUploadedImages(totalImages);
        await SecureStore.setItemAsync('serviceImages', JSON.stringify(totalImages));
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to upload image');
    }
  };

  const handleLocationAdd = async () => {
    try {
      await SecureStore.setItemAsync(
        "serviceFormData",
        JSON.stringify(formData)
      );
      router.push("/(location)/allow-location");
    } catch (error) {
      console.error("Error saving form data:", error);
      router.push("/(location)/allow-location");
    }
  };

  const handleNext = () => {
    if (validateForm()) {
      try {
        // Save all form data to SecureStore
        const serviceFormData = {
          serviceName: formData.serviceName.trim(),
          serviceType: formData.serviceType,
          businessPhone: formData.businessPhone.trim(),
          serviceDescription: formData.serviceDescription.trim(),
          email: user?.primaryEmailAddress?.emailAddress
        };

        SecureStore.setItemAsync("serviceFormData", JSON.stringify(serviceFormData))
          .then(() => {
            router.push("/service-setup/step2");
          })
          .catch((error) => {
            console.error("Error saving form data:", error);
            Alert.alert("Error", "Failed to save form data");
          });
      } catch (error) {
        console.error("Error preparing form data:", error);
        Alert.alert("Error", "Failed to process form data");
      }
    }
  };

  useEffect(() => {
    return () => {
      // Save form data when component unmounts
      SecureStore.setItemAsync(
        "serviceFormData",
        JSON.stringify(formData)
      ).catch((error) => console.error("Error saving form data:", error));
    };
  }, [formData]);

  useEffect(() => {
    const loadSavedImages = async () => {
      try {
        const savedImages = await SecureStore.getItemAsync('serviceImages');
        if (savedImages) {
          setUploadedImages(JSON.parse(savedImages));
        }
      } catch (error) {
        console.error('Error loading saved images:', error);
      }
    };

    loadSavedImages();
  }, []);

  const [localPhone, setLocalPhone] = useState("");

  useEffect(() => {
    setLocalPhone(formData.businessPhone);
  }, [formData.businessPhone]);

  const handlePhoneChange = useCallback((text: string) => {
    setLocalPhone(text);
    setFormData(prev => ({ ...prev, businessPhone: text }));
  }, []);

  const renderImageUpload = () => (
    <View>
      <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
        Service Pictures
      </Text>
      <View className="mb-2">
        <Text className="font-poppins-light text-xs text-secondary-500/50">
          Upload up to 3 pictures of your service location
        </Text>
      </View>

      {uploadedImages.length > 0 && (
        <View className="flex-row flex-wrap gap-2 mb-4">
          {uploadedImages.map((uri, index) => (
            <View key={index} className="relative w-24 h-24">
              <Image
                source={{ uri: uri.uri }}
                className="w-24 h-24 rounded-xl"
                style={{ borderWidth: 1, borderColor: '#E5E7EB' }}
              />
              <TouchableOpacity
                className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow-sm"
                onPress={() => {
                  const newImages = uploadedImages.filter((_, i) => i !== index);
                  setUploadedImages(newImages);
                  SecureStore.setItemAsync('serviceImages', JSON.stringify(newImages));
                }}
              >
                <Image 
                  source={images.close} 
                  className="w-4 h-4"
                  style={{ tintColor: '#666' }}
                />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      )}

      {uploadedImages.length < 3 && (
        <TouchableOpacity
          className={`flex-row justify-center items-center border border-primary-500 p-4 rounded-xl mt-2 mb-6 ${
            uploadedImages.length >= 3 ? 'opacity-50' : ''
          }`}
          onPress={handleImageUpload}
          disabled={uploadedImages.length >= 3}
        >
          <Image
            source={images.upload}
            className="w-5 h-5 mr-3"
            tintColor="#159AFF"
          />
          <Text className="text-primary-500 text-center text-base font-[Poppins-Medium]">
            Upload ({uploadedImages.length}/3)
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <View className="px-8 pt-8 pb-4 border-b border-secondary-400">
        <View>
          <View className="w-full flex-row">
            <TouchableOpacity
              className="flex-row items-center absolute left-0 border border-secondary-600 w-12 h-12 rounded-full justify-center"
              onPress={() => router.back()}
            >
              <Image source={images.back} className="w-4 h-4" />
            </TouchableOpacity>
            <View className="flex-1 items-center mt-2 mb-4">
              <Text className="font-poppins-medium  text-2xl mb-4">
                Service Verification
              </Text>
              <View className="w-[300px]">
                <Text className="font-poppins-regular text-center text-sm text-secondary-500/50">
                  Complete the verification process to get your service verified
                </Text>
              </View>
            </View>
          </View>
        </View>

        <Stepper
          currentStep={1}
          steps={["Service Info", "Documents", "Review"]}
        />
      </View>

      <ScrollView
        className="flex-1 bg-white px-4 py-2"
        contentContainerStyle={{
          paddingBottom: 20, // Reduced padding since button is outside
        }}
      >
        <View className="p-5">
          <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
            Service Name
          </Text>
          <TextInput
            className="border border-gray-300 rounded-xl p-4  mt-2 mb-6 font-[Poppins-Regular]"
            value={formData.serviceName}
            placeholder="Enter your service name"
            placeholderTextColor="#9CA3AF"
            onChangeText={(text) =>
              setFormData({ ...formData, serviceName: text })
            }
          />

          <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
            Service Type
          </Text>
          <TouchableOpacity
            onPress={() => setShowPicker(true)}
            className="border border-gray-300 rounded-xl mt-2 mb-6 p-4 flex-row justify-between items-center"
          >
            <Text className="font-[Poppins-Regular] text-base">
              {formData.serviceType}
            </Text>
            <Image
              source={images.down}
              className="w-[16px] h-[10px]"
              style={{ tintColor: "#6B7280" }}
            />
          </TouchableOpacity>

          <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
            Business Phone
          </Text>
          <View className="flex-row items-center border border-gray-300  rounded-xl h-[55px] mt-2 mb-6">
            <View className="bg-gray-100 h-full w-16 items-center justify-center rounded-l-xl mr-3">
              <Text className=" font-poppins-regular  px-2">+91</Text>
            </View>
            <TextInput
              className=" font-[Poppins-Regular]"
              value={localPhone}
              placeholder="Enter your business phone"
              placeholderTextColor="#9CA3AF"
              keyboardType="phone-pad"
              onChangeText={handlePhoneChange}
            />
          </View>

          <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
            Business Email
          </Text>
          <TextInput
            className="border border-secondary-400 bg-secondary-300/50 rounded-xl p-4 mt-2 mb-6 font-[Poppins-Regular]"
            value={user?.primaryEmailAddress?.emailAddress}
            editable={false}
          />

          <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
            Service Description
          </Text>
          <TextInput
            className="border border-gray-300 rounded-xl p-4 mt-2 mb-6 font-[Poppins-Regular] h-28"
            value={formData.serviceDescription}
            placeholder="Describe your service..."
            placeholderTextColor="#9CA3AF"
            onChangeText={(text) =>
              setFormData({ ...formData, serviceDescription: text })
            }
            multiline
            numberOfLines={4}
            textAlignVertical="top"
            style={{
              textAlign: "left",
              verticalAlign: "top",
            }}
          />
          <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
            Service Location
          </Text>
          {location ? (
            <View className="relative">
              <TouchableOpacity
                className="absolute -top-6 right-2 z-10"
                onPress={handleLocationAdd}
              >
                <Text className="text-primary-500 text-sm font-poppins-medium">
                  Change
                </Text>
              </TouchableOpacity>
              <View className="border border-secondary-400 bg-secondary-300/50 rounded-xl  p-5 mt-2 mb-6">
                <View className="flex-row items-start">
                  <Image
                    source={images.mapPin}
                    className="w-5 h-5 mr-2"
                    tintColor="#263238"
                  />
                  <Text className="font-poppins-regular  text-secondary-500 text-sm">
                    {savedAddress?.details?.buildingNo},{" "}
                    {savedAddress?.details?.locality},{" "}
                    {savedAddress?.details?.city},{" "}
                    {savedAddress?.details?.state} -{" "}
                    {savedAddress?.details?.pincode}
                  </Text>
                </View>
              </View>
            </View>
          ) : (
            <TouchableOpacity
              className="flex-row justify-center items-center border border-primary-500 p-4 rounded-xl mt-2 mb-6"
              onPress={handleLocationAdd}
            >
              <Image
                source={images.myLocation}
                className="w-5 h-5 mr-3"
                tintColor="#159AFF"
              />
              <Text className="text-primary-500 text-center text-base font-[Poppins-Medium]">
                Add Location
              </Text>
            </TouchableOpacity>
          )}

          {renderImageUpload()}
        </View>
      </ScrollView>

      {!keyboardVisible && (
        <View
          className="px-4 py-4 border-t justify-center items-center border-gray-200 bg-white"
          style={{
            paddingBottom: Platform.OS === "ios" ? 20 : 15,
          }}
        >
          <View className=" ">
            <ButtonBlueMain
              label="Next Step"
              onPress={handleNext}
              bgVariant="primary"
              textVariant="primary"
              className="w-[380px] h-[80px]"
            />
          </View>
        </View>
      )}

      <BottomSheetModal
        visible={showPicker}
        onClose={() => setShowPicker(false)}
        headerText="Select Service Type"
        items={serviceTypes.filter((type) => type !== "Select Type")}
        onSelectItem={(type) => {
          setFormData({ ...formData, serviceType: type });
          setShowPicker(false);
        }}
        defaultHeight={45}
        expandedHeight={65}
      />
    </SafeAreaView>
  );
}