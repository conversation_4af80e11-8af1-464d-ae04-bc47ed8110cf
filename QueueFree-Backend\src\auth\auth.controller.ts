import { <PERSON>, Post, Body, HttpStatus, HttpException } from '@nestjs/common';
import { CustomerService } from '../customer/customer.service';
import { PartnerService } from '../partner/partner.service';

@Controller('auth')
export class AuthController {
  private registrationInProgress = new Set<string>();

  constructor(
    private readonly customerService: CustomerService,
    private readonly partnerService: PartnerService,
  ) {}

  @Post('register-user')
  async registerUser(@Body() userData: { email: string, fullName?: string, clerkId?: string }) {
    if (this.registrationInProgress.has(userData.email)) {
      return {
        status: 'success',
        statusCode: HttpStatus.OK,
        message: 'Registration already in progress',
      };
    }

    try {
      this.registrationInProgress.add(userData.email);
      console.log('Starting user registration process for:', userData.email);
      console.log('User data received:', JSON.stringify(userData));
      const result = await this.customerService.createUser(
        userData.email, 
        userData.fullName,
        userData.clerkId
      );
      console.log('User registration completed:', result);
      return {
        status: 'success',
        statusCode: HttpStatus.OK,
        message: 'User registered successfully',
        data: result
      };
    } catch (error) {
      console.error('User registration failed:', error);
      throw new HttpException({
        status: 'error',
        message: error.message || 'User registration failed'
      }, HttpStatus.BAD_REQUEST);
    } finally {
      this.registrationInProgress.delete(userData.email);
    }
  }

  @Post('register-partner')
  async registerPartner(@Body() partnerData: { email: string }) {
    if (this.registrationInProgress.has(partnerData.email)) {
      return {
        status: 'success',
        statusCode: HttpStatus.OK,
        message: 'Registration already in progress',
      };
    }

    try {
      this.registrationInProgress.add(partnerData.email);
      console.log('Starting partner registration process for:', partnerData.email);
      const result = await this.partnerService.createPartner(partnerData.email);
      console.log('Partner registration completed:', result);
      return {
        status: 'success',
        statusCode: HttpStatus.OK,
        message: 'Partner registered successfully',
        data: result
      };
    } catch (error) {
      console.error('Partner registration failed:', error);
      throw new HttpException({
        status: 'error',
        message: error.message || 'Partner registration failed'
      }, HttpStatus.BAD_REQUEST);
    } finally {
      this.registrationInProgress.delete(partnerData.email);
    }
  }
}
