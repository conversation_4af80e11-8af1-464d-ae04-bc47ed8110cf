import React from "react";
import {
  StatusBar,
  View,
  Text,
  SafeAreaView,
  Image,
  TextInput,
  Alert,
} from "react-native";
import { images } from "@/constants";
import ButtonWhite from "@/components/ButtonWhite";
import { router } from "expo-router";
import { useSignIn, useSignUp } from "@clerk/clerk-expo";

const SignInScreen = () => {
  const [email, setEmail] = React.useState("");
  const [loading, setLoading] = React.useState(false);
  const { signIn } = useSignIn();
  const { signUp } = useSignUp();

  const onSignInOrSignUp = async () => {
    if (!signIn || !signUp) {
      Alert.alert("Error", "Authentication service is not available.");
      return;
    }

    if (!email || !/\S+@\S+\.\S+/.test(email)) {
      Alert.alert("Error", "Please enter a valid email address");
      return;
    }

    try {
      setLoading(true);
      // First attempt to sign in
      try {
        const signInAttempt = await signIn.create({
          identifier: email,
          strategy: "email_code",
        });

        router.push({
          pathname: "/verify-otp",
          params: {
            email: email,
            isSignUp: "false",
          },
        });
      } catch (err: any) {
        // If user doesn't exist, attempt to sign up
        if (err.message === "Couldn't find your account.") {
          const signUpAttempt = await signUp.create({
            emailAddress: email,
          });

          await signUpAttempt.prepareEmailAddressVerification();

          router.push({
            pathname: "/verify-otp",
            params: {
              email: email,
              isSignUp: "true",
            },
          });
        } else {
          throw err; // Re-throw other errors
        }
      }
    } catch (err: any) {
      Alert.alert(
        "Error",
        err.message || "Failed to process your request. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      <View className="flex-1 px-10 mt-10">
        <Image
          source={images.queue}
          className="w-full h-[320px]"
          resizeMode="contain"
        />

        <View className="mt-8">
          <Text className="font-poppins-medium text-primary-500 text-3xl text-center">
          Streamline
          </Text>
          <Text className="font-poppins-medium text-[35px] text-center">
          Your Queues!
          </Text>
        </View>
      </View>

      <View className="absolute -bottom-5 left-0 right-0">
        <Image
          source={images.waves}
          className="w-full h-[500px]"
          resizeMode="cover"
        />

        <View className="absolute bottom-20 left-0 right-0 px-8">
          <View className="flex-row items-center justify-center space-x-5 mb-7">
            <View className="h-[1px] w-28 bg-white" />
            <Text className="font-poppins-regular text-center text-white text-md px-4">
              Log in or Sign up
            </Text>
            <View className="h-[1px] w-28 bg-white" />
          </View>
          <View className="border h-[60px] w-auto border-white rounded-xl mb-8 bg-transparent">
            <TextInput
              className="text-lg font-poppins-regular text-white mx-5 h-full"
              placeholder="Enter your email address"
              placeholderTextColor="rgba(255, 255, 255, 0.7)"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              cursorColor="white"
            />
          </View>
          <ButtonWhite
            label={loading ? "Please wait..." : "Continue"}
            onPress={onSignInOrSignUp}
            disabled={loading}
          />
          <Text className="text-center font-poppins-regular text-white text-xs mt-20 px-4 leading-5">
            By continuing, you agree to our{"\n"}
            <Text className="text-white font-poppins-medium">
              Privacy Policy
            </Text>{" "}
            and{" "}
            <Text className="text-white font-poppins-medium">
              Terms & Condition
            </Text>
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default SignInScreen;
