"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSetupDataToJsonb1710539000000 = void 0;
class UpdateSetupDataToJsonb1710539000000 {
    async up(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE "service_setups" 
            ALTER COLUMN "setupData" TYPE JSONB USING "setupData"::JSONB
        `);
        await queryRunner.query(`
            CREATE INDEX IF NOT EXISTS idx_setup_data ON "service_setups" USING GIN ("setupData")
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            DROP INDEX IF EXISTS idx_setup_data
        `);
        await queryRunner.query(`
            ALTER TABLE "service_setups" 
            ALTER COLUMN "setupData" TYPE JSON USING "setupData"::JSON
        `);
    }
}
exports.UpdateSetupDataToJsonb1710539000000 = UpdateSetupDataToJsonb1710539000000;
//# sourceMappingURL=1710539000000-UpdateSetupDataToJsonb.js.map