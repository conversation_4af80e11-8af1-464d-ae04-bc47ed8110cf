import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not, IsNull, In, LessThan, MoreThanOrEqual, Between } from 'typeorm';
import { User } from './user.entity';
import { JoinQueueDto } from './dto/join-queue.dto';
import { RescheduleQueueDto } from './dto/reschedule-queue.dto';
import { Queue } from '../partner/entities/queue.entity';
import { Service } from '../partner/entities/service.entity';
import { ServiceSetup } from '../partner/entities/service-setup.entity';
import { RedisService } from '../services/redis/redis.service';
import { QueueFlowService } from '../services/queue-flow/queue-flow.service';
import { ModuleRef } from '@nestjs/core';

@Injectable()
export class CustomerService {
  private queueFlowService: QueueFlowService | null = null;

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Queue)
    private readonly queueRepository: Repository<Queue>,
    @InjectRepository(Service)
    private readonly serviceRepository: Repository<Service>,
    @InjectRepository(ServiceSetup)
    private readonly serviceSetupRepository: Repository<ServiceSetup>,
    private readonly redisService: RedisService,
    private readonly moduleRef: ModuleRef
  ) {}

  /**
   * Get the QueueFlowService instance
   * This is needed because we can't directly inject QueueFlowService due to circular dependency
   */
  private async getQueueFlowService(): Promise<QueueFlowService | null> {
    if (this.queueFlowService) {
      return this.queueFlowService;
    }

    try {
      this.queueFlowService = await this.moduleRef.resolve(QueueFlowService);
      return this.queueFlowService;
    } catch (error) {
      console.error('Error resolving QueueFlowService:', error);
      return null;
    }
  }

  // Helper method to check if a time slot has ended
  private hasTimeSlotEnded(queueDate: string | Date, timeSlot: string, buffer: number = 0): boolean {
    try {
      // Format the queue date for comparison
      let queueDateObj: Date;
      if (queueDate instanceof Date) {
        queueDateObj = queueDate;
      } else if (typeof queueDate === 'string') {
        queueDateObj = new Date(queueDate);
      } else {
        queueDateObj = new Date();
      }

      // Get current date and time
      const now = new Date();

      // Extract date parts for comparison
      const queueYear = queueDateObj.getFullYear();
      const queueMonth = queueDateObj.getMonth();
      const queueDay = queueDateObj.getDate();

      const nowYear = now.getFullYear();
      const nowMonth = now.getMonth();
      const nowDay = now.getDate();

      // If the queue date is in the past (different day), the time slot has ended
      if (queueYear < nowYear ||
          (queueYear === nowYear && queueMonth < nowMonth) ||
          (queueYear === nowYear && queueMonth === nowMonth && queueDay < nowDay)) {
        console.log(`Queue date ${queueDateObj.toISOString().split('T')[0]} is before today ${now.toISOString().split('T')[0]} - ended`);
        return true;
      }

      // If the queue date is in the future (different day), the time slot hasn't ended
      if (queueYear > nowYear ||
          (queueYear === nowYear && queueMonth > nowMonth) ||
          (queueYear === nowYear && queueMonth === nowMonth && queueDay > nowDay)) {
        console.log(`Queue date ${queueDateObj.toISOString().split('T')[0]} is after today ${now.toISOString().split('T')[0]} - not ended`);
        return false;
      }

      // If the queue date is today, we need to check the time
      console.log(`Queue date ${queueDateObj.toISOString().split('T')[0]} is today - checking time slot`);

      // Parse the time slot
      let timeEnd = '';
      if (timeSlot && timeSlot.includes(' - ')) {
        timeEnd = timeSlot.split(' - ')[1].trim();
      } else if (timeSlot) {
        timeEnd = timeSlot.trim(); // If there's just one time, use that
      } else {
        console.log('No time slot provided, cannot determine if ended');
        return false; // If there's no time slot, we can't determine if it's ended
      }

      console.log(`Time slot end time: ${timeEnd}`);

      // Parse the end time, handling AM/PM format
      let hours = 0;
      let minutes = 0;

      if (timeEnd.includes('AM') || timeEnd.includes('PM')) {
        // Handle 12-hour format with AM/PM
        const isPM = timeEnd.includes('PM');
        const timeParts = timeEnd.replace(/\s?(AM|PM)/, '').split(':');

        hours = parseInt(timeParts[0], 10);
        if (isPM && hours < 12) hours += 12;
        if (!isPM && hours === 12) hours = 0;

        minutes = parseInt(timeParts[1] || '0', 10);
      } else {
        // Handle 24-hour format
        const [hoursStr, minutesStr] = timeEnd.split(':');
        hours = parseInt(hoursStr, 10);
        minutes = parseInt(minutesStr || '0', 10);
      }

      // Create a Date object for the end time of the time slot today
      const timeSlotEndTime = new Date(queueDateObj);
      timeSlotEndTime.setHours(hours, minutes, 0, 0);

      // Add buffer minutes if provided (can be used for grace periods)
      if (buffer > 0) {
        timeSlotEndTime.setMinutes(timeSlotEndTime.getMinutes() + buffer);
      }

      // Compare with current time
      const hasEnded = now > timeSlotEndTime;
      console.log(`Current time: ${now.toLocaleTimeString()}, End time: ${timeSlotEndTime.toLocaleTimeString()}, Has ended: ${hasEnded}`);
      return hasEnded;
    } catch (error) {
      console.error(`Error in hasTimeSlotEnded: ${error.message}`, error);
      // Default to false if there's an error to prevent incorrect status changes
      return false;
    }
  }

  async createUser(email: string, fullName?: string, clerkId?: string) {
    if (!email || typeof email !== 'string') {
      throw new BadRequestException('Valid email is required');
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new BadRequestException('Invalid email format');
    }

    try {
      console.log('Checking for existing user:', email);
      let user = await this.userRepository.findOne({ where: { email } });

      if (user) {
        // Update existing user with clerkId if provided and not already set
        let updated = false;
        if (clerkId && !user.clerkId) {
          user.clerkId = clerkId;
          updated = true;
        }
        if (fullName && !user.fullName) {
          user.fullName = fullName;
          updated = true;
        }

        if (updated) {
          user = await this.userRepository.save(user);
        }

        return {
          status: 'success',
          message: 'User already registered',
          user,
          isExisting: true
        };
      }

      console.log('Creating new user:', email);
      user = this.userRepository.create({
        email,
        fullName,
        clerkId
      });
      const savedUser = await this.userRepository.save(user);
      console.log('New user created:', savedUser);

      return {
        status: 'success',
        message: 'User created successfully',
        user: savedUser,
        isExisting: false
      };
    } catch (error) {
      console.error('Error in createUser:', error);
      if (error?.code === '23505') { // PostgreSQL unique constraint violation
        throw new BadRequestException('Email already registered');
      }
      throw new BadRequestException('Failed to process user registration: ' + error.message);
    }
  }

  async findUserByClerkId(clerkId: string): Promise<User | null> {
    if (!clerkId) {
      return null;
    }
    return this.userRepository.findOne({ where: { clerkId } });
  }

  async updateLocation(email: string, latitude: number, longitude: number) {
    try {
      const user = await this.userRepository.findOne({ where: { email } });
      if (!user) {
        throw new Error('User not found');
      }

      user.latitude = latitude;
      user.longitude = longitude;
      user.lastLocationUpdate = new Date();

      await this.userRepository.save(user);

      return {
        success: true,
        message: 'Location updated successfully'
      };
    } catch (error) {
      throw new Error(`Failed to update location: ${error.message}`);
    }
  }

  async updateVIPStatus(email: string, isVIP: boolean) {
    try {
      const user = await this.userRepository.findOne({ where: { email } });
      if (!user) {
        throw new Error('User not found');
      }

      user.isVIP = isVIP;
      await this.userRepository.save(user);

      return {
        success: true,
        message: `VIP status ${isVIP ? 'enabled' : 'disabled'} successfully`,
        isVIP
      };
    } catch (error) {
      throw new Error(`Failed to update VIP status: ${error.message}`);
    }
  }

  async getUserProfile(email: string) {
    try {
      const user = await this.userRepository.findOne({ where: { email } });
      if (!user) {
        throw new Error('User not found');
      }

      return {
        success: true,
        user: {
          id: user.id,
          email: user.email,
          fullName: user.fullName,
          mobileNumber: user.mobileNumber,
          isVIP: user.isVIP,
          lastLocationUpdate: user.lastLocationUpdate
        }
      };
    } catch (error) {
      throw new Error(`Failed to get user profile: ${error.message}`);
    }
  }

  async updateUserDetails(email: string, fullName?: string, mobileNumber?: string) {
    try {
      const user = await this.userRepository.findOne({ where: { email } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Update fields if provided
      if (fullName !== undefined) {
        user.fullName = fullName;
      }

      if (mobileNumber !== undefined) {
        // Basic mobile number validation
        if (mobileNumber && !/^\+?[0-9]{10,15}$/.test(mobileNumber)) {
          throw new BadRequestException('Invalid mobile number format');
        }
        user.mobileNumber = mobileNumber;
      }

      await this.userRepository.save(user);

      return {
        success: true,
        message: 'User details updated successfully',
        user: {
          id: user.id,
          email: user.email,
          fullName: user.fullName,
          mobileNumber: user.mobileNumber,
          isVIP: user.isVIP
        }
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new Error(`Failed to update user details: ${error.message}`);
    }
  }

  async updateClerkId(email: string, clerkId: string) {
    try {
      if (!email || !clerkId) {
        throw new BadRequestException('Email and clerk ID are required');
      }

      const user = await this.userRepository.findOne({ where: { email } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Update the clerk ID
      user.clerkId = clerkId;
      await this.userRepository.save(user);

      return {
        success: true,
        message: 'User clerk ID updated successfully',
        user: {
          id: user.id,
          email: user.email,
          clerkId: user.clerkId
        }
      };
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to update user clerk ID: ${error.message}`);
    }
  }

  async addToWishlist(email: string, serviceId: string) {
    try {
      const user = await this.userRepository.findOne({ where: { email } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Initialize wishlist array if it doesn't exist
      if (!user.wishlist) {
        user.wishlist = [];
      }

      // Check if service is already in wishlist
      if (!user.wishlist.includes(serviceId)) {
        user.wishlist.push(serviceId);
        await this.userRepository.save(user);
        return {
          success: true,
          message: 'Service added to wishlist',
          wishlist: user.wishlist
        };
      } else {
        return {
          success: true,
          message: 'Service already in wishlist',
          wishlist: user.wishlist
        };
      }
    } catch (error) {
      throw new Error(`Failed to add service to wishlist: ${error.message}`);
    }
  }

  async removeFromWishlist(email: string, serviceId: string) {
    try {
      const user = await this.userRepository.findOne({ where: { email } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Check if wishlist exists and service is in it
      if (user.wishlist && user.wishlist.includes(serviceId)) {
        user.wishlist = user.wishlist.filter(id => id !== serviceId);
        await this.userRepository.save(user);
        return {
          success: true,
          message: 'Service removed from wishlist',
          wishlist: user.wishlist
        };
      } else {
        return {
          success: false,
          message: 'Service not found in wishlist',
          wishlist: user.wishlist || []
        };
      }
    } catch (error) {
      throw new Error(`Failed to remove service from wishlist: ${error.message}`);
    }
  }

  async getWishlist(email: string) {
    try {
      const user = await this.userRepository.findOne({ where: { email } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const wishlistItems = (user.wishlist || []).map(serviceId => ({
        serviceId
      }));

      return {
        status: "success",
        wishlist: wishlistItems
      };
    } catch (error) {
      throw new Error(`Failed to get wishlist: ${error.message}`);
    }
  }

  async joinQueue(joinQueueDto: JoinQueueDto): Promise<Queue> {
    const {
      serviceId,
      userId,
      userName,
      mobileNumber,
      date,
      timeSlot,
      amount,
      isVIP: requestIsVIP,
      hasSubUnits,
      subUnitId,
      subUnitName
    } = joinQueueDto;

    // Check if service exists
    const service = await this.serviceRepository.findOne({
      where: { id: Number(serviceId) },
    });

    if (!service) {
      throw new NotFoundException(`Service with ID ${serviceId} not found`);
    }

    // Try to find user by Clerk ID first (which is passed as userId)
    let dbUserId: number | null = null;
    let isVIP = requestIsVIP;

    try {
      // First check if this is a clerk ID
      const userByClerkId = await this.findUserByClerkId(userId);

      if (userByClerkId) {
        // If found, use the database ID as the reference
        dbUserId = userByClerkId.id;
        isVIP = requestIsVIP ?? userByClerkId.isVIP;
      } else if (userId.includes('@')) {
        // If it looks like an email, try to find by email
        const userByEmail = await this.userRepository.findOne({ where: { email: userId } });
        if (userByEmail) {
          dbUserId = userByEmail.id;
          isVIP = requestIsVIP ?? userByEmail.isVIP;
        }
      }
    } catch (error) {
      console.error('Error finding user for queue:', error);
    }

    // Check if user already has a queue entry for this service, date and time slot
    const existingQueue = await this.queueRepository.findOne({
      where: {
        userId,
        serviceId: Number(serviceId),
        date: new Date(date),
        timeSlot,
        status: 'waiting', // Only consider waiting entries
      },
    });

    if (existingQueue) {
      throw new BadRequestException('You already have a queue entry for this service, date and time slot');
    }

    // Generate a 4-digit unique code for the slot
    const uniqueSlotId = Math.floor(1000 + Math.random() * 9000).toString();

    // Check if service has subunits
    const serviceSetup = await this.serviceSetupRepository.findOne({
      where: { service: { id: Number(serviceId) } }
    });

    const hasSubUnitsFlag = serviceSetup?.setupData?.hasSubUnits || false;

    // Get all queues for this service, date, and time slot (both waiting and serving)
    const allQueues = await this.queueRepository.find({
      where: {
        serviceId: Number(serviceId),
        date: new Date(date),
        timeSlot,
        status: In(['waiting', 'serving']), // Include both waiting and serving queues
      },
      order: {
        isVIP: 'DESC', // VIP first
        position: 'ASC', // Then by position
        createdAt: 'ASC' // Then by creation time
      }
    });

    // Separate waiting and serving queues
    const servingQueues = allQueues.filter(q => q.status === 'serving');
    const waitingQueues = allQueues.filter(q => q.status === 'waiting');

    console.log(`Found ${servingQueues.length} serving queues and ${waitingQueues.length} waiting queues for service ${serviceId}, date ${date}, timeSlot ${timeSlot}`);

    // Calculate position based on VIP status - using the same logic as summary.tsx
    let position = 1; // Default position if no queues exist

    // Count all queues by VIP status (both waiting and serving)
    const vipCount = allQueues.filter(q => q.isVIP).length;
    const normalCount = allQueues.filter(q => !q.isVIP).length;
    const totalCount = vipCount + normalCount;

    console.log(`Queue counts - VIP: ${vipCount}, Normal: ${normalCount}, Total: ${totalCount}`);

    // Calculate position based on VIP status with enhanced logic
    if (isVIP) {
      // Enhanced VIP positioning logic
      const firstPersonInQueue = allQueues.find(q => q.position === 1);

      if (!firstPersonInQueue) {
        // No one in queue, VIP gets position 1
        position = 1;
        console.log(`VIP user will be in position ${position} (first in empty queue)`);
      } else if (firstPersonInQueue.status === 'waiting') {
        // First person is waiting, VIP can jump ahead
        position = 1;
        console.log(`VIP user will be in position ${position} (jumping ahead of waiting first person)`);

        // Update positions of all existing queues (both VIP and normal)
        for (const q of allQueues) {
          q.position += 1;
        }

        // Save updated positions to database
        if (allQueues.length > 0) {
          await this.queueRepository.save(allQueues);

          // Update Redis cache for each affected queue member
          for (const q of allQueues) {
            const existingRedisData = await this.redisService.getQueue(q.id.toString());
            if (existingRedisData) {
              existingRedisData.position = q.position;
              await this.redisService.saveQueue(q.id.toString(), existingRedisData);
              console.log(`Updated Redis position for queue ${q.id} to position ${q.position}`);
            }
          }
        }
      } else if (firstPersonInQueue.status === 'serving') {
        // First person is being served, check for VIPs waiting behind them
        const vipQueuesWaiting = waitingQueues.filter(q => q.isVIP);

        if (vipQueuesWaiting.length === 0) {
          // No VIPs waiting, new VIP gets position 2
          position = 2;
          console.log(`VIP user will be in position ${position} (first person serving, no VIPs waiting)`);

          // Update positions of all non-VIP waiting queues
          const nonVipWaitingQueues = waitingQueues.filter(q => !q.isVIP);
          for (const q of nonVipWaitingQueues) {
            q.position += 1;
          }

          // Save updated positions to database
          if (nonVipWaitingQueues.length > 0) {
            await this.queueRepository.save(nonVipWaitingQueues);

            // Update Redis cache for each affected queue member
            for (const q of nonVipWaitingQueues) {
              const existingRedisData = await this.redisService.getQueue(q.id.toString());
              if (existingRedisData) {
                existingRedisData.position = q.position;
                await this.redisService.saveQueue(q.id.toString(), existingRedisData);
                console.log(`Updated Redis position for queue ${q.id} to position ${q.position}`);
              }
            }
          }
        } else {
          // VIPs already waiting, new VIP joins after the last VIP
          const lastVipPosition = Math.max(...vipQueuesWaiting.map(q => q.position));
          position = lastVipPosition + 1;
          console.log(`VIP user will be in position ${position} (after last VIP at position ${lastVipPosition})`);

          // Update positions of all non-VIP queues that come after this position
          const nonVipQueuesToUpdate = waitingQueues.filter(q => !q.isVIP && q.position >= position);
          for (const q of nonVipQueuesToUpdate) {
            q.position += 1;
          }

          // Save updated positions to database
          if (nonVipQueuesToUpdate.length > 0) {
            await this.queueRepository.save(nonVipQueuesToUpdate);

            // Update Redis cache for each affected queue member
            for (const q of nonVipQueuesToUpdate) {
              const existingRedisData = await this.redisService.getQueue(q.id.toString());
              if (existingRedisData) {
                existingRedisData.position = q.position;
                await this.redisService.saveQueue(q.id.toString(), existingRedisData);
                console.log(`Updated Redis position for queue ${q.id} to position ${q.position}`);
              }
            }
          }
        }
      } else {
        // Fallback to original logic if status is unclear
        position = vipCount + 1;
        console.log(`VIP user will be in position ${position} (fallback logic)`);

        // Need to update positions of all non-VIP waiting queues
        const nonVipWaitingQueues = waitingQueues.filter(q => !q.isVIP);
        for (const q of nonVipWaitingQueues) {
          q.position += 1;
        }

        // Save updated positions to database
        if (nonVipWaitingQueues.length > 0) {
          await this.queueRepository.save(nonVipWaitingQueues);

          // Update Redis cache for each affected queue member
          for (const q of nonVipWaitingQueues) {
            const existingRedisData = await this.redisService.getQueue(q.id.toString());
            if (existingRedisData) {
              existingRedisData.position = q.position;
              await this.redisService.saveQueue(q.id.toString(), existingRedisData);
              console.log(`Updated Redis position for queue ${q.id} to position ${q.position}`);
            }
          }
        }
      }
    } else {
      // For normal: position is after all VIPs and existing normals
      position = totalCount + 1;
      console.log(`Normal user will be in position ${position} (after all VIPs and normals)`);
    }

    console.log(`Calculated position ${position} for new queue (isVIP: ${isVIP})`);

    // Log serving queues for debugging
    if (servingQueues.length > 0) {
      console.log(`There are ${servingQueues.length} queues currently being served, included in position calculation`);
    }

    console.log(`Setting position ${position} for new queue in service ${serviceId}, date ${date}, timeSlot ${timeSlot}, isVIP: ${isVIP}`);

    // Create a new queue entry
    const newQueue = new Queue();
    newQueue.serviceId = Number(serviceId);
    newQueue.userId = userId;
    newQueue.userName = userName;
    newQueue.date = new Date(date);
    newQueue.timeSlot = timeSlot;
    newQueue.status = 'waiting';
    newQueue.isVIP = isVIP || false;
    newQueue.uniqueSlotId = uniqueSlotId;
    newQueue.hasSubUnits = hasSubUnitsFlag || false;
    newQueue.subUnitId = subUnitId || '';
    newQueue.subUnitName = subUnitName || '';
    newQueue.position = position; // Set the position
    newQueue.initialPositionAtJoin = position; // Store the initial position at join time

    // Set current time as the join time
    newQueue.createdAt = new Date(); // This will be overwritten by TypeORM but we set it for clarity

    // Set the serve time based on subunit if available
    if (hasSubUnitsFlag && subUnitId && serviceSetup?.setupData?.subUnits) {
      const subUnitIndex = parseInt(subUnitId, 10);
      const subUnit = serviceSetup.setupData.subUnits[subUnitIndex];
      if (subUnit?.avgServeTime) {
        newQueue.serveTime = parseInt(subUnit.avgServeTime, 10);
        console.log(`Setting serve time from subunit ${subUnitId} (${subUnit.name}): ${newQueue.serveTime} minutes`);
      } else {
        newQueue.serveTime = 15; // Default
        console.log(`No serve time found for subunit ${subUnitId}, using default: 15 minutes`);
      }
    } else if (serviceSetup?.setupData?.servingTime) {
      newQueue.serveTime = parseInt(serviceSetup.setupData.servingTime, 10);
      console.log(`Setting serve time from service: ${newQueue.serveTime} minutes`);
    } else {
      newQueue.serveTime = 15; // Default
      console.log(`No serve time found for service, using default: 15 minutes`);
    }

    // Save to database
    const savedQueue = await this.queueRepository.save(newQueue) as Queue;

    console.log(`New queue created with ID ${savedQueue.id} for service ${serviceId}`);

    // Calculate estimated serve time immediately after creating the queue
    try {
      // Get the QueueFlowService from the module
      const queueFlowService = await this.getQueueFlowService();
      if (queueFlowService) {
        console.log(`Calculating estimated serve time for new queue ${savedQueue.id}`);
        await queueFlowService.calculateEstimatedServeTime(savedQueue.id);

        // Also recalculate for all queues in this service to ensure consistency
        await queueFlowService.recalculateEstimatedServeTimes(
          savedQueue.serviceId,
          savedQueue.date,
          savedQueue.timeSlot
        );
      } else {
        console.error(`QueueFlowService not available for calculating estimated serve time`);
      }
    } catch (error) {
      console.error(`Error calculating estimated serve time for queue ${savedQueue.id}:`, error);
    }

    // Format date for Redis keys
    const dateStr = (() => {
      if (!date) {
        return new Date().toISOString().split('T')[0];
      }
      // date is a string from JoinQueueDto, directly convert to ISO string
      return new Date(date).toISOString().split('T')[0];
    })();

    // Store in Redis for faster access with additional metadata
    const queueDataForRedis = {
      ...savedQueue,
      amount,
      mobileNumber,
      serviceName: service.serviceName,
      serviceType: service.serviceType,
      uniqueSlotId, // Add the 4-digit code
      hasSubUnits: hasSubUnitsFlag,
      subUnitId: subUnitId || null,
      subUnitName: subUnitName || null,
      position, // Include the position
      initialPositionAtJoin: position, // Include the initial position at join time
      serveTime: savedQueue.serveTime // Include the serve time
    };

    // Only add dbUserId if it's not null
    if (dbUserId !== null) {
      Object.assign(queueDataForRedis, { dbUserId });
    }

    await this.redisService.saveQueue(savedQueue.id.toString(), queueDataForRedis);

    // Update counts in Redis
    const countKey = isVIP
      ? `queue:vipcount:${serviceId}:${dateStr}:${timeSlot}`
      : `queue:normalcount:${serviceId}:${dateStr}:${timeSlot}`;
    const currentCount = await this.redisService.get(countKey) || 0;
    await this.redisService.set(countKey, parseInt(currentCount.toString()) + 1);

    // Update position data in Redis
    // Get all positions for this service, date, and time slot
    const positionsKey = `service:${serviceId}:positions:${dateStr}:${timeSlot}`;
    const positionsData = await this.redisService.get(positionsKey) || {};

    // Add the new queue's position
    positionsData[savedQueue.id] = position;

    // Save updated positions to Redis
    await this.redisService.set(positionsKey, positionsData);

    // Also save using the dedicated method
    await this.redisService.saveQueuePosition(
      serviceId.toString(),
      dateStr,
      timeSlot,
      positionsData
    );

    // Invalidate all cached data to ensure the new queue is immediately visible

    // Invalidate service active queues cache
    const activeQueuesKey = `service:${serviceId}:active-queues:${dateStr}`;
    const allActiveQueuesKey = `service:${serviceId}:active-queues:all`;
    await this.redisService.del(activeQueuesKey);
    await this.redisService.del(allActiveQueuesKey);

    // Invalidate service-level queues cache
    await this.redisService.invalidateServiceQueues(Number(serviceId), dateStr);
    await this.redisService.invalidateServiceQueues(Number(serviceId), 'all');

    // Invalidate queue counts cache
    const queueCountsKey = `service:${serviceId}:queue-counts:${dateStr}`;
    await this.redisService.del(queueCountsKey);

    // Invalidate position cache for this time slot
    const positionKey = `service:${serviceId}:position:${dateStr}:${timeSlot}`;
    await this.redisService.del(positionKey);

    console.log(`Invalidated Redis caches for fast refresh after queue creation`);

    // Return queue without dbUserId property to match Queue type
    return savedQueue as unknown as Queue;
  }

  // Helper method to calculate position for reschedule (excludes the queue being rescheduled)
  private async calculatePositionForReschedule(
    serviceId: number,
    date: string,
    timeSlot: string,
    isVIP: boolean,
    excludeQueueId: number
  ): Promise<number> {
    // Get all existing queues for this service, date, and time slot (excluding the one being rescheduled)
    const allQueues = await this.queueRepository.find({
      where: {
        serviceId,
        date: new Date(date),
        timeSlot,
        status: In(['waiting', 'checked-in', 'serving']),
      },
      order: {
        isVIP: 'DESC', // VIP first
        createdAt: 'ASC' // Then by creation time
      }
    });

    // Filter out the queue being rescheduled
    const filteredQueues = allQueues.filter(q => q.id !== excludeQueueId);

    console.log(`Found ${filteredQueues.length} existing queues for reschedule position calculation (excluding queue ${excludeQueueId})`);

    // Calculate position based on VIP status
    let position = 1; // Default position if no queues exist

    if (filteredQueues.length === 0) {
      // No other queues, this will be first
      position = 1;
      console.log(`Rescheduled queue will be in position ${position} (first in empty slot)`);
    } else {
      // Count queues by VIP status
      const vipCount = filteredQueues.filter(q => q.isVIP).length;
      const normalCount = filteredQueues.filter(q => !q.isVIP).length;
      const totalCount = vipCount + normalCount;

      console.log(`Queue counts for reschedule - VIP: ${vipCount}, Normal: ${normalCount}, Total: ${totalCount}`);

      if (isVIP) {
        // VIP logic: check if first person is waiting
        const firstPersonInQueue = filteredQueues.find(q => q.position === 1);

        if (!firstPersonInQueue || firstPersonInQueue.status === 'waiting') {
          // VIP can jump to position 1
          position = 1;
          console.log(`VIP rescheduled queue will be in position ${position} (jumping ahead)`);

          // Update positions of existing queues
          for (const q of filteredQueues) {
            q.position += 1;
          }

          // Save updated positions to database
          if (filteredQueues.length > 0) {
            await this.queueRepository.save(filteredQueues);

            // Update Redis cache for each affected queue
            for (const q of filteredQueues) {
              const existingRedisData = await this.redisService.getQueue(q.id.toString());
              if (existingRedisData) {
                existingRedisData.position = q.position;
                await this.redisService.saveQueue(q.id.toString(), existingRedisData);
                console.log(`Updated Redis position for queue ${q.id} to position ${q.position} due to VIP reschedule`);
              }
            }
          }
        } else {
          // First person is serving, VIP goes to position 2 (or after existing VIPs)
          position = vipCount + 1;
          console.log(`VIP rescheduled queue will be in position ${position} (after serving person and existing VIPs)`);
        }
      } else {
        // Normal user: position after all existing queues
        position = totalCount + 1;
        console.log(`Normal rescheduled queue will be in position ${position} (after all existing queues)`);
      }
    }

    return position;
  }

  async rescheduleQueue(rescheduleQueueDto: RescheduleQueueDto): Promise<Queue> {
    const {
      oldQueueId,
      serviceId,
      userId,
      userName,
      mobileNumber,
      date,
      timeSlot,
      amount,
      isVIP: requestIsVIP,
      hasSubUnits,
      subUnitId,
      subUnitName
    } = rescheduleQueueDto;

    console.log(`Starting reschedule process for queue ${oldQueueId} to new date ${date} and time ${timeSlot}`);

    // First, get the old queue to validate it exists and belongs to the user
    const oldQueue = await this.queueRepository.findOne({
      where: { id: Number(oldQueueId), userId },
      relations: ['service'],
    });

    if (!oldQueue) {
      throw new NotFoundException(`Queue with ID ${oldQueueId} not found for this user`);
    }

    // Check if the old queue is in a valid state for rescheduling
    if (oldQueue.status === 'completed' || oldQueue.status === 'cancelled') {
      throw new BadRequestException(`Cannot reschedule a ${oldQueue.status} queue`);
    }

    // Check if service exists
    const service = await this.serviceRepository.findOne({
      where: { id: Number(serviceId) },
    });

    if (!service) {
      throw new NotFoundException(`Service with ID ${serviceId} not found`);
    }

    // Try to find user by Clerk ID first (which is passed as userId)
    let dbUserId: number | null = null;
    let isVIP = requestIsVIP;

    try {
      // First check if this is a clerk ID
      const userByClerkId = await this.findUserByClerkId(userId);

      if (userByClerkId) {
        // If found, use the database ID as the reference
        dbUserId = userByClerkId.id;
        isVIP = requestIsVIP ?? userByClerkId.isVIP;
      } else if (userId.includes('@')) {
        // If it looks like an email, try to find by email
        const userByEmail = await this.userRepository.findOne({ where: { email: userId } });
        if (userByEmail) {
          dbUserId = userByEmail.id;
          isVIP = requestIsVIP ?? userByEmail.isVIP;
        }
      }
    } catch (error) {
      console.error('Error finding user for reschedule:', error);
    }

    // Check if user already has a queue entry for the new service, date and time slot
    const existingQueue = await this.queueRepository.findOne({
      where: {
        userId,
        serviceId: Number(serviceId),
        date: new Date(date),
        timeSlot,
        status: 'waiting',
      },
    });

    if (existingQueue && existingQueue.id !== oldQueue.id) {
      throw new BadRequestException('You already have a queue entry for this service, date and time slot');
    }

    // Calculate position for the new time slot using the same logic as joinQueue
    const position = await this.calculatePositionForReschedule(
      Number(serviceId),
      date,
      timeSlot,
      isVIP || false,
      oldQueue.id
    );

    console.log(`Calculated new position ${position} for rescheduled queue`);

    // Update the old queue with new details
    oldQueue.date = new Date(date);
    oldQueue.timeSlot = timeSlot;
    oldQueue.position = position;
    oldQueue.initialPositionAtJoin = position;
    oldQueue.status = 'waiting'; // Reset status to waiting
    oldQueue.hasSubUnits = hasSubUnits || false;
    oldQueue.subUnitId = subUnitId || '';
    oldQueue.subUnitName = subUnitName || '';

    // Get service setup for serve time
    const serviceSetup = await this.serviceSetupRepository.findOne({
      where: { service: { id: Number(serviceId) } },
      relations: ['service'],
    });

    // Set serve time from service setup or default
    if (hasSubUnits && subUnitId && serviceSetup?.setupData?.subUnits) {
      const subUnit = serviceSetup.setupData.subUnits.find((unit: any) => unit.name === subUnitName);
      if (subUnit?.avgServeTime) {
        oldQueue.serveTime = parseInt(subUnit.avgServeTime, 10);
        console.log(`Setting serve time from subunit: ${oldQueue.serveTime} minutes`);
      }
    } else if (serviceSetup?.setupData?.servingTime) {
      oldQueue.serveTime = parseInt(serviceSetup.setupData.servingTime, 10);
      console.log(`Setting serve time from service: ${oldQueue.serveTime} minutes`);
    } else {
      oldQueue.serveTime = 15; // Default
      console.log(`No serve time found for service, using default: 15 minutes`);
    }

    // Save updated queue to database
    const savedQueue = await this.queueRepository.save(oldQueue) as Queue;

    console.log(`Queue ${savedQueue.id} rescheduled successfully`);

    // Calculate estimated serve time for the rescheduled queue
    try {
      const queueFlowService = await this.getQueueFlowService();
      if (queueFlowService) {
        console.log(`Calculating estimated serve time for rescheduled queue ${savedQueue.id}`);
        await queueFlowService.calculateEstimatedServeTime(savedQueue.id);

        // Also recalculate for all queues in this service to ensure consistency
        await queueFlowService.recalculateEstimatedServeTimes(
          savedQueue.serviceId,
          savedQueue.date,
          savedQueue.timeSlot
        );
      }
    } catch (error) {
      console.error(`Error calculating estimated serve time for rescheduled queue ${savedQueue.id}:`, error);
    }

    // Update Redis cache
    const dateStr = new Date(date).toISOString().split('T')[0];
    const oldDateStr = oldQueue.date instanceof Date
      ? oldQueue.date.toISOString().split('T')[0]
      : new Date(oldQueue.date).toISOString().split('T')[0];

    // Prepare queue data for Redis
    const queueDataForRedis = {
      ...savedQueue,
      amount,
      mobileNumber,
      serviceName: service.serviceName,
      serviceType: service.serviceType,
      uniqueSlotId: oldQueue.uniqueSlotId || Math.floor(1000 + Math.random() * 9000).toString(),
      hasSubUnits: hasSubUnits || false,
      subUnitId: subUnitId || null,
      subUnitName: subUnitName || null,
      position,
      initialPositionAtJoin: position,
      serveTime: savedQueue.serveTime
    };

    // Only add dbUserId if it's not null
    if (dbUserId !== null) {
      Object.assign(queueDataForRedis, { dbUserId });
    }

    // Update queue in Redis
    await this.redisService.saveQueue(savedQueue.id.toString(), queueDataForRedis);

    // Update position data in Redis for new time slot
    const newPositionsKey = `service:${serviceId}:positions:${dateStr}:${timeSlot}`;
    const newPositionsData = await this.redisService.get(newPositionsKey) || {};
    newPositionsData[savedQueue.id] = position;
    await this.redisService.set(newPositionsKey, newPositionsData);
    await this.redisService.saveQueuePosition(serviceId.toString(), dateStr, timeSlot, newPositionsData);

    // Invalidate caches for both old and new dates/times
    const cacheKeysToInvalidate = [
      `service:${serviceId}:active-queues:${oldDateStr}`,
      `service:${serviceId}:active-queues:${dateStr}`,
      `service:${serviceId}:active-queues:all`,
      `service:${serviceId}:queue-counts:${oldDateStr}`,
      `service:${serviceId}:queue-counts:${dateStr}`,
      `service:${serviceId}:position:${oldDateStr}:${oldQueue.timeSlot}`,
      `service:${serviceId}:position:${dateStr}:${timeSlot}`
    ];

    for (const key of cacheKeysToInvalidate) {
      await this.redisService.del(key);
    }

    // Invalidate service-level queues cache
    await this.redisService.invalidateServiceQueues(Number(serviceId), oldDateStr);
    await this.redisService.invalidateServiceQueues(Number(serviceId), dateStr);
    await this.redisService.invalidateServiceQueues(Number(serviceId), 'all');

    console.log(`Invalidated Redis caches for reschedule operation`);

    return savedQueue as unknown as Queue;
  }

  async getUserQueues(userId: string): Promise<any> {
    // Get queues from database
    const queues = await this.queueRepository.find({
      where: { userId },
      relations: ['service'],
      order: { createdAt: 'DESC' },
    });

    // Define queue data type for arrays
    interface QueueData {
      id: number;
      serviceId: number;
      serviceName: string;
      serviceType: string;
      date: Date;
      timeSlot: string;
      status: string;
      isVIP: boolean;
      createdAt: Date;
      uniqueSlotId?: string; // Make uniqueSlotId optional
      hasSubUnits?: boolean; // Add subunit information
      subUnitId?: string;
      subUnitName?: string;
    }

    // Group queues by status
    const upcoming: QueueData[] = [];
    const completed: QueueData[] = [];
    const cancelled: QueueData[] = [];
    const noShow: QueueData[] = [];

    // Current date for comparison
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    // Process each queue to add enhanced details
    for (const queue of queues) {
      // Try to get data from Redis first to get uniqueSlotId and subunit information
      const redisKey = `queue:${queue.id}`;
      const redisData = await this.redisService.get(redisKey);

      // Use type assertion to access uniqueSlotId property
      let uniqueSlotId = (queue as any).uniqueSlotId;
      let hasSubUnits = queue.hasSubUnits || false;
      let subUnitId = queue.subUnitId || '';
      let subUnitName = queue.subUnitName || '';

      // If Redis has data, use it for uniqueSlotId and subunit information
      if (redisData && typeof redisData === 'object') {
        if ('uniqueSlotId' in redisData) {
          uniqueSlotId = redisData.uniqueSlotId;
        }

        // Get subunit information from Redis if available
        if ('hasSubUnits' in redisData) {
          hasSubUnits = redisData.hasSubUnits;
        }
        if ('subUnitId' in redisData) {
          subUnitId = redisData.subUnitId;
        }
        if ('subUnitName' in redisData) {
          subUnitName = redisData.subUnitName;
        }
      }

      // If still no uniqueSlotId, generate a fallback
      if (!uniqueSlotId) {
        uniqueSlotId = Math.floor(1000 + Math.random() * 9000).toString();
      }

      // Log subunit information for debugging
      if (hasSubUnits || subUnitId || subUnitName) {
        console.log(`Queue ${queue.id} has subunit: ${subUnitName} (ID: ${subUnitId}, hasSubUnits: ${hasSubUnits})`);
      }

      // Format the queue date for comparison
      const queueDate = queue.date instanceof Date
        ? queue.date.toISOString()
        : typeof queue.date === 'string'
          ? queue.date
          : new Date().toISOString();

      const queueDateStr = queueDate.split('T')[0];

      // Get the end time of the queue from timeSlot
      let timeEnd = '';
      if (queue.timeSlot && queue.timeSlot.includes(' - ')) {
        timeEnd = queue.timeSlot.split(' - ')[1];
      } else if (queue.timeSlot) {
        timeEnd = queue.timeSlot;
      } else {
        timeEnd = '23:59';
      }

      // Now determine if this queue has ended
      let actualStatus = queue.status;

      // If queue is still 'waiting' but time slot has ended, mark as completed or no-show
      if (queue.status === 'waiting' && this.hasTimeSlotEnded(queue.date, queue.timeSlot)) {
        // Determine if this was a no-show (not checked in) or a completed service
        actualStatus = queue.isCheckedIn ? 'completed' : 'no-show';

        // Actually update in the database
        queue.status = actualStatus;
        await this.queueRepository.save(queue);

        // Update Redis if we found data
        if (redisData) {
          await this.redisService.updateQueueStatus(queue.id.toString(), actualStatus);
        }
      }

      const queueData: QueueData = {
        id: queue.id,
        serviceId: queue.serviceId,
        serviceName: queue.service?.serviceName || 'Unknown Service',
        serviceType: queue.service?.serviceType || 'Unknown Type',
        date: queue.date,
        timeSlot: queue.timeSlot,
        status: actualStatus, // Use the actual status, not the DB status
        isVIP: queue.isVIP,
        createdAt: queue.createdAt,
        uniqueSlotId, // Add uniqueSlotId to the response
        hasSubUnits, // Add subunit information
        subUnitId,
        subUnitName
      };

      // Categorize based on the actual status
      if (actualStatus === 'waiting' || actualStatus === 'checked-in' || actualStatus === 'serving') {
        upcoming.push(queueData);
      } else if (actualStatus === 'completed') {
        completed.push(queueData);
      } else if (actualStatus === 'cancelled') {
        cancelled.push(queueData);
      } else if (actualStatus === 'no-show') {
        noShow.push(queueData);
      }
    }

    return {
      upcoming,
      completed,
      cancelled,
      noShow
    };
  }

  async getQueuesByStatus(userId: string, status: string): Promise<any[]> {
    // Validate status parameter
    if (!['upcoming', 'completed', 'cancelled', 'no-show', 'serving'].includes(status)) {
      throw new BadRequestException('Invalid status parameter. Must be one of: upcoming, completed, cancelled, no-show, serving');
    }

    // Map the frontend status names to backend status values
    const statusMap = {
      'upcoming': 'waiting',
      'completed': 'completed',
      'cancelled': 'cancelled',
      'no-show': 'no-show',
      'serving': 'serving'
    };

    // Current date for comparison
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    // Get queues from database with the specified status
    let queues = await this.queueRepository.find({
      where: { userId, status: statusMap[status] },
      relations: ['service'],
      order: { createdAt: status === 'upcoming' ? 'ASC' : 'DESC' }, // ASC for upcoming, DESC for history
    });

    // If requesting upcoming queues, we need to filter out past queues
    if (status === 'upcoming') {
      // Find all 'waiting' queues but filter out past dates and ended time slots
      const filteredQueues: Queue[] = [];
      for (const queue of queues) {
        // If queue date is today or in the future and the time slot hasn't ended, keep it
        if (!this.hasTimeSlotEnded(queue.date, queue.timeSlot)) {
          filteredQueues.push(queue);
        } else {
          // Auto-update past queues that are still in 'waiting' status
          const newStatus = queue.isCheckedIn ? 'completed' : 'no-show';
          queue.status = newStatus;
          await this.queueRepository.save(queue);

          // Update Redis if available
          const redisKey = `queue:${queue.id}`;
          const redisData = await this.redisService.get(redisKey);
          if (redisData) {
            await this.redisService.updateQueueStatus(queue.id.toString(), newStatus);
          }
        }
      }
      queues = filteredQueues;
    }

    // If requesting completed queues, also include past queues that are still marked as waiting
    if (status === 'completed') {
      // Find all 'waiting' queues that have past dates
      const pastQueues = await this.queueRepository.find({
        where: { userId, status: 'waiting' },
        relations: ['service'],
      });

      const additionalCompletedQueues: Queue[] = [];
      for (const queue of pastQueues) {
        // Check if time slot has ended and user checked in
        if (this.hasTimeSlotEnded(queue.date, queue.timeSlot) && queue.isCheckedIn) {
          // Update status before adding to results
          queue.status = 'completed';
          await this.queueRepository.save(queue);

          // Update Redis if available
          const redisKey = `queue:${queue.id}`;
          const redisData = await this.redisService.get(redisKey);
          if (redisData) {
            await this.redisService.updateQueueStatus(queue.id.toString(), 'completed');
          }

          additionalCompletedQueues.push(queue);
        }
      }

      // Add these auto-completed queues to our results
      queues = [...queues, ...additionalCompletedQueues];
    }

    // If requesting no-show queues, also include past queues that were not checked in
    if (status === 'no-show') {
      // Find all 'waiting' queues that have past dates
      const pastQueues = await this.queueRepository.find({
        where: { userId, status: 'waiting' },
        relations: ['service'],
      });

      const additionalNoShowQueues: Queue[] = [];
      for (const queue of pastQueues) {
        // Check if time slot has ended and user did NOT check in
        if (this.hasTimeSlotEnded(queue.date, queue.timeSlot) && !queue.isCheckedIn) {
          // Update status before adding to results
          queue.status = 'no-show';
          await this.queueRepository.save(queue);

          // Update Redis if available
          const redisKey = `queue:${queue.id}`;
          const redisData = await this.redisService.get(redisKey);
          if (redisData) {
            await this.redisService.updateQueueStatus(queue.id.toString(), 'no-show');
          }

          additionalNoShowQueues.push(queue);
        }
      }

      // Add these auto-detected no-show queues to our results
      queues = [...queues, ...additionalNoShowQueues];
    }

    // Enhance queue data with uniqueSlotId and subunit information from Redis
    return Promise.all(queues.map(async queue => {
      // Try to get data from Redis first to get uniqueSlotId and subunit information
      const redisKey = `queue:${queue.id}`;
      const redisData = await this.redisService.get(redisKey);

      // Use type assertion to access uniqueSlotId property
      let uniqueSlotId = (queue as any).uniqueSlotId;
      let hasSubUnits = queue.hasSubUnits || false;
      let subUnitId = queue.subUnitId || '';
      let subUnitName = queue.subUnitName || '';

      // If Redis has data, use it for uniqueSlotId and subunit information
      if (redisData && typeof redisData === 'object') {
        if ('uniqueSlotId' in redisData) {
          uniqueSlotId = redisData.uniqueSlotId;
        }

        // Get subunit information from Redis if available
        if ('hasSubUnits' in redisData) {
          hasSubUnits = redisData.hasSubUnits;
        }
        if ('subUnitId' in redisData) {
          subUnitId = redisData.subUnitId;
        }
        if ('subUnitName' in redisData) {
          subUnitName = redisData.subUnitName;
        }
      }

      // If still no uniqueSlotId, generate a fallback
      if (!uniqueSlotId) {
        uniqueSlotId = Math.floor(1000 + Math.random() * 9000).toString();
      }

      // Log subunit information for debugging
      if (hasSubUnits || subUnitId || subUnitName) {
        console.log(`Queue ${queue.id} has subunit: ${subUnitName} (ID: ${subUnitId}, hasSubUnits: ${hasSubUnits})`);
      }

      return {
        id: queue.id,
        serviceId: queue.serviceId,
        serviceName: queue.service?.serviceName || 'Unknown Service',
        serviceType: queue.service?.serviceType || 'Unknown Type',
        date: queue.date,
        timeSlot: queue.timeSlot,
        status: queue.status,
        isVIP: queue.isVIP,
        createdAt: queue.createdAt,
        uniqueSlotId,
        // Include subunit information
        hasSubUnits,
        subUnitId,
        subUnitName
      };
    }));
  }

  async getActiveUserQueues(userId: string): Promise<any[]> {
    // Get only active (waiting) queues
    const queues = await this.queueRepository.find({
      where: { userId, status: 'waiting' },
      relations: ['service'],
      order: { createdAt: 'ASC' },
    });

    // Current date for comparison
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    // Filter out past queues
    const activeQueues: Queue[] = [];

    for (const queue of queues) {
      // If the queue date is today or in the future and the time slot hasn't ended, it's active
      if (!this.hasTimeSlotEnded(queue.date, queue.timeSlot)) {
        activeQueues.push(queue);
      } else {
        // Past queue - update status to completed
        queue.status = 'completed';
        await this.queueRepository.save(queue);

        // Update Redis if available
        const redisKey = `queue:${queue.id}`;
        const redisData = await this.redisService.get(redisKey);
        if (redisData) {
          await this.redisService.updateQueueStatus(queue.id.toString(), 'completed');
        }
      }
    }

    // Try to get enhanced data from Redis first
    const enhancedQueues = await Promise.all(
      activeQueues.map(async queue => {
        const redisKey = `queue:${queue.id}`;
        const redisData = await this.redisService.get(redisKey);

        if (redisData) {
          return redisData;
        }

        // Fall back to database data
        return {
          id: queue.id,
          serviceId: queue.serviceId,
          serviceName: queue.service.serviceName,
          serviceType: queue.service.serviceType,
          date: queue.date,
          timeSlot: queue.timeSlot,
          status: queue.status,
          isVIP: queue.isVIP,
          createdAt: queue.createdAt,
          uniqueSlotId: (queue as any).uniqueSlotId || Math.floor(1000 + Math.random() * 9000).toString(), // Add uniqueSlotId
          // Include subunit information
          hasSubUnits: queue.hasSubUnits || false,
          subUnitId: queue.subUnitId || '',
          subUnitName: queue.subUnitName || ''
        };
      })
    );

    return enhancedQueues;
  }

  // Get active queues for a specific service and date, excluding cancelled queues
  async getServiceActiveQueues(serviceId: number, date: string): Promise<any[]> {
    try {
      // First, check if we have cached data in Redis
      const redisKey = `service:${serviceId}:active-queues:${date}`;
      const cachedQueues = await this.redisService.get(redisKey);

      if (cachedQueues) {
        console.log(`Found ${cachedQueues.length} cached queues for service ${serviceId} on date ${date} in Redis`);

        // Filter out non-active statuses and ended time slots
        const filteredQueues: any[] = [];
        const queueIdsToUpdate: number[] = [];

        for (const queue of cachedQueues) {
          // Check for active status: only 'waiting', 'checked-in', or 'serving' are considered active
          const isActiveStatus = queue.status === 'waiting' || queue.status === 'checked-in' || queue.status === 'serving';

          if (!isActiveStatus) {
            // Skip non-active statuses
            continue;
          }

          if (this.hasTimeSlotEnded(queue.date, queue.timeSlot)) {
            // This queue's time slot has ended, collect its ID for updating
            queueIdsToUpdate.push(queue.id);
          } else {
            // Time slot hasn't ended and status is active, keep in active queues
            filteredQueues.push(queue);
          }
        }

        // If we filtered any queues, update the cache with the filtered data
        if (filteredQueues.length !== cachedQueues.length) {
          await this.redisService.set(redisKey, filteredQueues, { ex: 120 });
          console.log(`Filtered out ${cachedQueues.length - filteredQueues.length} ended time slots or non-active queues from cached data`);

          // Update the status of ended queues asynchronously
          if (queueIdsToUpdate.length > 0) {
            this.updateQueueStatusByIds(queueIdsToUpdate, 'completed', serviceId, date);
          }
        }

        return filteredQueues;
      }

      // If no cached data, query the database
      // Create where clause, handling 'all' dates
      const whereClause: any = {
        serviceId,
        status: In(['waiting', 'checked-in', 'serving']) // Only include active statuses
      };

      // Only add date filter if not requesting all dates
      if (date !== 'all') {
        whereClause.date = new Date(date);
      }

      // Get active queues from database
      const queues = await this.queueRepository.find({
        where: whereClause,
        order: { createdAt: 'ASC' },
      });

      console.log(`Found ${queues.length} active queues for service ${serviceId} in database`);

      // Filter out queues with ended time slots and update their status
      const filteredQueues: Queue[] = [];
      const queueIdsToUpdate: number[] = [];

      for (const queue of queues) {
        if (this.hasTimeSlotEnded(queue.date, queue.timeSlot)) {
          queueIdsToUpdate.push(queue.id);
        } else {
          filteredQueues.push(queue);
        }
      }

      // Update the status of ended queues asynchronously
      if (queueIdsToUpdate.length > 0) {
        this.updateQueueStatusByIds(queueIdsToUpdate, 'completed', serviceId, date);
      }

      // Format the queue data
      const activeQueues = filteredQueues.map(queue => ({
        id: queue.id,
        serviceId: queue.serviceId,
        timeSlot: queue.timeSlot,
        status: queue.status,
        isVIP: queue.isVIP,
        userId: queue.userId,
        date: queue.date,
        createdAt: queue.createdAt,
        // Include subunit information
        hasSubUnits: queue.hasSubUnits || false,
        subUnitId: queue.subUnitId || '',
        subUnitName: queue.subUnitName || ''
      }));

      // Cache the result in Redis for 2 minutes
      await this.redisService.set(redisKey, activeQueues, { ex: 120 });

      return activeQueues;
    } catch (error) {
      console.error(`Error fetching active queues for service ${serviceId} on date ${date}:`, error);
      throw new Error(`Failed to fetch active queues: ${error.message}`);
    }
  }

  // Enhanced getQueueById method with Redis
  async getQueueById(queueId: number | string, userId?: string): Promise<any> {
    const queueIdNumber = typeof queueId === 'string' ? parseInt(queueId, 10) : queueId;

    let query = this.queueRepository
      .createQueryBuilder('queue')
      .leftJoinAndSelect('queue.service', 'service')
      .where('queue.id = :queueId', { queueId: queueIdNumber });

    // If userId provided, add it to the query
    if (userId) {
      query = query.andWhere('queue.userId = :userId', { userId });
    }

    const queue = await query.getOne();

    if (!queue) {
      return null;
    }

    // Try to get data from Redis for enhanced info
    const redisKey = `queue:${queueIdNumber}`;
    const redisData = await this.redisService.get(redisKey);

    // If Redis data exists, merge with database data
    if (redisData) {
      return {
        ...queue,
        ...redisData,
        // Keep original properties that should come from DB
        id: queue.id,
        serviceId: queue.serviceId,
        service: queue.service
      };
    }

    return queue;
  }

  // Check in a user to a queue
  async checkInQueue(queueId: string, userId: string): Promise<any> {
    try {
      const queue = await this.queueRepository.findOne({
        where: { id: Number(queueId), userId },
        relations: ['service'],
      });

      if (!queue) {
        throw new NotFoundException(`Queue with ID ${queueId} not found for this user`);
      }

      // Update queue isCheckedIn status only
      queue.isCheckedIn = true;

      // Ensure status remains 'waiting' if not already completed or cancelled
      if (queue.status !== 'completed' && queue.status !== 'cancelled') {
        queue.status = 'waiting';
      }

      await this.queueRepository.save(queue);

      // Update the queue data in Redis immediately with the new isCheckedIn flag
      const queueData = await this.redisService.getQueue(queueId);
      if (queueData) {
        queueData.isCheckedIn = true;

        // Ensure status remains 'waiting' if not already completed or cancelled
        if (queueData.status !== 'completed' && queueData.status !== 'cancelled') {
          queueData.status = 'waiting';
        }

        await this.redisService.saveQueue(queueId, queueData, 300); // 5 minutes TTL
      }

      // Make sure to invalidate all related Redis cache
      if (queue.date) {
        // Get date in correct format for Redis keys
        const dateStr = (() => {
          if (!queue.date) {
            return new Date().toISOString().split('T')[0];
          }
          return new Date(queue.date).toISOString().split('T')[0];
        })();

        // Invalidate service-level active queue lists
        await this.redisService.invalidateServiceQueues(queue.serviceId, dateStr);

        // Also invalidate the 'all' date key which is used by mobile app endpoints
        await this.redisService.invalidateServiceQueues(queue.serviceId, 'all');

        // Invalidate queue counts cache
        const queueCountsKey = `service:${queue.serviceId}:queue-counts:${dateStr}`;
        await this.redisService.del(queueCountsKey);

        // Also invalidate service active queues cache for both specific date and 'all'
        const activeQueuesKey = `service:${queue.serviceId}:active-queues:${dateStr}`;
        const allActiveQueuesKey = `service:${queue.serviceId}:active-queues:all`;
        await this.redisService.del(activeQueuesKey);
        await this.redisService.del(allActiveQueuesKey);
      }

      return {
        status: 'success',
        message: 'Successfully checked in',
        queue: {
          id: queue.id,
          status: queue.status,
          isCheckedIn: queue.isCheckedIn
        }
      };
    } catch (error) {
      throw new Error(`Failed to check in: ${error.message}`);
    }
  }

  // New method to toggle check-in status
  async toggleCheckInQueue(queueId: string, userId: string): Promise<any> {
    try {
      // Find the queue
      const queue = await this.queueRepository.findOne({
        where: { id: Number(queueId) },
        relations: ['service'],
      });

      if (!queue) {
        throw new Error('Queue not found');
      }

      // Validate the user owns this queue
      if (queue.userId !== userId) {
        throw new Error('Unauthorized: This queue does not belong to you');
      }

      // Toggle the isCheckedIn status
      queue.isCheckedIn = !queue.isCheckedIn;

      // Save the updated queue
      const savedQueue = await this.queueRepository.save(queue);

      // For Redis updates
      const date = queue.date instanceof Date
        ? queue.date.toISOString().split('T')[0]
        : queue.date;

      // Update the queue data in Redis with the new check-in status
      const queueData = await this.redisService.getQueue(queueId);
      if (queueData) {
        queueData.isCheckedIn = queue.isCheckedIn;
        await this.redisService.saveQueue(queueId, queueData, 300); // 5 minutes TTL

        // Also explicitly update the check-in status
        await this.redisService.updateQueueCheckInStatus(queueId, queue.isCheckedIn);
      }

      return {
        status: 'success',
        message: queue.isCheckedIn ? 'Successfully checked in' : 'Check-in removed',
        data: {
          queueId: savedQueue.id,
          isCheckedIn: savedQueue.isCheckedIn,
        },
      };
    } catch (error) {
      throw new Error(error.message || 'Failed to toggle check-in status');
    }
  }

  // Cancel a queue
  async cancelQueue(queueId: string, userId: string): Promise<any> {
    try {
      const queue = await this.queueRepository.findOne({
        where: { id: Number(queueId), userId },
      });

      if (!queue) {
        throw new NotFoundException(`Queue with ID ${queueId} not found for this user`);
      }

      // Update queue status
      queue.status = 'cancelled';
      await this.queueRepository.save(queue);

      // Update the queue data in Redis immediately with the new status
      const queueData = await this.redisService.getQueue(queueId);
      if (queueData) {
        queueData.status = 'cancelled';
        await this.redisService.saveQueue(queueId, queueData, 300); // 5 minutes TTL

        // Also explicitly update the status
        await this.redisService.updateQueueStatus(queueId, 'cancelled');
      }

      // Make sure to invalidate all related Redis cache
      if (queue.date) {
        // Get date in correct format for Redis keys
        const dateStr = (() => {
          if (!queue.date) {
            return new Date().toISOString().split('T')[0];
          }
          return new Date(queue.date).toISOString().split('T')[0];
        })();

        // Invalidate service-level active queue lists
        await this.redisService.invalidateServiceQueues(queue.serviceId, dateStr);

        // Also invalidate the 'all' date key which is used by mobile app endpoints
        await this.redisService.invalidateServiceQueues(queue.serviceId, 'all');

        // Invalidate queue counts cache
        const queueCountsKey = `service:${queue.serviceId}:queue-counts:${dateStr}`;
        await this.redisService.del(queueCountsKey);

        // Also invalidate service active queues cache for both specific date and 'all'
        const activeQueuesKey = `service:${queue.serviceId}:active-queues:${dateStr}`;
        const allActiveQueuesKey = `service:${queue.serviceId}:active-queues:all`;
        await this.redisService.del(activeQueuesKey);
        await this.redisService.del(allActiveQueuesKey);
      }

      return {
        status: 'success',
        message: 'Successfully cancelled queue',
        queue: {
          id: queue.id,
          status: queue.status
        }
      };
    } catch (error) {
      throw new Error(`Failed to cancel queue: ${error.message}`);
    }
  }

  // Helper method to get queue position
  private async getQueuePosition(queue: Queue): Promise<number> {
    try {
      // Format the date string
      const dateStr = (() => {
        if (!queue.date) {
          return new Date().toISOString().split('T')[0];
        }
        return new Date(queue.date).toISOString().split('T')[0];
      })();

      // First check if we have position data in Redis
      const positionData = await this.redisService.getQueuePosition(
        queue.serviceId,
        dateStr,
        queue.timeSlot
      );

      if (positionData && positionData[queue.id]) {
        return positionData[queue.id];
      }

      // If not in Redis, calculate from database
      const queuesForSlot = await this.queueRepository.find({
        where: {
          serviceId: queue.serviceId,
          date: queue.date,
          timeSlot: queue.timeSlot,
          status: In(['waiting', 'checked-in']),
        },
        order: {
          isVIP: 'DESC', // VIP first
          createdAt: 'ASC' // Then by creation time
        }
      });

      // Calculate position and store all positions
      const positions: Record<string, number> = {};
      let position = 0;

      for (const q of queuesForSlot) {
        position++;
        positions[q.id] = position;

        // Once we find our queue, we can stop counting
        if (q.id === queue.id) {
          break;
        }
      }

      // Store all positions in Redis for future reference
      await this.redisService.saveQueuePosition(
        queue.serviceId,
        dateStr,
        queue.timeSlot,
        positions
      );

      return positions[queue.id] || 0;
    } catch (error) {
      console.error('Error getting queue position:', error);
      return 0; // Default position if we can't calculate
    }
  }

  // Complete a queue or mark as no-show based on isCheckedIn status
  async completeQueue(queueId: string, userId: string, isCheckedIn?: boolean): Promise<any> {
    try {
      const queue = await this.queueRepository.findOne({
        where: { id: Number(queueId), userId },
      });

      if (!queue) {
        throw new NotFoundException(`Queue with ID ${queueId} not found for this user`);
      }

      // If isCheckedIn is explicitly provided, use it; otherwise, use the queue's current value
      const checkedIn = isCheckedIn !== undefined ? isCheckedIn : queue.isCheckedIn;

      // Determine status based on check-in status
      const newStatus = checkedIn ? 'completed' : 'no-show';
      console.log(`Marking queue ${queueId} as ${newStatus} (isCheckedIn: ${checkedIn})`);

      // Update queue status
      queue.status = newStatus;
      await this.queueRepository.save(queue);

      // Update the queue data in Redis immediately with the new status
      const queueData = await this.redisService.getQueue(queueId);
      if (queueData) {
        queueData.status = newStatus;
        queueData.isCheckedIn = checkedIn;
        await this.redisService.saveQueue(queueId, queueData, 300); // 5 minutes TTL

        // Also explicitly update the status
        await this.redisService.updateQueueStatus(queueId, newStatus);
      }

      // Make sure to invalidate all related Redis cache
      if (queue.date) {
        // Get date in correct format for Redis keys
        const dateStr = (() => {
          if (!queue.date) {
            return new Date().toISOString().split('T')[0];
          }
          return new Date(queue.date).toISOString().split('T')[0];
        })();

        // Invalidate service-level active queue lists
        await this.redisService.invalidateServiceQueues(queue.serviceId, dateStr);

        // Also invalidate the 'all' date key which is used by mobile app endpoints
        await this.redisService.invalidateServiceQueues(queue.serviceId, 'all');

        // Invalidate queue counts cache
        const queueCountsKey = `service:${queue.serviceId}:queue-counts:${dateStr}`;
        await this.redisService.del(queueCountsKey);

        // Also invalidate service active queues cache for both specific date and 'all'
        const activeQueuesKey = `service:${queue.serviceId}:active-queues:${dateStr}`;
        const allActiveQueuesKey = `service:${queue.serviceId}:active-queues:all`;
        await this.redisService.del(activeQueuesKey);
        await this.redisService.del(allActiveQueuesKey);
      }

      return {
        status: 'success',
        message: 'Successfully completed queue',
        queue: {
          id: queue.id,
          status: queue.status
        }
      };
    } catch (error) {
      throw new Error(`Failed to complete queue: ${error.message}`);
    }
  }

  // Get queue counts for a service by date for all time slots (lightweight endpoint)
  async getServiceQueueCounts(serviceId: number, date: string, subUnitId?: string): Promise<Record<string, { normalCount: number; vipCount: number }>> {
    try {
      // Create a Redis key that includes the subUnitId if provided
      const redisKey = subUnitId
        ? `service:${serviceId}:queue-counts:${date}:subunit:${subUnitId}`
        : `service:${serviceId}:queue-counts:${date}`;

      // Check if we have cached data first
      const cachedCounts = await this.redisService.get(redisKey);
      if (cachedCounts) {
        console.log('Queue counts from Redis cache:', redisKey);
        return cachedCounts;
      }

      // If no cache, calculate counts from active queues
      const activeQueues = await this.getServiceActiveQueues(serviceId, date);

      // Filter by subUnitId if provided
      let filteredQueues = activeQueues;
      if (subUnitId !== undefined) {
        console.log(`Filtering queue counts by subUnitId: ${subUnitId}`);
        filteredQueues = activeQueues.filter(queue =>
          queue.hasSubUnits && queue.subUnitId === subUnitId
        );
        console.log(`After filtering, found ${filteredQueues.length} queues for subUnitId ${subUnitId}`);
      }

      // Group by timeSlot and calculate counts
      const timeSlotCounts: Record<string, { normalCount: number; vipCount: number }> = {};

      // Check if any time slots have ended since the user started their session
      // This ensures we don't count queues in time slots that have already passed
      let needsQueueStatusUpdate = false;
      const queueIdsToUpdate: number[] = [];

      if (Array.isArray(filteredQueues)) {
        for (const queue of filteredQueues) {
          if (!queue.timeSlot) continue;

          // Skip queues with ended time slots and mark them for updating
          if (this.hasTimeSlotEnded(queue.date, queue.timeSlot)) {
            needsQueueStatusUpdate = true;
            queueIdsToUpdate.push(queue.id);
            continue;
          }

          if (!timeSlotCounts[queue.timeSlot]) {
            timeSlotCounts[queue.timeSlot] = {
              normalCount: 0,
              vipCount: 0
            };
          }

          if (queue.isVIP) {
            timeSlotCounts[queue.timeSlot].vipCount++;
          } else {
            timeSlotCounts[queue.timeSlot].normalCount++;
          }
        }
      }

      // If we found any ended time slots, update their status asynchronously
      if (needsQueueStatusUpdate && queueIdsToUpdate.length > 0) {
        this.updateQueueStatusByIds(queueIdsToUpdate, 'completed', serviceId, date);
      }

      // Cache the counts for 30 seconds for very fast subsequent access
      await this.redisService.set(redisKey, timeSlotCounts, { ex: 30 });

      return timeSlotCounts;
    } catch (error) {
      console.error(`Error getting queue counts for service ${serviceId} on date ${date}:`, error);
      return {};
    }
  }

  // Add this method after the getServiceActiveQueues method
  async getServiceQueuesByStatus(serviceId: number, date: string, status: string): Promise<any[]> {
    try {
      // First, check if we have cached data in Redis
      const redisKey = `service:${serviceId}:${status}-queues:${date}`;
      const cachedQueues = await this.redisService.get(redisKey);

      if (cachedQueues) {
        console.log(`Found ${cachedQueues.length} cached ${status} queues for service ${serviceId} in Redis`);
        return cachedQueues;
      }

      // If no cached data, query the database
      // Create where clause, handling 'all' dates
      const whereClause: any = {
        serviceId,
        status
      };

      // Only add date filter if not requesting all dates
      if (date !== 'all') {
        whereClause.date = new Date(date);
      }

      // Get queues for this service with the specified status
      const queues = await this.queueRepository.find({
        where: whereClause,
        order: { createdAt: 'ASC' },
      });

      console.log(`Found ${queues.length} ${status} queues for service ${serviceId} in database`);

      // Format the queue data
      const filteredQueues = queues.map(queue => ({
        id: queue.id,
        serviceId: queue.serviceId,
        timeSlot: queue.timeSlot,
        status: queue.status,
        isVIP: queue.isVIP,
        userId: queue.userId,
        date: queue.date,
        createdAt: queue.createdAt,
        position: 0, // Will be calculated on frontend
        // Include subunit information
        hasSubUnits: queue.hasSubUnits || false,
        subUnitId: queue.subUnitId || '',
        subUnitName: queue.subUnitName || ''
      }));

      // Cache the result in Redis for 2 minutes
      await this.redisService.set(redisKey, filteredQueues, { ex: 120 });

      return filteredQueues;
    } catch (error) {
      console.error(`Error fetching ${status} queues for service ${serviceId} on date ${date}:`, error);
      throw new Error(`Failed to fetch ${status} queues: ${error.message}`);
    }
  }

  // Add this method after getServiceQueuesByStatus
  async updateExpiredQueues(): Promise<{updated: number, message: string}> {
    try {
      // Get all waiting queues
      const waitingQueues = await this.queueRepository.find({
        where: { status: 'waiting' },
        relations: ['service']
      });

      console.log(`Found ${waitingQueues.length} waiting queues to check for expiration`);

      // Track how many queues were updated
      let updatedCount = {
        completed: 0,
        noShow: 0
      };

      // Process each queue to check if it's expired
      for (const queue of waitingQueues) {
        // Check if the time slot has ended
        if (this.hasTimeSlotEnded(queue.date, queue.timeSlot)) {
          // Determine if this was a no-show (not checked in) or a completed service
          const newStatus = queue.isCheckedIn ? 'completed' : 'no-show';

          // Update status in database
          queue.status = newStatus;
          await this.queueRepository.save(queue);

          // Track which type was updated
          if (newStatus === 'completed') {
            updatedCount.completed++;
          } else {
            updatedCount.noShow++;
          }

          // Update Redis if there's an entry
          const redisKey = `queue:${queue.id}`;
          const redisData = await this.redisService.get(redisKey);
          if (redisData) {
            await this.redisService.updateQueueStatus(queue.id.toString(), newStatus);
          }

          // Format date for Redis keys
          const dateStr = (() => {
            if (!queue.date) {
              return new Date().toISOString().split('T')[0];
            }
            return new Date(queue.date).toISOString().split('T')[0];
          })();

          // Invalidate related caches
          await this.redisService.invalidateServiceQueues(queue.serviceId, dateStr);
          await this.redisService.invalidateServiceQueues(queue.serviceId, 'all');

          // Invalidate queue counts cache
          const queueCountsKey = `service:${queue.serviceId}:queue-counts:${dateStr}`;
          await this.redisService.del(queueCountsKey);

          // Invalidate service active queues cache
          const activeQueuesKey = `service:${queue.serviceId}:active-queues:${dateStr}`;
          const allActiveQueuesKey = `service:${queue.serviceId}:active-queues:all`;
          await this.redisService.del(activeQueuesKey);
          await this.redisService.del(allActiveQueuesKey);
        }
      }

      const totalUpdated = updatedCount.completed + updatedCount.noShow;

      return {
        updated: totalUpdated,
        message: `Updated ${totalUpdated} expired queues: ${updatedCount.completed} to 'completed' status and ${updatedCount.noShow} to 'no-show' status`
      };
    } catch (error) {
      console.error('Error updating expired queues:', error);
      return {
        updated: 0,
        message: `Failed to update expired queues: ${error.message}`
      };
    }
  }

  // Helper method to update queue status by IDs
  private async updateQueueStatusByIds(queueIds: number[], status: string, serviceId: number, date: string): Promise<void> {
    try {
      console.log(`Updating ${queueIds.length} queues to status '${status}' for service ${serviceId}`);

      // Bulk update in database
      await this.queueRepository.update(
        { id: In(queueIds) },
        { status }
      );

      // Update in Redis individually
      for (const queueId of queueIds) {
        const redisKey = `queue:${queueId}`;
        const redisData = await this.redisService.get(redisKey);
        if (redisData) {
          await this.redisService.updateQueueStatus(queueId.toString(), status);
        }
      }

      // Format date for Redis keys
      const dateStr = typeof date === 'string' ? date : new Date().toISOString().split('T')[0];

      // Invalidate related caches
      await this.redisService.invalidateServiceQueues(serviceId, dateStr);
      await this.redisService.invalidateServiceQueues(serviceId, 'all');

      // Invalidate queue counts cache
      const queueCountsKey = `service:${serviceId}:queue-counts:${dateStr}`;
      await this.redisService.del(queueCountsKey);

      // Invalidate service active queues cache
      const activeQueuesKey = `service:${serviceId}:active-queues:${dateStr}`;
      const allActiveQueuesKey = `service:${serviceId}:active-queues:all`;
      await this.redisService.del(activeQueuesKey);
      await this.redisService.del(allActiveQueuesKey);

      console.log(`Successfully updated ${queueIds.length} queues to status '${status}'`);
    } catch (error) {
      console.error(`Error updating queue status: ${error.message}`);
    }
  }

  async getQueueByUniqueSlotId(uniqueSlotId: string): Promise<any> {
    try {
      // Find the queue in the database by uniqueSlotId
      const queue = await this.queueRepository.findOne({
        where: { uniqueSlotId },
        relations: ['service']
      });

      if (!queue) {
        throw new NotFoundException(`Queue with uniqueSlotId ${uniqueSlotId} not found`);
      }

      // Try to look up mobile number if userId looks like a clerk ID
      let mobileNumber: string | null = null;
      if (queue.userId && !queue.userId.includes('@')) {
        try {
          // Look up user by clerk ID
          const user = await this.findUserByClerkId(queue.userId);
          if (user && user.mobileNumber) {
            console.log(`Found mobile number via clerk ID: ${user.mobileNumber}`);
            mobileNumber = user.mobileNumber;
          }
        } catch (error) {
          console.error(`Error looking up user by clerk ID: ${error.message}`);
        }
      }

      // Include the service name and type
      const result = {
        ...queue,
        serviceName: queue.service?.serviceName || 'Unknown Service',
        serviceType: queue.service?.serviceType || 'Unknown Type',
        mobileNumber // Include the mobile number if found
      };

      // Check for Redis data (may have additional information)
      try {
        const redisData = await this.redisService.getQueue(queue.id.toString());
        if (redisData) {
          // Merge data from Redis and DB, with DB values taking precedence for critical fields
          return {
            ...redisData,
            ...result,
            uniqueSlotId: queue.uniqueSlotId, // Ensure these critical fields come from DB
            userName: queue.userName,
            status: queue.status,
            mobileNumber: mobileNumber || redisData.mobileNumber // Use the mobile number we found, fallback to Redis
          };
        }
      } catch (error) {
        console.log(`Error fetching Redis data for queue ${queue.id}:`, error);
      }

      // Return just the DB data if Redis lookup fails
      return result;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error in getQueueByUniqueSlotId(${uniqueSlotId}):`, error);
      throw new Error(`Failed to fetch queue by uniqueSlotId: ${error.message}`);
    }
  }

  // Get all queues from Redis for a service regardless of status
  async getAllQueuesFromRedis(serviceId: number): Promise<any[]> {
    try {
      console.log(`Fetching all queues from Redis for service ID: ${serviceId}`);

      // First, try using the existing methods which leverage Redis with fallbacks

      // Get active queues (waiting, checked-in)
      const activeQueues = await this.getServiceActiveQueues(serviceId, 'all');
      console.log(`Found ${activeQueues.length} active queues for service ${serviceId}`);

      // Get completed queues
      const completedQueues = await this.getServiceQueuesByStatus(serviceId, 'all', 'completed');
      console.log(`Found ${completedQueues.length} completed queues for service ${serviceId}`);

      // Get no-show queues
      const noShowQueues = await this.getServiceQueuesByStatus(serviceId, 'all', 'no-show');
      console.log(`Found ${noShowQueues.length} no-show queues for service ${serviceId}`);

      // If we have no queues from the above methods, try a direct database query as a fallback
      let allQueues = [...activeQueues, ...completedQueues, ...noShowQueues];

      if (allQueues.length === 0) {
        console.log(`No queues found in Redis. Falling back to database for service ${serviceId}`);

        // Direct database query for all queues of this service
        const dbQueues = await this.queueRepository.find({
          where: { serviceId },
          relations: ['service'],
        });

        console.log(`Found ${dbQueues.length} queues in database for service ${serviceId}`);

        // Map to the expected format
        allQueues = dbQueues.map(queue => ({
          id: queue.id,
          serviceId: queue.serviceId,
          serviceName: queue.service?.serviceName || 'Unknown Service',
          serviceType: queue.service?.serviceType || 'Unknown Type',
          date: queue.date,
          timeSlot: queue.timeSlot,
          status: queue.status,
          isVIP: queue.isVIP,
          userId: queue.userId,
          createdAt: queue.createdAt,
          uniqueSlotId: queue.uniqueSlotId || Math.floor(1000 + Math.random() * 9000).toString(),
          isCheckedIn: queue.isCheckedIn || false
        }));

        // Store all these queues in Redis for future use
        for (const queue of allQueues) {
          await this.redisService.saveQueue(queue.id.toString(), queue);
        }
      }

      // Remove duplicate queue entries by ID
      const uniqueQueues = Array.from(
        new Map(allQueues.map(queue => [queue.id, queue])).values()
      );

      console.log(`Returning ${uniqueQueues.length} unique queues for service ${serviceId}`);
      return uniqueQueues;
    } catch (error) {
      console.error(`Error fetching all queues from Redis for service ${serviceId}:`, error);
      throw new Error(`Failed to fetch all queues from Redis: ${error.message}`);
    }
  }

  // Direct, optimized method for fetching all queues from Redis in a single operation
  async getAllQueuesDirectFromRedis(serviceId: number): Promise<any[]> {
    try {
      console.log(`Fast Redis fetch for service ID: ${serviceId}`);

      // Get all queue keys for this service directly (faster than filtering later)
      const redisKeyPattern = `queue:*`;
      const allKeys = await this.redisService.getKeys(redisKeyPattern);

      if (!allKeys || allKeys.length === 0) {
        console.log(`No queue keys found in Redis`);
        return this.getAllQueuesFromDatabase(serviceId);
      }

      console.log(`Found ${allKeys.length} total queue keys in Redis`);

      // Batch fetch all values from Redis in a single operation
      const startTime = Date.now();
      const allValues = await this.redisService.mget(...allKeys);
      const fetchTime = Date.now();
      console.log(`Redis batch fetch completed in ${fetchTime - startTime}ms`);

      // Filter queues for the requested service in memory (faster than multiple DB calls)
      const filteredQueues = allValues
        .filter(Boolean) // Remove null/undefined values
        .filter(queue => queue.serviceId === serviceId);

      const filterTime = Date.now();
      console.log(`Memory filtering completed in ${filterTime - fetchTime}ms (found ${filteredQueues.length} queues)`);

      if (filteredQueues.length === 0) {
        // Fallback to database if no queues found in Redis
        console.log(`No queues found in Redis for service ${serviceId}. Falling back to database.`);
        return this.getAllQueuesFromDatabase(serviceId);
      }

      return filteredQueues;
    } catch (error) {
      console.error(`Error in direct Redis fetch for service ${serviceId}:`, error);

      // Fallback to database on Redis error
      console.log(`Falling back to database due to Redis error`);
      return this.getAllQueuesFromDatabase(serviceId);
    }
  }

  // Database fallback method for fetching all queues if Redis fails
  private async getAllQueuesFromDatabase(serviceId: number): Promise<any[]> {
    console.log(`Fetching all queues from database for service ${serviceId}`);

    const startTime = Date.now();
    const queues = await this.queueRepository.find({
      where: { serviceId },
      relations: ['service'],
    });
    const endTime = Date.now();

    console.log(`Database query completed in ${endTime - startTime}ms (found ${queues.length} queues)`);

    if (queues.length === 0) {
      return [];
    }

    // Map database queues to the expected format
    const mappedQueues = queues.map(queue => ({
      id: queue.id,
      serviceId: queue.serviceId,
      serviceName: queue.service?.serviceName || 'Unknown Service',
      serviceType: queue.service?.serviceType || 'Unknown Type',
      date: queue.date,
      timeSlot: queue.timeSlot,
      status: queue.status,
      isVIP: queue.isVIP,
      userId: queue.userId,
      createdAt: queue.createdAt,
      uniqueSlotId: queue.uniqueSlotId || Math.floor(1000 + Math.random() * 9000).toString(),
      isCheckedIn: queue.isCheckedIn || false,
      // Include subunit information
      hasSubUnits: queue.hasSubUnits || false,
      subUnitId: queue.subUnitId || '',
      subUnitName: queue.subUnitName || ''
    }));

    // Store all these queues in Redis for future use
    const cacheStart = Date.now();
    for (const queue of mappedQueues) {
      await this.redisService.saveQueue(queue.id.toString(), queue);
    }
    const cacheEnd = Date.now();

    console.log(`Redis caching completed in ${cacheEnd - cacheStart}ms`);

    return mappedQueues;
  }

  // Method to ensure queue status consistency between Redis and database
  async ensureQueueConsistency(serviceId: number): Promise<void> {
    try {
      // Step 1: Get all queue keys from Redis for this service
      const allKeys = await this.redisService.getKeys(`queue:*`);
      console.log(`Found ${allKeys.length} total queue keys in Redis`);

      if (!allKeys || allKeys.length === 0) {
        console.log(`No queue keys found in Redis`);
        return;
      }

      // Step 2: Get all queue data from Redis in batch
      const allQueueData = await this.redisService.mget(...allKeys);

      // Step 3: Filter for queues belonging to this service
      const serviceQueues = allQueueData
        .filter(q => q && q.serviceId === serviceId)
        .map(q => ({
          id: q.id,
          status: q.status,
          lastUpdated: q.statusUpdatedAt || q.updatedAt || null
        }));

      console.log(`Found ${serviceQueues.length} queues for service ${serviceId} in Redis`);

      // Step 4: Get DB status for these queues for comparison
      if (serviceQueues.length > 0) {
        const queueIds = serviceQueues.map(q => q.id);

        // Get queue status from database
        const dbQueues = await this.queueRepository.find({
          where: { id: In(queueIds) },
          select: ['id', 'status', 'isCheckedIn', 'date', 'timeSlot', 'serviceId']
        });

        console.log(`Found ${dbQueues.length} matching queues in database`);

        // Step 5: Compare and fix inconsistencies
        const fixPromises = dbQueues.map(async dbQueue => {
          const redisQueue = serviceQueues.find(q => q.id === dbQueue.id);
          if (!redisQueue) return;

          // Check for status mismatch - prioritize finalized statuses
          if (redisQueue.status !== dbQueue.status) {
            console.log(`Status mismatch for queue ${dbQueue.id}: Redis=${redisQueue.status}, DB=${dbQueue.status}`);

            // If DB status is completed or no-show, it should take precedence
            // These are final states that should not be changed back
            if (dbQueue.status === 'completed' || dbQueue.status === 'no-show') {
              console.log(`Fixing Redis status for queue ${dbQueue.id} to match DB: ${dbQueue.status}`);

              // Step 6: Get full queue data from Redis to preserve all fields
              const fullRedisData = await this.redisService.getQueue(dbQueue.id);
              if (fullRedisData) {
                // Update status but preserve all other fields
                const updatedData = {
                  ...fullRedisData,
                  status: dbQueue.status,
                  statusUpdatedAt: new Date().toISOString(),
                  statusFixed: true // Mark that this was fixed by the consistency check
                };

                // Save with a long TTL to ensure persistence
                await this.redisService.saveQueue(dbQueue.id, updatedData, 86400 * 7); // 7 days

                // Also create a backup status-specific key
                const statusKey = `queue:${dbQueue.id}:status:${dbQueue.status}`;
                await this.redisService.set(statusKey, updatedData, { ex: 86400 * 14 }); // 14 days
              }
            }
            // If Redis status is completed or no-show but DB has a different state,
            // we need to update the database to match Redis
            else if ((redisQueue.status === 'completed' || redisQueue.status === 'no-show') &&
                     dbQueue.status !== redisQueue.status) {
              console.log(`Fixing DB status for queue ${dbQueue.id} to match Redis: ${redisQueue.status}`);

              // Update database status to match Redis
              dbQueue.status = redisQueue.status;
              await this.queueRepository.save(dbQueue);
            }
          }
        });

        await Promise.all(fixPromises);
        console.log(`Completed consistency check for ${dbQueues.length} queues`);
      }
    } catch (error) {
      console.error(`Error in ensureQueueConsistency for service ${serviceId}:`, error);
    }
  }

  // Method to restore lost or corrupted queue data in Redis from database
  async restoreQueueData(serviceId: number): Promise<void> {
    try {
      // Find all queues for this service in the database
      const dbQueues = await this.queueRepository.find({
        where: { serviceId },
        relations: ['service'],
      });

      console.log(`Found ${dbQueues.length} queues for service ${serviceId} in database`);

      if (dbQueues.length === 0) return;

      // Get all existing queue keys in Redis for this service
      const existingKeys = await this.redisService.getKeys(`queue:*`);
      const existingQueueIds = existingKeys
        .map(key => {
          const match = key.match(/queue:(\d+)$/);
          return match ? parseInt(match[1]) : null;
        })
        .filter(id => id !== null);

      console.log(`Found ${existingQueueIds.length} existing queue keys in Redis`);

      // Process each database queue
      let restoredCount = 0;
      const restorePromises = dbQueues.map(async queue => {
        // Check if queue doesn't exist in Redis or has corrupted data
        if (!existingQueueIds.includes(queue.id)) {
          // Prepare queue data for Redis
          const redisData = {
            id: queue.id,
            serviceId: queue.serviceId,
            serviceName: queue.service?.serviceName || 'Unknown Service',
            serviceType: queue.service?.serviceType || 'Unknown Type',
            date: queue.date,
            timeSlot: queue.timeSlot,
            status: queue.status,
            isVIP: queue.isVIP,
            userId: queue.userId,
            createdAt: queue.createdAt,
            updatedAt: new Date().toISOString(),
            uniqueSlotId: queue.uniqueSlotId || Math.floor(1000 + Math.random() * 9000).toString(),
            isCheckedIn: queue.isCheckedIn,
            fullName: queue.userName || 'Anonymous',
            mobileNumber: null, // Will be filled from user data if available
            statusUpdatedAt: new Date().toISOString(),
            restoredFromDB: true
          };

          // Store in Redis with long TTL
          await this.redisService.saveQueue(queue.id.toString(), redisData, 86400 * 7); // 7 days

          // Also create a status-specific backup key
          const statusKey = `queue:${queue.id}:status:${queue.status}`;
          await this.redisService.set(statusKey, redisData, { ex: 86400 * 14 }); // 14 days

          restoredCount++;
        }
      });

      await Promise.all(restorePromises);
      console.log(`Restored ${restoredCount} queues to Redis from database`);
    } catch (error) {
      console.error(`Error in restoreQueueData for service ${serviceId}:`, error);
    }
  }

  async getQueueGracePeriodStatus(queueId: number): Promise<any> {
    // Get the queue from Redis first
    const redisKey = `queue:${queueId}`;
    const queueData = await this.redisService.get(redisKey);

    if (queueData) {
      // Process Redis data for grace period
      const now = new Date();
      let remainingSeconds = 0;
      let isExpired = true;

      if (queueData.inGracePeriod && queueData.graceStartedAt && queueData.graceEndTime) {
        const graceEndTime = new Date(queueData.graceEndTime);

        if (now < graceEndTime) {
          isExpired = false;
          remainingSeconds = Math.floor((graceEndTime.getTime() - now.getTime()) / 1000);
        }
      }

      return {
        queueId: queueData.id,
        inGracePeriod: queueData.inGracePeriod || false,
        confirmedPresence: queueData.confirmedPresence || false,
        graceStartedAt: queueData.graceStartedAt,
        graceEndTime: queueData.graceEndTime,
        graceTimeSeconds: queueData.graceTimeSeconds || 120,
        remainingSeconds: queueData.inGracePeriod ? remainingSeconds : 0,
        isExpired: queueData.inGracePeriod ? isExpired : false,
        status: queueData.status
      };
    }

    // Fall back to database
    const queue = await this.queueRepository.findOne({
      where: { id: queueId },
      relations: ['service']
    });

    if (!queue) {
      throw new NotFoundException(`Queue with ID ${queueId} not found`);
    }

    // Get service setup data for grace time
    let graceTime = 120; // Default to 2 minutes
    if (queue.service) {
      const setup = await this.serviceSetupRepository.findOne({
        where: { service: { id: queue.serviceId } }
      });

      if (setup?.graceTime) {
        graceTime = setup.graceTime;
      }
    }

    // Calculate remaining time if in grace period
    let remainingSeconds = 0;
    let isExpired = true;

    if (queue.inGracePeriod && queue.graceStartedAt) {
      const now = new Date();
      const graceEndTime = new Date(queue.graceStartedAt);
      graceEndTime.setSeconds(graceEndTime.getSeconds() + graceTime);

      if (now < graceEndTime) {
        isExpired = false;
        remainingSeconds = Math.floor((graceEndTime.getTime() - now.getTime()) / 1000);
      }
    }

    // Format the response
    return {
      queueId: queue.id,
      inGracePeriod: queue.inGracePeriod,
      confirmedPresence: queue.confirmedPresence,
      graceStartedAt: queue.graceStartedAt,
      graceTimeSeconds: graceTime,
      remainingSeconds: queue.inGracePeriod ? remainingSeconds : 0,
      isExpired: queue.inGracePeriod ? isExpired : false,
      status: queue.status
    };
  }
}
