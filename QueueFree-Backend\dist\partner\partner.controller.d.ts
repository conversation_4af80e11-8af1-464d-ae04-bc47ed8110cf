import { PartnerService } from './partner.service';
import { SchedulerService } from '../services/scheduler/scheduler.service';
import { QueueFlowService } from '../services/queue-flow/queue-flow.service';
export declare class PartnerController {
    private readonly partnerService;
    private readonly schedulerService;
    private readonly queueFlowService;
    private readonly logger;
    constructor(partnerService: PartnerService, schedulerService: SchedulerService, queueFlowService: QueueFlowService);
    registerPartner(body: {
        email: string;
    }): Promise<{
        status: string;
        message: string;
        isExisting: boolean;
        serviceId: number;
    }>;
    registerService(serviceData: any): Promise<{
        serviceId: number;
        status: string;
    }>;
    getServiceStatus(serviceId: string): Promise<{
        success: boolean;
        status: string;
    }>;
    saveServiceSetup(serviceId: string, setupData: any): Promise<{
        status: string;
        message: string;
        data: {
            selectedDays: string[];
            availableHours?: {
                [day: string]: Array<{
                    start: string;
                    end: string;
                }>;
            };
            timeSlots?: Array<{
                start: string;
                end: string;
            }>;
            servingTime: string;
            basePrice: string;
            useDayWiseTimeSlots?: boolean;
            timeSlotsByDay?: {
                [day: string]: Array<{
                    start: string;
                    end: string;
                }>;
            };
            hasSubUnits?: boolean;
            subUnits?: Array<{
                name: string;
                availableHours: {
                    [day: string]: Array<{
                        start: string;
                        end: string;
                    }>;
                };
                dayWiseAvailableHours?: {
                    [day: string]: Array<{
                        start: string;
                        end: string;
                    }>;
                };
                avgServeTime: string;
                pricePerHead: string;
                selectedDays?: string[];
                useDayWiseTimeSlots?: boolean;
                timeSlots?: Array<{
                    start: string;
                    end: string;
                }>;
            }>;
        };
        termsAccepted: boolean;
        hasSetup: boolean;
    }>;
    getServiceSetup(serviceId: string): Promise<{
        status: string;
        message: string;
        hasSetup: boolean;
        data: null;
    } | {
        status: string;
        message: string;
        hasSetup: boolean;
        data: {
            selectedDays: string[];
            availableHours?: {
                [day: string]: Array<{
                    start: string;
                    end: string;
                }>;
            };
            timeSlots?: Array<{
                start: string;
                end: string;
            }>;
            servingTime: string;
            basePrice: string;
            useDayWiseTimeSlots?: boolean;
            timeSlotsByDay?: {
                [day: string]: Array<{
                    start: string;
                    end: string;
                }>;
            };
            hasSubUnits?: boolean;
            subUnits?: Array<{
                name: string;
                availableHours: {
                    [day: string]: Array<{
                        start: string;
                        end: string;
                    }>;
                };
                dayWiseAvailableHours?: {
                    [day: string]: Array<{
                        start: string;
                        end: string;
                    }>;
                };
                avgServeTime: string;
                pricePerHead: string;
                selectedDays?: string[];
                useDayWiseTimeSlots?: boolean;
                timeSlots?: Array<{
                    start: string;
                    end: string;
                }>;
            }>;
        };
    }>;
    getServiceDetails(email: string): Promise<import("./entities/service.entity").Service | {
        id: null;
        serviceName: string;
        termsAccepted: boolean;
        serviceType: string;
        businessPhone: string;
        serviceDescription: string;
        address: null;
        images: never[];
        verificationStatus: string;
        documents: null;
    }>;
    updateService(serviceData: any): Promise<{
        status: string;
        message: string;
        service: import("./entities/service.entity").Service;
    }>;
    checkServiceCompletion(email: string): Promise<{
        exists: boolean;
        isVerified: boolean;
        isDetailsComplete: boolean;
        hasSetup: boolean;
        verificationStatus: null;
        termsAccepted: boolean;
        serviceId: null;
        setupComplete: boolean;
        serviceDetails?: undefined;
    } | {
        exists: boolean;
        isVerified: boolean;
        isDetailsComplete: boolean;
        hasSetup: boolean;
        verificationStatus: string;
        termsAccepted: boolean;
        serviceId: number;
        setupComplete: boolean;
        serviceDetails: {
            serviceName: string;
            serviceType: string;
            businessPhone: string;
            serviceDescription: string;
            address: {
                details: {
                    buildingNo: string;
                    locality: string;
                    city: string;
                    state: string;
                    pincode: string;
                };
                coordinates: {
                    latitude: number;
                    longitude: number;
                };
                area?: string;
                fullAddress?: string;
                googleMapsLink?: string;
            };
            images: string[];
            documents: {
                panNumber: string;
                gstin?: string;
                panCardImage: string;
            };
        };
    }>;
    acceptTerms(email: string): Promise<{
        status: string;
        message: string;
    }>;
    getVerificationStatusByEmail(email: string): Promise<{
        success: boolean;
        status: string;
    }>;
    toggleServiceStatus(email: string, data: {
        isOpen: boolean;
    }): Promise<{
        status: string;
        message: string;
        isOpen: boolean;
    }>;
    getServiceOpenStatus(email: string): Promise<{
        success: boolean;
        isOpen: boolean;
    }>;
    saveBankDetails(serviceId: string, bankDetails: any): Promise<{
        status: string;
        message: string;
    }>;
    getBankDetails(serviceId: string): Promise<{
        status: string;
        data: import("./entities/bank-details.entity").BankDetails;
    }>;
    saveLeaveDays(serviceId: string, leaveDays: any): Promise<{
        status: string;
        message: string;
    }>;
    getLeaveDays(serviceId: string): Promise<{
        status: string;
        data: {
            subUnits: {
                [subUnitId: string]: string[];
            };
        };
        hasSubUnits: boolean;
    } | {
        status: string;
        data: string[];
        hasSubUnits: boolean;
    }>;
    getAllServices(): Promise<{
        _id: string;
        serviceName: string;
        serviceType: string;
        address: {
            details: {
                buildingNo: string;
                locality: string;
                city: string;
                state: string;
                pincode: string;
            };
            coordinates: {
                latitude: number;
                longitude: number;
            };
            area?: string;
            fullAddress?: string;
            googleMapsLink?: string;
        };
        image: string | null;
        isOpen: boolean;
        rating: number;
        reviewCount: number;
        serviceDescription: string;
        queueInfo: {
            waitingTime: number;
            membersInQueue: number;
            vipCount: number;
            normalCount: number;
            cost: number;
            servingTime: number;
        };
    }[]>;
    getServiceById(id: string): Promise<{
        _id: string;
        serviceName: string;
        serviceType: string;
        address: {
            details: {
                buildingNo: string;
                locality: string;
                city: string;
                state: string;
                pincode: string;
            };
            coordinates: {
                latitude: number;
                longitude: number;
            };
            area?: string;
            fullAddress?: string;
            googleMapsLink?: string;
        };
        images: string[];
        isOpen: boolean;
        rating: number;
        reviewCount: number;
        reviews: import("./entities/review.entity").Review[];
        serviceDescription: string;
        businessPhone: string;
        email: string;
        workingHours: {
            startTime: string;
            endTime: string;
        } | null;
        selectedDays: string[];
        queueInfo: {
            waitingTime: number;
            membersInQueue: number;
            vipCount: number;
            normalCount: number;
            cost: number;
            servingTime: number;
        };
    }>;
    getServiceReviews(id: string, limit?: string, offset?: string): Promise<{
        reviews: import("./entities/review.entity").Review[];
        totalCount: number;
        starDistribution: {
            1: number;
            2: number;
            3: number;
            4: number;
            5: number;
        };
    }>;
    addReview(id: string, reviewData: {
        userId: string;
        userName: string;
        userProfilePic?: string;
        rating: number;
        comment: string;
    }): Promise<import("./entities/review.entity").Review>;
    getServiceTimeSlots(id: string, date: string, subUnitId?: string): Promise<{
        status: string;
        message: string;
        timeSlots: never[];
        workingDay?: undefined;
        isLegacy?: undefined;
    } | {
        status: string;
        workingDay: boolean;
        timeSlots: {
            timeSlot: string;
            normalQueueCount: number;
            vipQueueCount: number;
        }[];
        message?: undefined;
        isLegacy?: undefined;
    } | {
        status: string;
        workingDay: boolean;
        timeSlots: {
            timeSlot: string;
            normalQueueCount: number;
            vipQueueCount: number;
        }[];
        isLegacy: boolean;
        message?: undefined;
    }>;
    testServiceSetup(serviceId: string): Promise<{
        status: string;
        serviceId: number;
        setupData: {
            selectedDays: string[];
            availableHours?: {
                [day: string]: Array<{
                    start: string;
                    end: string;
                }>;
            };
            timeSlots?: Array<{
                start: string;
                end: string;
            }>;
            servingTime: string;
            basePrice: string;
            useDayWiseTimeSlots?: boolean;
            timeSlotsByDay?: {
                [day: string]: Array<{
                    start: string;
                    end: string;
                }>;
            };
            hasSubUnits?: boolean;
            subUnits?: Array<{
                name: string;
                availableHours: {
                    [day: string]: Array<{
                        start: string;
                        end: string;
                    }>;
                };
                dayWiseAvailableHours?: {
                    [day: string]: Array<{
                        start: string;
                        end: string;
                    }>;
                };
                avgServeTime: string;
                pricePerHead: string;
                selectedDays?: string[];
                useDayWiseTimeSlots?: boolean;
                timeSlots?: Array<{
                    start: string;
                    end: string;
                }>;
            }>;
        } | null;
        leaveDays: string[] | {
            subUnits: {
                [subUnitId: string]: string[];
            };
        };
    }>;
    completeQueue(queueId: string): Promise<{
        status: string;
        message: string;
        data: import("./entities/queue.entity").Queue;
    }>;
    confirmPresence(queueId: string, body: {
        isPresent: boolean;
    }): Promise<{
        status: string;
        message: string;
        data: import("./entities/queue.entity").Queue;
    }>;
    cancelQueue(queueId: string): Promise<{
        status: string;
        message: string;
        data: import("./entities/queue.entity").Queue;
    }>;
    markQueueAsNoShow(queueId: string): Promise<{
        status: string;
        message: string;
        data: import("./entities/queue.entity").Queue;
    }>;
    moveQueueToEnd(queueId: string): Promise<{
        status: string;
        message: string;
        data: {
            newPosition: number;
            id: number;
            service: import("./entities/service.entity").Service;
            serviceId: number;
            status: string;
            isVIP: boolean;
            date: Date;
            timeSlot: string;
            userId: string;
            userName: string;
            uniqueSlotId: string;
            isCheckedIn: boolean;
            graceStartedAt: Date | null;
            confirmedPresence: boolean;
            inGracePeriod: boolean;
            currentlyServing: boolean;
            servingStartedAt: Date | null;
            statusUpdatedAt: Date | null;
            position: number;
            initialPositionAtJoin: number;
            estimatedServeTime: Date | null;
            waitTimeStatus: string;
            hasSubUnits: boolean;
            subUnitId: string;
            subUnitName: string;
            serveTime: number;
            createdAt: Date;
        };
    }>;
    startServingQueue(queueId: string): Promise<{
        status: string;
        message: string;
        data: import("./entities/queue.entity").Queue;
    }>;
    startGracePeriod(queueId: string): Promise<{
        status: string;
        message: string;
        data: import("./entities/queue.entity").Queue;
    }>;
    getGracePeriodStatus(queueId: string): Promise<{
        status: string;
        data: any;
    }>;
    getEstimatedWaitTime(queueId: string): Promise<{
        status: string;
        data: {
            queueId: number;
            waitTimeMinutes: number;
            waitTimeStatus: string;
            estimatedServeTime: string;
        };
    }>;
    getActualServiceTime(queueId: string): Promise<{
        status: string;
        data: {
            queueId: number;
            serviceTimeMinutes: number;
            servingStartedAt: string | null;
            statusUpdatedAt: string | null;
            status: string;
        };
    }>;
    moveQueueToEndOfLine(queueId: string): Promise<{
        status: string;
        message: string;
        data: {
            queueId: number;
            position: number;
            status: string;
            estimatedServeTime: string | null;
        };
    }>;
    getServingStatus(queueId: string, hasSubUnits?: string, subUnitId?: string): Promise<{
        status: string;
        data: {
            queueId: any;
            serviceId: any;
            isServing: boolean;
            servingStartedAt: any;
            estimatedEndTime: string;
            servingTimeMinutes: number;
            remainingMinutes: number;
            remainingSeconds: number;
            hasSubUnits: boolean;
            subUnitId: any;
        };
    } | {
        status: string;
        data: {
            queueId: number;
            serviceId: number;
            isServing: boolean;
            servingStartedAt: null;
            estimatedEndTime: null;
            servingTimeMinutes: number;
            remainingMinutes: number;
            remainingSeconds: number;
            hasSubUnits: boolean;
            subUnitId: string;
        };
    }>;
    triggerAutoGracePeriods(): Promise<{
        status: string;
        message: string;
    }>;
    refreshServingQueues(): Promise<{
        status: string;
        message: string;
    }>;
}
