import React, { useEffect, useState, useRef } from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StatusBar,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
  Alert,
  Linking,
  TextInput,
} from "react-native";
import { useLocalSearchParams, useRouter, Stack } from "expo-router";
import { images } from "@/constants";
import { useUser } from "@clerk/clerk-expo";
import LottieView from "lottie-react-native";
import ReviewSheet from "@/components/ReviewSheet";

// Interface for queue status
interface QueueStatus {
  id: number;
  serviceId: number;
  serviceName: string;
  serviceType: string;
  date: string;
  timeSlot: string;
  status: string; // completed, no-show, cancelled
  isVIP: boolean;
  createdAt: string;
  uniqueSlotId: string;
  statusUpdatedAt?: string;
  hasSubUnits?: boolean;
  subUnitId?: string;
  subUnitName?: string;
  images?: string[];
  contactPhone?: string;
}

// Interface for service details
interface ServiceDetails {
  _id: string;
  serviceName: string;
  serviceType: string;
  businessPhone: string;
  images: string[];
}

export default function QueueCompletedScreen() {
  const router = useRouter();
  const { queueId, status } = useLocalSearchParams();
  const { user } = useUser();

  // States
  const [isLoading, setIsLoading] = useState(true);
  const [queueData, setQueueData] = useState<QueueStatus | null>(null);
  const [serviceData, setServiceData] = useState<ServiceDetails | null>(null);

  // Review states
  const [reviewModalVisible, setReviewModalVisible] = useState(false);
  const [userRating, setUserRating] = useState(0);
  const [userReview, setUserReview] = useState("");
  const [reviewSubmitting, setReviewSubmitting] = useState(false);

  // API base URL
  const API_BASE_URL = "http://192.168.74.138:3000/api";

  // Fetch queue data
  const fetchQueueData = async () => {
    if (!queueId || !user?.id) return;

    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE_URL}/customer/queue/${queueId}`);

      if (!response.ok) {
        throw new Error("Failed to fetch queue data");
      }

      const data = await response.json();

      if (data.status === "success" && data.queue) {
        console.log("Queue data from Redis/DB:", data.queue);

        // Check if we have Redis-cached service data available
        if (data.queue.serviceName && data.queue.serviceType) {
          console.log("Queue data contains Redis-cached service information");
        }

        // If status is passed as a param, use it to override the status in the data
        // This ensures the correct animation and messaging is displayed
        if (status) {
          data.queue.status = status as string;
        }

        setQueueData(data.queue);

        // Fetch service details
        if (data.queue.serviceId) {
          fetchServiceDetails(data.queue.serviceId);
        }
      }
    } catch (error) {
      console.error("Error fetching queue data:", error);
      Alert.alert("Error", "Failed to load queue information");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch service details
  const fetchServiceDetails = async (id: number) => {
    try {
      // First check if we already have the service name and type from Redis cache
      // (they are included in the queue data when available)
      if (queueData?.serviceName && queueData?.serviceType) {
        console.log("Using service data from Redis cache:", {
          serviceName: queueData.serviceName,
          serviceType: queueData.serviceType
        });

        // Use the cached data from Redis that came with the queue information
        setServiceData({
          _id: id.toString(),
          serviceName: queueData.serviceName,
          serviceType: queueData.serviceType,
          businessPhone: queueData.contactPhone || "",
          images: Array.isArray(queueData.images) ? queueData.images : []
        });

        return; // Exit early since we have the data we need
      }

      // If Redis cache doesn't have the data, fall back to fetching from database
      console.log("Redis cache miss, fetching service details from database...");
      const response = await fetch(`${API_BASE_URL}/partner/services/${id}`);

      if (response.ok) {
        const serviceData = await response.json();
        console.log("Service details fetched from DB:", serviceData);

        // Process images
        let processedImages = [];
        if (serviceData.images) {
          processedImages = Array.isArray(serviceData.images)
            ? serviceData.images.filter(
                (url: string) => typeof url === "string" && url.trim() !== ""
              )
            : [];
        }

        setServiceData({
          ...serviceData,
          images: processedImages,
        });
      }
    } catch (error) {
      console.error("Error fetching service details:", error);
    }
  };

  // Handle join again
  const handleJoinAgain = () => {
    if (!queueData) return;

    // Redirect to service-details page instead of join-queue
    router.push({
      pathname: "/(root)/service-details",
      params: { id: queueData.serviceId.toString() },
    });
  };

  // Handle write review
  const handleWriteReview = () => {
    if (!queueData) return;

    // Show the review modal
    setReviewModalVisible(true);
  };

  // Handle submit review
  const handleSubmitReview = async () => {
    if (!queueData || !user || userRating === 0) {
      Alert.alert("Error", "Please provide a rating before submitting");
      return;
    }

    if (!userReview.trim()) {
      Alert.alert("Error", "Please write a review");
      return;
    }

    try {
      setReviewSubmitting(true);

      // Create the review data matching the format expected by the API
      const reviewData = {
        userId: user.id,
        userName: user.fullName || user.username || "Anonymous User",
        userProfilePic: user.imageUrl || null,
        rating: userRating,
        comment: userReview,
      };

      // Use the correct API endpoint
      const response = await fetch(
        `${API_BASE_URL}/partner/services/${queueData.serviceId}/reviews`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(reviewData),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to submit review: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      // Reset form and close modal
      setUserRating(0);
      setUserReview("");
      setReviewModalVisible(false);

      Alert.alert("Success", "Your review has been submitted successfully");
    } catch (error) {
      console.error('Error submitting review:', error);
      Alert.alert("Error", "Failed to submit your review. Please try again.");
    } finally {
      setReviewSubmitting(false);
    }
  };

  // Get image source
  const getImageSource = () => {
    if (serviceData?.images && serviceData.images.length > 0) {
      return { uri: serviceData.images[0] };
    }
    return images.image;
  };

  // Get status color
  const getStatusColor = () => {
    if (!queueData) return { bg: "#F3F4F6", text: "#6B7280" };

    switch (queueData.status) {
      case "completed":
        return { text: "#159AFF" };
      case "no-show":
        return { text: "#F56565" };
      case "cancelled":
        return { text: "#FFAC26" };
      default:
        return { text: "#6B7280" };
    }
  };

  // Get status title
  const getStatusTitle = () => {
    if (!queueData) return "Queue Status";

    switch (queueData.status) {
      case "completed":
        return "Completed";
      case "no-show":
        return "No Show";
      case "cancelled":
        return "Cancelled";
      default:
        return "Queue Status";
    }
  };

  // Get status message
  const getStatusMessage = () => {
    if (!queueData) return "";

    switch (queueData.status) {
      case "completed":
        return "You're all done!";
      case "no-show":
        return "Appointment Missed!";
      case "cancelled":
        return "Left the queue! ";
      default:
        return "";
    }
  };

  // Get status description
  const getStatusDescription = () => {
    if (!queueData) return "";

    switch (queueData.status) {
      case "completed":
        return "Your service has been successfully completed. We hope you had a great experience!";
      case "no-show":
        return "You were marked as no-show because you didn't check in for your appointment.";
      case "cancelled":
        return "You've cancelled your place in the queue. You can always join again when you're ready.";
      default:
        return "";
    }
  };

  // Get Lottie animation source
  const getLottieSource = () => {
    if (!queueData) return require("@/assets/animations/loading.json");

    switch (queueData.status) {
      case "completed":
        return require("@/assets/animations/completed.json");
      case "no-show":
        return require("@/assets/animations/no-show.json");
      case "cancelled":
        return require("@/assets/animations/cancelled.json");
      default:
        return require("@/assets/animations/loading.json");
    }
  };

  // Load data on initial render
  useEffect(() => {
    fetchQueueData();
  }, [queueId, user?.id]);

  // Loading Screen
  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#159AFF" />
        </View>
      </SafeAreaView>
    );
  }

  const statusColor = getStatusColor();

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <View
        className="flex-1 px-6"
      >
        {/* Header */}
        <View className="pt-12 pb-6 items-center">
          <TouchableOpacity
            className="absolute left-0 top-12 border border-secondary-600 w-12 h-12 rounded-full justify-center items-center"
            onPress={() => router.replace("/(root)/(tabs)/queues")}
          >
            <Image source={images.back} className="w-4 h-4" />
          </TouchableOpacity>

          <View className="items-center mt-2">
            <View
              className=" rounded-full mb-2"
              style={{ backgroundColor: statusColor.bg }}
            >
              <Text className="font-poppins-medium text-xl text-secondary-500">
                {getStatusTitle()}
              </Text>
            </View>

            {queueData?.statusUpdatedAt && (
              <Text className="text-secondary-600 text-base">
                {new Date(queueData.statusUpdatedAt).toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                  hour12: true,
                })}
              </Text>
            )}
          </View>
        </View>

        {/* Animation */}
        <View className="items-center justify-center py-6">
          <View className="w-52 h-52">
            <LottieView
              source={getLottieSource()}
              autoPlay
              loop={true}
              style={{ width: "100%", height: "100%" }}
            />
          </View>
        </View>

        {/* Status Message */}
        <View className="items-center mb-8 ">
          <Text className="font-poppins-semibold w-[75%] text-2xl text-secondary-800 mb-2 text-center">
            {getStatusMessage()}
          </Text>

          

          <Text className="text-secondary-600 w-[75%] text-base text-center">
            {getStatusDescription()}
          </Text>
        </View>

        {/* Service Card */}
        <View className="bg-white rounded-3xl mt-32 border border-primary-500/10 p-6">
          <View className="">
            <View className="flex-row items-center mb-6">
              <Image
                source={getImageSource()}
                className="w-16 h-16 rounded-xl mr-4"
                style={{ backgroundColor: "#f5f5f5" }}
              />
              <View className="flex-1">
                <Text className="font-poppins-medium text-lg text-secondary-800">
                  {serviceData?.serviceName}
                </Text>
                <View className="flex-row items-center">
                  <Text className="text-secondary-600 text-sm">
                    {serviceData?.serviceType}
                  </Text>
                  {queueData?.hasSubUnits && queueData?.subUnitName && (
                    <View className="bg-primary-50 px-2 py-1 rounded-lg">
                      <Text className="text-secondary-600 text-sm">
                        |  {queueData.subUnitName}
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            </View>

            

            <View className="flex-row items-center justify-between py-4 border-t border-gray-100">
              <Text className="text-secondary-600 text-sm">Unique Slot ID</Text>
              <Text className="font-poppins-medium text-sm text-secondary-600">
                {queueData?.uniqueSlotId}
              </Text>
            </View>

            <View className="flex-row items-center justify-between py-2">
              <Text className="text-secondary-600 text-sm">Date</Text>
              <Text className="font-poppins-medium text-sm text-secondary-600">
                {queueData?.date
                  ? new Date(queueData.date).toLocaleDateString()
                  : ""}
              </Text>
            </View>

            <View className="flex-row items-center justify-between pt-4 ">
              <Text className="text-secondary-600 text-sm">Time Slot</Text>
              <Text className="font-poppins-medium text-sm text-secondary-600">
                {queueData?.timeSlot}
              </Text>
            </View>
          </View>
        </View>
      </View>
      <View className="p-6 border-t flex justify-center items-center border-gray-200">
        {queueData?.status === "cancelled" ||
        queueData?.status === "no-show" ? (
          <TouchableOpacity
            className="w-[390px] py-6 rounded-2xl items-center bg-primary-500"
            onPress={handleJoinAgain}
          >
            <Text className="text-white font-poppins-medium text-lg">
              Join Again
            </Text>
          </TouchableOpacity>
        ) : queueData?.status === "completed" ? (
          <TouchableOpacity
            className="w-[390px] py-6 rounded-2xl items-center bg-primary-500"
            onPress={handleWriteReview}
          >
            <Text className="text-white font-poppins-medium text-lg">
              Write a Review
            </Text>
          </TouchableOpacity>
        ) : null}
      </View>

      {/* Review Bottom Sheet */}
      <ReviewSheet
        visible={reviewModalVisible}
        onClose={() => setReviewModalVisible(false)}
        userRating={userRating}
        setUserRating={setUserRating}
        userReview={userReview}
        setUserReview={setUserReview}
        onSubmit={handleSubmitReview}
        isSubmitting={reviewSubmitting}
      />
    </SafeAreaView>
  );
}
