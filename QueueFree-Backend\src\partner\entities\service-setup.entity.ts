import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, Column, OneToOne, JoinColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Service } from './service.entity';

@Entity('service_setups')
export class ServiceSetup {
  @PrimaryGeneratedColumn()
  id: number;

  @OneToOne(() => Service)
  @JoinColumn()
  service: Service;

  @Column('jsonb')
  setupData: {
    selectedDays: string[];
    availableHours?: {
      [day: string]: Array<{
        start: string;
        end: string;
      }>;
    };
    // Legacy fields - kept for backward compatibility
    timeSlots?: Array<{
      start: string;
      end: string;
    }>;
    servingTime: string;
    basePrice: string;
    useDayWiseTimeSlots?: boolean;
    timeSlotsByDay?: {
      [day: string]: Array<{
        start: string;
        end: string;
      }>;
    };
    hasSubUnits?: boolean;
    subUnits?: Array<{
      name: string;
      availableHours: {
        [day: string]: Array<{
          start: string;
          end: string;
        }>;
      };
      // Legacy fields - kept for backward compatibility
      dayWiseAvailableHours?: {
        [day: string]: Array<{
          start: string;
          end: string;
        }>;
      };
      avgServeTime: string;
      pricePerHead: string;
      selectedDays?: string[];
      useDayWiseTimeSlots?: boolean;
      timeSlots?: Array<{
        start: string;
        end: string;
      }>;
    }>;
  };

  @Column('jsonb', { 
    default: '{"main": [], "subUnits": {}}',
    comment: 'Format: { main: string[], subUnits: { "subunit-0": string[], "subunit-1": string[], ... } }'
  })
  leaveDays: {
    main: string[];
    subUnits: {
      [subUnitId: string]: string[];
    };
  };

  @Column({ type: 'integer', default: 120 })
  graceTime: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
