/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/AppRouter`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/verify-otp` | `/verify-otp`; params?: Router.UnknownInputParams; } | { pathname: `${'/(location)'}/allow-location` | `/allow-location`; params?: Router.UnknownInputParams; } | { pathname: `${'/(location)'}/enter-address` | `/enter-address`; params?: Router.UnknownInputParams; } | { pathname: `${'/(location)'}/select-location` | `/select-location`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/edit-service` | `/edit-service`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/view-queue` | `/view-queue`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/dashboard` | `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/insights` | `/insights`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/manage` | `/manage`; params?: Router.UnknownInputParams; } | { pathname: `/service-setup/setup`; params?: Router.UnknownInputParams; } | { pathname: `/service-setup/step1`; params?: Router.UnknownInputParams; } | { pathname: `/service-setup/step2`; params?: Router.UnknownInputParams; } | { pathname: `/service-setup/step3`; params?: Router.UnknownInputParams; } | { pathname: `/service-setup/update-bank-details`; params?: Router.UnknownInputParams; } | { pathname: `/service-setup/update-leave-days`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/AppRouter`; params?: Router.UnknownOutputParams; } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/verify-otp` | `/verify-otp`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(location)'}/allow-location` | `/allow-location`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(location)'}/enter-address` | `/enter-address`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(location)'}/select-location` | `/select-location`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/edit-service` | `/edit-service`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/view-queue` | `/view-queue`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/dashboard` | `/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/insights` | `/insights`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/manage` | `/manage`; params?: Router.UnknownOutputParams; } | { pathname: `/service-setup/setup`; params?: Router.UnknownOutputParams; } | { pathname: `/service-setup/step1`; params?: Router.UnknownOutputParams; } | { pathname: `/service-setup/step2`; params?: Router.UnknownOutputParams; } | { pathname: `/service-setup/step3`; params?: Router.UnknownOutputParams; } | { pathname: `/service-setup/update-bank-details`; params?: Router.UnknownOutputParams; } | { pathname: `/service-setup/update-leave-days`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/AppRouter${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-in${`?${string}` | `#${string}` | ''}` | `/sign-in${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/verify-otp${`?${string}` | `#${string}` | ''}` | `/verify-otp${`?${string}` | `#${string}` | ''}` | `${'/(location)'}/allow-location${`?${string}` | `#${string}` | ''}` | `/allow-location${`?${string}` | `#${string}` | ''}` | `${'/(location)'}/enter-address${`?${string}` | `#${string}` | ''}` | `/enter-address${`?${string}` | `#${string}` | ''}` | `${'/(location)'}/select-location${`?${string}` | `#${string}` | ''}` | `/select-location${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/edit-service${`?${string}` | `#${string}` | ''}` | `/edit-service${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/view-queue${`?${string}` | `#${string}` | ''}` | `/view-queue${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(tabs)'}/dashboard${`?${string}` | `#${string}` | ''}` | `/dashboard${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(tabs)'}/insights${`?${string}` | `#${string}` | ''}` | `/insights${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(tabs)'}/manage${`?${string}` | `#${string}` | ''}` | `/manage${`?${string}` | `#${string}` | ''}` | `/service-setup/setup${`?${string}` | `#${string}` | ''}` | `/service-setup/step1${`?${string}` | `#${string}` | ''}` | `/service-setup/step2${`?${string}` | `#${string}` | ''}` | `/service-setup/step3${`?${string}` | `#${string}` | ''}` | `/service-setup/update-bank-details${`?${string}` | `#${string}` | ''}` | `/service-setup/update-leave-days${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/AppRouter`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/verify-otp` | `/verify-otp`; params?: Router.UnknownInputParams; } | { pathname: `${'/(location)'}/allow-location` | `/allow-location`; params?: Router.UnknownInputParams; } | { pathname: `${'/(location)'}/enter-address` | `/enter-address`; params?: Router.UnknownInputParams; } | { pathname: `${'/(location)'}/select-location` | `/select-location`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/edit-service` | `/edit-service`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/view-queue` | `/view-queue`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/dashboard` | `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/insights` | `/insights`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/manage` | `/manage`; params?: Router.UnknownInputParams; } | { pathname: `/service-setup/setup`; params?: Router.UnknownInputParams; } | { pathname: `/service-setup/step1`; params?: Router.UnknownInputParams; } | { pathname: `/service-setup/step2`; params?: Router.UnknownInputParams; } | { pathname: `/service-setup/step3`; params?: Router.UnknownInputParams; } | { pathname: `/service-setup/update-bank-details`; params?: Router.UnknownInputParams; } | { pathname: `/service-setup/update-leave-days`; params?: Router.UnknownInputParams; } | `/+not-found` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
