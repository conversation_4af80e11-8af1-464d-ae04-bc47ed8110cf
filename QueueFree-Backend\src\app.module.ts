import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { CustomerModule } from './customer/customer.module';
import { User } from './customer/user.entity';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { PartnerModule } from './partner/partner.module';
import * as dotenv from 'dotenv';
import { Service } from './partner/entities/service.entity';
import { UploadModule } from './services/upload.module';
import { ServiceSetup } from './partner/entities/service-setup.entity';
import { Queue } from './partner/entities/queue.entity'; // Import Queue entity
import { RedisModule } from './services/redis/redis.module';
import { MigrationService } from './services/migration.service';
import { RedisService } from './services/redis/redis.service';
import { CommonServicesModule } from './services/common-services.module';

dotenv.config();

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      url: process.env.DATABASE_URL,
      entities: [User, Service, ServiceSetup, Queue], // Add Queue here
      synchronize: true,
      ssl: true,
      extra: {
        ssl: {
          rejectUnauthorized: false,
        },
      },
      logging: true,
      autoLoadEntities: true,
      migrations: [__dirname + '/migrations/**/*{.ts,.js}'],
      migrationsRun: true, // This will run migrations automatically
    }),
    // Import the CommonServicesModule first to ensure services are available
    CommonServicesModule,
    // Other modules
    RedisModule,
    AuthModule,
    CustomerModule,
    PartnerModule,
    UploadModule,
  ],
  controllers: [AppController],
  providers: [AppService, MigrationService, RedisService],
})
export class AppModule {}
