"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var QueueFlowService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueueFlowService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const queue_entity_1 = require("../../partner/entities/queue.entity");
const service_setup_entity_1 = require("../../partner/entities/service-setup.entity");
const redis_service_1 = require("../redis/redis.service");
const schedule_1 = require("@nestjs/schedule");
let QueueFlowService = QueueFlowService_1 = class QueueFlowService {
    constructor(queueRepository, serviceSetupRepository, redisService) {
        this.queueRepository = queueRepository;
        this.serviceSetupRepository = serviceSetupRepository;
        this.redisService = redisService;
        this.logger = new common_1.Logger(QueueFlowService_1.name);
    }
    async startGracePeriod(queueId, serviceId) {
        this.logger.log(`Starting grace period for queue ${queueId}`);
        const serviceSetup = await this.serviceSetupRepository.findOne({
            where: { service: { id: serviceId } }
        });
        const graceTime = serviceSetup?.graceTime || 120;
        const queue = await this.queueRepository.findOne({
            where: { id: queueId }
        });
        if (!queue) {
            throw new Error(`Queue ${queueId} not found`);
        }
        queue.graceStartedAt = new Date();
        queue.inGracePeriod = true;
        queue.confirmedPresence = false;
        const updatedQueue = await this.queueRepository.save(queue);
        await this.updateRedisWithGracePeriod(updatedQueue, graceTime);
        return updatedQueue;
    }
    async handlePresenceConfirmation(queueId, isPresent) {
        this.logger.log(`Handling presence confirmation for queue ${queueId}: ${isPresent}`);
        const queue = await this.queueRepository.findOne({
            where: { id: queueId },
            relations: ['service']
        });
        if (!queue) {
            throw new Error(`Queue ${queueId} not found`);
        }
        if (!queue.inGracePeriod || !queue.graceStartedAt) {
            return queue;
        }
        if (isPresent) {
            queue.confirmedPresence = true;
            queue.isCheckedIn = true;
            queue.inGracePeriod = false;
            const updatedQueue = await this.queueRepository.save(queue);
            await this.redisService.saveQueue(queueId.toString(), {
                ...await this.redisService.getQueue(queueId.toString()),
                confirmedPresence: true,
                isCheckedIn: true,
                inGracePeriod: false
            });
            return updatedQueue;
        }
        else {
            const allWaitingQueues = await this.queueRepository.find({
                where: {
                    serviceId: queue.serviceId,
                    date: queue.date,
                    timeSlot: queue.timeSlot,
                    status: 'waiting',
                },
                order: {
                    position: 'ASC',
                    createdAt: 'ASC'
                }
            });
            this.logger.log(`Found ${allWaitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);
            queue.confirmedPresence = false;
            queue.inGracePeriod = false;
            queue.status = 'no-show';
            queue.statusUpdatedAt = new Date();
            queue.position = -1;
            const updatedQueue = await this.queueRepository.save(queue);
            const queueData = await this.redisService.getQueue(queueId.toString()) || {};
            queueData.status = 'no-show';
            queueData.position = -1;
            await this.redisService.saveQueue(queueId.toString(), queueData);
            await this.redisService.updateQueueStatus(queueId.toString(), 'no-show');
            const remainingQueues = allWaitingQueues.filter(q => q.id !== queueId);
            const positions = {};
            let position = 0;
            for (const q of remainingQueues) {
                position++;
                q.position = position;
                positions[q.id] = position;
                const qData = await this.redisService.getQueue(q.id.toString()) || {};
                qData.position = position;
                await this.redisService.saveQueue(q.id.toString(), qData);
            }
            this.logger.log(`Reordered positions for ${remainingQueues.length} queues after marking queue ${queueId} as no-show`);
            if (remainingQueues.length > 0) {
                await this.queueRepository.save(remainingQueues);
            }
            const dateStr = new Date(queue.date).toISOString().split('T')[0];
            await this.redisService.saveQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot, positions);
            await this.redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);
            await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
            await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');
            return updatedQueue;
        }
    }
    async startServing(queueId) {
        this.logger.log(`Starting service for queue ${queueId}`);
        const queue = await this.queueRepository.findOne({
            where: { id: queueId },
            relations: ['service']
        });
        if (!queue) {
            throw new Error(`Queue ${queueId} not found`);
        }
        queue.status = 'serving';
        queue.currentlyServing = true;
        queue.servingStartedAt = new Date();
        queue.statusUpdatedAt = new Date();
        queue.isCheckedIn = true;
        if (queue.inGracePeriod) {
            queue.inGracePeriod = false;
            queue.confirmedPresence = true;
        }
        const updatedQueue = await this.queueRepository.save(queue);
        let servingTime = 15;
        if (queue.service) {
            const serviceSetup = await this.serviceSetupRepository.findOne({
                where: { service: { id: queue.serviceId } }
            });
            if (serviceSetup?.setupData?.servingTime) {
                servingTime = parseInt(serviceSetup.setupData.servingTime, 10);
                this.logger.log(`Using service configuration serving time: ${servingTime} minutes`);
            }
            else {
                this.logger.log(`No serving time configured for service ${queue.serviceId}, using default: ${servingTime} minutes`);
            }
        }
        const servingEndTime = new Date(queue.servingStartedAt);
        servingEndTime.setMinutes(servingEndTime.getMinutes() + servingTime);
        this.logger.log(`Queue ${queueId} serving started at ${queue.servingStartedAt.toISOString()}`);
        this.logger.log(`Estimated completion time: ${servingEndTime.toISOString()} (${servingTime} minutes later)`);
        const redisTTL = 86400;
        this.logger.log(`Setting Redis TTL for queue ${queueId} to ${redisTTL} seconds (24 hours fixed TTL for serving status)`);
        let existingData = {};
        try {
            existingData = await this.redisService.getQueue(queueId.toString()) || {};
        }
        catch (error) {
            this.logger.error(`Error getting existing Redis data for queue ${queueId}:`, error);
        }
        const updatedRedisData = {
            ...existingData,
            id: queue.id,
            serviceId: queue.serviceId,
            status: 'serving',
            currentlyServing: true,
            servingStartedAt: queue.servingStartedAt.toISOString(),
            estimatedEndTime: servingEndTime.toISOString(),
            servingTime,
            servingTimeMinutes: servingTime,
            remainingMinutes: servingTime,
            remainingSeconds: servingTime * 60,
            statusUpdatedAt: new Date().toISOString(),
            isCheckedIn: true
        };
        await this.redisService.set(`queue:${queueId}`, updatedRedisData, { ex: redisTTL });
        await this.redisService.updateQueueStatus(queueId.toString(), 'serving', true);
        const date = new Date(queue.date).toISOString().split('T')[0];
        await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), date);
        await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');
        return updatedQueue;
    }
    async completeService(queueId) {
        this.logger.log(`Completing service for queue ${queueId}`);
        const queue = await this.queueRepository.findOne({
            where: { id: queueId },
            relations: ['service']
        });
        if (!queue) {
            throw new Error(`Queue ${queueId} not found`);
        }
        const allWaitingQueues = await this.queueRepository.find({
            where: {
                serviceId: queue.serviceId,
                date: queue.date,
                timeSlot: queue.timeSlot,
                status: 'waiting',
            },
            order: {
                position: 'ASC',
                createdAt: 'ASC'
            }
        });
        this.logger.log(`Found ${allWaitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);
        queue.status = 'completed';
        queue.currentlyServing = false;
        queue.statusUpdatedAt = new Date();
        queue.position = -1;
        const updatedQueue = await this.queueRepository.save(queue);
        const queueData = await this.redisService.getQueue(queueId.toString()) || {};
        queueData.status = 'completed';
        queueData.position = -1;
        await this.redisService.saveQueue(queueId.toString(), queueData);
        await this.redisService.updateQueueStatus(queueId.toString(), 'completed');
        const remainingQueues = allWaitingQueues.filter(q => q.id !== queueId);
        const positions = {};
        let position = 0;
        for (const q of remainingQueues) {
            position++;
            q.position = position;
            positions[q.id] = position;
            const qData = await this.redisService.getQueue(q.id.toString()) || {};
            qData.position = position;
            await this.redisService.saveQueue(q.id.toString(), qData);
        }
        this.logger.log(`Reordered positions for ${remainingQueues.length} queues after completing queue ${queueId}`);
        if (remainingQueues.length > 0) {
            await this.queueRepository.save(remainingQueues);
        }
        const dateStr = new Date(queue.date).toISOString().split('T')[0];
        await this.redisService.saveQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot, positions);
        await this.redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);
        await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
        await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');
        return updatedQueue;
    }
    async markAsNoShow(queueId) {
        this.logger.log(`Marking queue ${queueId} as no-show`);
        const queue = await this.queueRepository.findOne({
            where: { id: queueId },
            relations: ['service']
        });
        if (!queue) {
            throw new Error(`Queue ${queueId} not found`);
        }
        const allWaitingQueues = await this.queueRepository.find({
            where: {
                serviceId: queue.serviceId,
                date: queue.date,
                timeSlot: queue.timeSlot,
                status: 'waiting',
            },
            order: {
                position: 'ASC',
                createdAt: 'ASC'
            }
        });
        this.logger.log(`Found ${allWaitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);
        queue.status = 'no-show';
        queue.inGracePeriod = false;
        queue.currentlyServing = false;
        queue.statusUpdatedAt = new Date();
        queue.position = -1;
        const updatedQueue = await this.queueRepository.save(queue);
        const queueData = await this.redisService.getQueue(queueId.toString()) || {};
        queueData.status = 'no-show';
        queueData.position = -1;
        await this.redisService.saveQueue(queueId.toString(), queueData);
        await this.redisService.updateQueueStatus(queueId.toString(), 'no-show');
        const remainingQueues = allWaitingQueues.filter(q => q.id !== queueId);
        const positions = {};
        let position = 0;
        for (const q of remainingQueues) {
            position++;
            q.position = position;
            positions[q.id] = position;
            const qData = await this.redisService.getQueue(q.id.toString()) || {};
            qData.position = position;
            await this.redisService.saveQueue(q.id.toString(), qData);
        }
        this.logger.log(`Reordered positions for ${remainingQueues.length} queues after marking queue ${queueId} as no-show`);
        if (remainingQueues.length > 0) {
            await this.queueRepository.save(remainingQueues);
        }
        const dateStr = new Date(queue.date).toISOString().split('T')[0];
        await this.redisService.saveQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot, positions);
        await this.redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);
        await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
        await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');
        return updatedQueue;
    }
    async updateRedisWithGracePeriod(queue, graceTime) {
        if (!queue.graceStartedAt) {
            this.logger.error(`Queue ${queue.id} has no grace start time`);
            return;
        }
        const graceEndTime = new Date(queue.graceStartedAt);
        graceEndTime.setSeconds(graceEndTime.getSeconds() + graceTime);
        const redisKey = `queue:${queue.id}`;
        let queueData = await this.redisService.get(redisKey) || {};
        queueData = {
            ...queueData,
            id: queue.id,
            inGracePeriod: true,
            graceStartedAt: queue.graceStartedAt.toISOString(),
            graceEndTime: graceEndTime.toISOString(),
            graceTimeSeconds: graceTime,
            confirmedPresence: false,
            serviceId: queue.serviceId,
            status: queue.status
        };
        await this.redisService.set(redisKey, queueData, { ex: Math.max(graceTime * 2, 300) });
    }
    async checkGracePeriods() {
        this.logger.debug('Checking for expired grace periods');
        try {
            const queuesInGracePeriod = await this.queueRepository.find({
                where: { inGracePeriod: true },
                relations: ['service']
            });
            if (queuesInGracePeriod.length === 0) {
                return;
            }
            this.logger.debug(`Found ${queuesInGracePeriod.length} queues in grace period`);
            const now = new Date();
            for (const queue of queuesInGracePeriod) {
                try {
                    if (!queue.graceStartedAt) {
                        continue;
                    }
                    const serviceSetup = await this.serviceSetupRepository.findOne({
                        where: { service: { id: queue.serviceId } }
                    });
                    const graceTime = serviceSetup?.graceTime || 120;
                    const graceEndTime = new Date(queue.graceStartedAt);
                    graceEndTime.setSeconds(graceEndTime.getSeconds() + graceTime);
                    if (now > graceEndTime) {
                        if (!queue.confirmedPresence) {
                            this.logger.log(`Grace period expired for queue ${queue.id} - marking as no-show`);
                            await this.markAsNoShow(queue.id);
                        }
                        else {
                            this.logger.log(`Grace period expired for queue ${queue.id} but customer confirmed presence`);
                            queue.inGracePeriod = false;
                            await this.queueRepository.save(queue);
                            await this.redisService.saveQueue(queue.id.toString(), {
                                ...await this.redisService.getQueue(queue.id.toString()),
                                inGracePeriod: false
                            });
                        }
                    }
                }
                catch (error) {
                    this.logger.error(`Error processing grace period for queue ${queue.id}:`, error);
                }
            }
        }
        catch (error) {
            this.logger.error('Error checking grace periods:', error);
        }
    }
    async autoStartGracePeriods() {
        this.logger.log('Auto grace period starting has been disabled - grace periods are only started manually');
        return { disabled: true, message: 'Auto grace periods disabled' };
    }
    async checkSingleGracePeriod(queueId) {
        this.logger.debug(`Checking grace period for queue ${queueId}`);
        try {
            const queue = await this.queueRepository.findOne({
                where: { id: queueId, inGracePeriod: true },
                relations: ['service']
            });
            if (!queue) {
                this.logger.debug(`Queue ${queueId} not found or not in grace period`);
                return;
            }
            if (!queue.graceStartedAt) {
                this.logger.debug(`Queue ${queueId} has no grace start time`);
                return;
            }
            const serviceSetup = await this.serviceSetupRepository.findOne({
                where: { service: { id: queue.serviceId } }
            });
            const graceTime = serviceSetup?.graceTime || 120;
            const graceEndTime = new Date(queue.graceStartedAt);
            graceEndTime.setSeconds(graceEndTime.getSeconds() + graceTime);
            const now = new Date();
            if (now > graceEndTime) {
                if (!queue.confirmedPresence) {
                    this.logger.log(`Grace period expired for queue ${queue.id} - marking as no-show immediately`);
                    await this.markAsNoShow(queue.id);
                }
                else {
                    this.logger.log(`Grace period expired for queue ${queue.id} but customer confirmed presence`);
                    queue.inGracePeriod = false;
                    await this.queueRepository.save(queue);
                    await this.redisService.saveQueue(queue.id.toString(), {
                        ...await this.redisService.getQueue(queue.id.toString()),
                        inGracePeriod: false
                    });
                }
            }
            else {
                this.logger.debug(`Grace period for queue ${queueId} has not expired yet`);
            }
        }
        catch (error) {
            this.logger.error(`Error checking grace period for queue ${queueId}:`, error);
            throw error;
        }
    }
    async calculateEstimatedServeTime(queueId) {
        this.logger.log(`Calculating estimated serve time for queue ${queueId}`);
        const queue = await this.queueRepository.findOne({
            where: { id: queueId },
            relations: ['service']
        });
        if (!queue) {
            throw new Error(`Queue ${queueId} not found`);
        }
        const serviceSetup = await this.serviceSetupRepository.findOne({
            where: { service: { id: queue.serviceId } }
        });
        let serveTime = 15;
        if (serviceSetup?.setupData?.hasSubUnits && Array.isArray(serviceSetup.setupData.subUnits) && serviceSetup.setupData.subUnits.length > 0) {
            const subUnitId = queue.subUnitId !== undefined ? parseInt(queue.subUnitId, 10) : 0;
            const subUnitIndex = isNaN(subUnitId) ? 0 : Math.min(subUnitId, serviceSetup.setupData.subUnits.length - 1);
            const subUnit = serviceSetup.setupData.subUnits[subUnitIndex];
            if (subUnit?.avgServeTime) {
                serveTime = parseInt(subUnit.avgServeTime, 10);
                this.logger.log(`Using subunit ${subUnitIndex} (${subUnit.name}) serve time: ${serveTime} minutes`);
            }
            else {
                this.logger.log(`No serve time configured for subunit ${subUnitIndex}, using default: ${serveTime} minutes`);
            }
        }
        else if (serviceSetup?.setupData?.servingTime) {
            serveTime = parseInt(serviceSetup.setupData.servingTime, 10);
            this.logger.log(`Using service configuration serving time: ${serveTime} minutes`);
        }
        else {
            this.logger.log(`No serving time configured for service ${queue.serviceId}, using default: ${serveTime} minutes`);
        }
        const timeSlotParts = queue.timeSlot.split(' - ');
        const timeStart = timeSlotParts[0].trim();
        let startHour = 0;
        let startMinute = 0;
        if (timeStart.includes('AM') || timeStart.includes('PM')) {
            const [timeValue, period] = timeStart.split(' ');
            const [hourStr, minuteStr] = timeValue.split(':');
            let hour = parseInt(hourStr, 10);
            const minute = parseInt(minuteStr, 10);
            if (period.toUpperCase() === 'PM' && hour < 12) {
                hour += 12;
            }
            else if (period.toUpperCase() === 'AM' && hour === 12) {
                hour = 0;
            }
            startHour = hour;
            startMinute = minute;
        }
        else {
            const [hours, minutes] = timeStart.split(':').map(part => parseInt(part, 10));
            startHour = hours;
            startMinute = minutes;
        }
        const timeSlotStart = new Date(queue.date);
        timeSlotStart.setHours(startHour, startMinute, 0, 0);
        const now = new Date();
        const isServiceStarted = now > timeSlotStart;
        if (isServiceStarted) {
            this.logger.log(`Current time (${now.toISOString()}) is after time slot start time (${timeSlotStart.toISOString()}). Service has already started.`);
        }
        else {
            this.logger.log(`Current time (${now.toISOString()}) is before time slot start time (${timeSlotStart.toISOString()}). Service has not started yet.`);
        }
        const servingQueue = await this.queueRepository.findOne({
            where: {
                serviceId: queue.serviceId,
                date: queue.date,
                timeSlot: queue.timeSlot,
                status: 'serving'
            }
        });
        let baseTime;
        if (isServiceStarted) {
            if (servingQueue && servingQueue.servingStartedAt) {
                const servingStartTime = new Date(servingQueue.servingStartedAt);
                const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);
                if (elapsedMinutes >= 0) {
                    baseTime = now;
                    this.logger.log(`Someone is being served and service has already started. Using current time as base for estimated serve time calculation.`);
                }
                else {
                    baseTime = timeSlotStart;
                    this.logger.log(`Someone is being served but service hasn't started yet. Using time slot start time as base for estimated serve time calculation.`);
                }
            }
            else {
                baseTime = now;
                this.logger.log(`No one is being served but service time has started. Using current time as base for estimated serve time calculation.`);
            }
        }
        else {
            baseTime = timeSlotStart;
            this.logger.log(`Service time hasn't started yet. Using time slot start time as base for estimated serve time calculation.`);
        }
        const estimatedServeTime = new Date(baseTime);
        const positionForCalculation = queue.position;
        if (servingQueue && servingQueue.servingStartedAt && baseTime.getTime() > timeSlotStart.getTime()) {
            const servingStartTime = new Date(servingQueue.servingStartedAt);
            const now = new Date();
            const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);
            const remainingMinutes = Math.max(0, serveTime - elapsedMinutes);
            if (positionForCalculation === 1) {
                estimatedServeTime.setMinutes(estimatedServeTime.getMinutes());
            }
            else if (positionForCalculation === 2) {
                estimatedServeTime.setMinutes(estimatedServeTime.getMinutes() + remainingMinutes);
            }
            else {
                estimatedServeTime.setMinutes(estimatedServeTime.getMinutes() + remainingMinutes + (serveTime * (positionForCalculation - 2)));
            }
            this.logger.log(`Adjusted estimated serve time for queue ${queue.id} at position ${positionForCalculation} with someone being served (remaining: ${remainingMinutes} minutes).`);
        }
        else {
            estimatedServeTime.setMinutes(estimatedServeTime.getMinutes() + (serveTime * (positionForCalculation - 1)));
            this.logger.log(`Standard estimated serve time calculation for queue ${queue.id} at position ${positionForCalculation}.`);
        }
        queue.estimatedServeTime = estimatedServeTime;
        queue.waitTimeStatus = 'on-time';
        const updatedQueue = await this.queueRepository.save(queue);
        await this.updateRedisWithEstimatedServeTime(updatedQueue, serveTime);
        return updatedQueue;
    }
    async updateRedisWithEstimatedServeTime(queue, serveTime) {
        const redisKey = `queue:${queue.id}`;
        let queueData = await this.redisService.get(redisKey) || {};
        queueData = {
            ...queueData,
            id: queue.id,
            estimatedServeTime: queue.estimatedServeTime?.toISOString(),
            waitTimeStatus: queue.waitTimeStatus,
            serveTime: queue.serveTime || serveTime,
            position: queue.position,
            initialPositionAtJoin: queue.initialPositionAtJoin || queue.position
        };
        await this.redisService.set(redisKey, queueData, { ex: 3600 });
    }
    async calculateWaitTimeStatus(queueId) {
        this.logger.log(`Calculating wait time status for queue ${queueId}`);
        const queue = await this.queueRepository.findOne({
            where: { id: queueId }
        });
        if (!queue) {
            throw new Error(`Queue ${queueId} not found`);
        }
        if (!queue.estimatedServeTime) {
            await this.calculateEstimatedServeTime(queueId);
            const updatedQueue = await this.queueRepository.findOne({
                where: { id: queueId }
            });
            if (!updatedQueue || !updatedQueue.estimatedServeTime) {
                return 'on-time';
            }
            queue.estimatedServeTime = updatedQueue.estimatedServeTime;
        }
        const servingQueue = await this.queueRepository.findOne({
            where: {
                serviceId: queue.serviceId,
                date: queue.date,
                timeSlot: queue.timeSlot,
                status: 'serving'
            }
        });
        let waitTimeStatus = 'on-time';
        if (servingQueue && servingQueue.servingStartedAt) {
            const serviceSetup = await this.serviceSetupRepository.findOne({
                where: { service: { id: queue.serviceId } }
            });
            let serveTime = 15;
            if (serviceSetup?.setupData?.servingTime) {
                serveTime = parseInt(serviceSetup.setupData.servingTime, 10);
            }
            const servingStartTime = new Date(servingQueue.servingStartedAt);
            const now = new Date();
            const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);
            if (elapsedMinutes > serveTime) {
                const delayMinutes = elapsedMinutes - serveTime;
                waitTimeStatus = 'delayed';
                this.logger.log(`Queue ${queueId} wait time status: ${waitTimeStatus} (serving is delayed by ${delayMinutes} minutes)`);
            }
            else {
                const actualStartTime = new Date(servingQueue.servingStartedAt);
                const estimatedStartTime = servingQueue.estimatedServeTime || actualStartTime;
                const diffMinutes = Math.round((actualStartTime.getTime() - estimatedStartTime.getTime()) / 60000);
                if (diffMinutes < -10) {
                    waitTimeStatus = 'early';
                }
                else if (diffMinutes > 10) {
                    waitTimeStatus = 'delayed';
                }
                else {
                    waitTimeStatus = 'on-time';
                }
                this.logger.log(`Queue ${queueId} wait time status: ${waitTimeStatus} (diff: ${diffMinutes} minutes)`);
            }
        }
        else {
            const now = new Date();
            const estimatedTime = queue.estimatedServeTime;
            if (estimatedTime) {
                const diffMinutes = Math.round((now.getTime() - estimatedTime.getTime()) / 60000);
                if (diffMinutes > 10) {
                    waitTimeStatus = 'delayed';
                }
                else if (diffMinutes < -10) {
                    waitTimeStatus = 'early';
                }
                else {
                    waitTimeStatus = 'on-time';
                }
                this.logger.log(`Queue ${queueId} wait time status: ${waitTimeStatus} (diff: ${diffMinutes} minutes)`);
            }
        }
        queue.waitTimeStatus = waitTimeStatus;
        await this.queueRepository.save(queue);
        const redisKey = `queue:${queue.id}`;
        let queueData = await this.redisService.get(redisKey) || {};
        queueData.waitTimeStatus = waitTimeStatus;
        await this.redisService.set(redisKey, queueData, { ex: 3600 });
        return waitTimeStatus;
    }
    async calculateActualServiceTime(queueId) {
        this.logger.log(`Calculating actual service time for queue ${queueId}`);
        const queue = await this.queueRepository.findOne({
            where: { id: queueId }
        });
        if (!queue) {
            throw new Error(`Queue ${queueId} not found`);
        }
        if (queue.status !== 'completed' || !queue.servingStartedAt || !queue.statusUpdatedAt) {
            this.logger.log(`Queue ${queueId} was not served or is missing timestamps`);
            return 0;
        }
        const startTime = new Date(queue.servingStartedAt);
        const endTime = new Date(queue.statusUpdatedAt);
        const serviceTimeMinutes = Math.round((endTime.getTime() - startTime.getTime()) / 60000);
        const redisKey = `queue:${queue.id}`;
        let queueData = await this.redisService.get(redisKey) || {};
        queueData.actualServiceTime = serviceTimeMinutes;
        await this.redisService.set(redisKey, queueData, { ex: 86400 * 7 });
        this.logger.log(`Queue ${queueId} actual service time: ${serviceTimeMinutes} minutes`);
        return serviceTimeMinutes;
    }
    async recalculateEstimatedServeTimes(serviceId, date, timeSlot) {
        this.logger.log(`Recalculating estimated serve times for service ${serviceId}, date ${date}, timeSlot ${timeSlot}`);
        const waitingQueues = await this.queueRepository.find({
            where: {
                serviceId,
                date,
                timeSlot,
                status: 'waiting'
            },
            order: {
                position: 'ASC'
            }
        });
        this.logger.log(`Found ${waitingQueues.length} waiting queues to recalculate`);
        const serviceSetup = await this.serviceSetupRepository.findOne({
            where: { service: { id: serviceId } }
        });
        let serveTime = 15;
        const subunitServeTimes = new Map();
        if (serviceSetup?.setupData?.hasSubUnits && Array.isArray(serviceSetup.setupData.subUnits) && serviceSetup.setupData.subUnits.length > 0) {
            serviceSetup.setupData.subUnits.forEach((subUnit, index) => {
                if (subUnit?.avgServeTime) {
                    const subUnitServeTime = parseInt(subUnit.avgServeTime, 10);
                    subunitServeTimes.set(index.toString(), subUnitServeTime);
                    this.logger.log(`Stored subunit ${index} (${subUnit.name}) serve time: ${subUnitServeTime} minutes`);
                }
            });
            if (subunitServeTimes.has('0')) {
                serveTime = subunitServeTimes.get('0') || 15;
                this.logger.log(`Using first subunit's serve time as default: ${serveTime} minutes`);
            }
        }
        else if (serviceSetup?.setupData?.servingTime) {
            serveTime = parseInt(serviceSetup.setupData.servingTime, 10);
            this.logger.log(`Using service configuration serving time: ${serveTime} minutes`);
        }
        const timeSlotParts = timeSlot.split(' - ');
        const timeStart = timeSlotParts[0].trim();
        let startHour = 0;
        let startMinute = 0;
        if (timeStart.includes('AM') || timeStart.includes('PM')) {
            const [timeValue, period] = timeStart.split(' ');
            const [hourStr, minuteStr] = timeValue.split(':');
            let hour = parseInt(hourStr, 10);
            const minute = parseInt(minuteStr, 10);
            if (period.toUpperCase() === 'PM' && hour < 12) {
                hour += 12;
            }
            else if (period.toUpperCase() === 'AM' && hour === 12) {
                hour = 0;
            }
            startHour = hour;
            startMinute = minute;
        }
        else {
            const [hours, minutes] = timeStart.split(':').map(part => parseInt(part, 10));
            startHour = hours;
            startMinute = minutes;
        }
        const timeSlotStart = new Date(date);
        timeSlotStart.setHours(startHour, startMinute, 0, 0);
        const now = new Date();
        const isServiceStarted = now > timeSlotStart;
        if (isServiceStarted) {
            this.logger.log(`Current time (${now.toISOString()}) is after time slot start time (${timeSlotStart.toISOString()}). Service has already started.`);
        }
        else {
            this.logger.log(`Current time (${now.toISOString()}) is before time slot start time (${timeSlotStart.toISOString()}). Service has not started yet.`);
        }
        const servingQueue = await this.queueRepository.findOne({
            where: {
                serviceId,
                date,
                timeSlot,
                status: 'serving'
            }
        });
        for (const queue of waitingQueues) {
            let queueServeTime = serveTime;
            if (queue.subUnitId !== undefined && subunitServeTimes.has(queue.subUnitId)) {
                queueServeTime = subunitServeTimes.get(queue.subUnitId) || serveTime;
                this.logger.log(`Using subunit ${queue.subUnitId} serve time for queue ${queue.id}: ${queueServeTime} minutes`);
            }
            let baseTime;
            if (isServiceStarted) {
                if (servingQueue && servingQueue.servingStartedAt) {
                    const servingStartTime = new Date(servingQueue.servingStartedAt);
                    const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);
                    if (elapsedMinutes >= 0) {
                        baseTime = now;
                        this.logger.log(`Someone is being served and service has already started. Using current time as base for estimated serve time calculation.`);
                    }
                    else {
                        baseTime = timeSlotStart;
                        this.logger.log(`Someone is being served but service hasn't started yet. Using time slot start time as base for estimated serve time calculation.`);
                    }
                }
                else {
                    baseTime = now;
                    this.logger.log(`No one is being served but service time has started. Using current time as base for estimated serve time calculation.`);
                }
            }
            else {
                baseTime = timeSlotStart;
                this.logger.log(`Service time hasn't started yet. Using time slot start time as base for estimated serve time calculation.`);
            }
            const estimatedServeTime = new Date(baseTime);
            const positionForCalculation = queue.position;
            if (servingQueue && servingQueue.servingStartedAt && baseTime.getTime() > timeSlotStart.getTime()) {
                const servingStartTime = new Date(servingQueue.servingStartedAt);
                const now = new Date();
                const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);
                const remainingMinutes = Math.max(0, queueServeTime - elapsedMinutes);
                if (positionForCalculation === 1) {
                    estimatedServeTime.setMinutes(estimatedServeTime.getMinutes());
                }
                else if (positionForCalculation === 2) {
                    estimatedServeTime.setMinutes(estimatedServeTime.getMinutes() + remainingMinutes);
                }
                else {
                    estimatedServeTime.setMinutes(estimatedServeTime.getMinutes() + remainingMinutes + (queueServeTime * (positionForCalculation - 2)));
                }
                this.logger.log(`Adjusted estimated serve time for queue ${queue.id} at position ${positionForCalculation} with someone being served (remaining: ${remainingMinutes} minutes).`);
            }
            else {
                estimatedServeTime.setMinutes(estimatedServeTime.getMinutes() + (queueServeTime * (positionForCalculation - 1)));
                this.logger.log(`Standard estimated serve time calculation for queue ${queue.id} at position ${positionForCalculation}.`);
            }
            let individualShiftMinutes = 0;
            if (servingQueue && servingQueue.servingStartedAt && servingQueue.estimatedServeTime) {
                const actualStartTime = new Date(servingQueue.servingStartedAt);
                const estimatedStartTime = new Date(servingQueue.estimatedServeTime);
                individualShiftMinutes = Math.round((actualStartTime.getTime() - estimatedStartTime.getTime()) / 60000);
                this.logger.log(`Service is ${individualShiftMinutes > 0 ? 'delayed' : 'early'} by ${Math.abs(individualShiftMinutes)} minutes for queue ${queue.id}`);
            }
            estimatedServeTime.setMinutes(estimatedServeTime.getMinutes() + individualShiftMinutes);
            queue.estimatedServeTime = estimatedServeTime;
            queue.serveTime = queueServeTime;
            if (individualShiftMinutes < -10) {
                queue.waitTimeStatus = 'early';
            }
            else if (individualShiftMinutes > 10) {
                queue.waitTimeStatus = 'delayed';
            }
            else {
                queue.waitTimeStatus = 'on-time';
            }
            const redisKey = `queue:${queue.id}`;
            let queueData = await this.redisService.get(redisKey) || {};
            queueData.estimatedServeTime = estimatedServeTime.toISOString();
            queueData.waitTimeStatus = queue.waitTimeStatus;
            queueData.shiftMinutes = individualShiftMinutes;
            queueData.position = queue.position;
            queueData.initialPositionAtJoin = queue.initialPositionAtJoin || queue.position;
            queueData.serveTime = queue.serveTime || queueServeTime;
            await this.redisService.set(redisKey, queueData, { ex: 3600 });
        }
        if (waitingQueues.length > 0) {
            await this.queueRepository.save(waitingQueues);
        }
        this.logger.log(`Recalculated estimated serve times for ${waitingQueues.length} queues`);
    }
    async getEstimatedWaitTime(queueId) {
        this.logger.log(`Getting estimated wait time for queue ${queueId}`);
        const queue = await this.queueRepository.findOne({
            where: { id: queueId }
        });
        if (!queue) {
            throw new Error(`Queue ${queueId} not found`);
        }
        const timeSlotParts = queue.timeSlot.split(' - ');
        const timeStart = timeSlotParts[0].trim();
        let startHour = 0;
        let startMinute = 0;
        if (timeStart.includes('AM') || timeStart.includes('PM')) {
            const [timeValue, period] = timeStart.split(' ');
            const [hourStr, minuteStr] = timeValue.split(':');
            let hour = parseInt(hourStr, 10);
            const minute = parseInt(minuteStr, 10);
            if (period.toUpperCase() === 'PM' && hour < 12) {
                hour += 12;
            }
            else if (period.toUpperCase() === 'AM' && hour === 12) {
                hour = 0;
            }
            startHour = hour;
            startMinute = minute;
        }
        else {
            const [hours, minutes] = timeStart.split(':').map(part => parseInt(part, 10));
            startHour = hours;
            startMinute = minutes;
        }
        const timeSlotStart = new Date(queue.date);
        timeSlotStart.setHours(startHour, startMinute, 0, 0);
        const now = new Date();
        const isServiceStarted = now > timeSlotStart;
        if (isServiceStarted) {
            this.logger.log(`Current time (${now.toISOString()}) is after time slot start time (${timeSlotStart.toISOString()}). Service has already started.`);
        }
        else {
            this.logger.log(`Current time (${now.toISOString()}) is before time slot start time (${timeSlotStart.toISOString()}). Service has not started yet.`);
        }
        if (!queue.estimatedServeTime) {
            await this.calculateEstimatedServeTime(queueId);
            const updatedQueue = await this.queueRepository.findOne({
                where: { id: queueId }
            });
            if (!updatedQueue || !updatedQueue.estimatedServeTime) {
                return {
                    waitTimeMinutes: 0,
                    waitTimeStatus: 'on-time',
                    estimatedServeTime: new Date(),
                    position: 0,
                    initialPositionAtJoin: 0
                };
            }
            queue.estimatedServeTime = updatedQueue.estimatedServeTime;
            queue.waitTimeStatus = updatedQueue.waitTimeStatus;
        }
        const serviceSetup = await this.serviceSetupRepository.findOne({
            where: { service: { id: queue.serviceId } }
        });
        let serveTime = 15;
        if (serviceSetup?.setupData?.hasSubUnits && Array.isArray(serviceSetup.setupData.subUnits) && serviceSetup.setupData.subUnits.length > 0) {
            const subUnitId = queue.subUnitId !== undefined ? parseInt(queue.subUnitId, 10) : 0;
            const subUnitIndex = isNaN(subUnitId) ? 0 : Math.min(subUnitId, serviceSetup.setupData.subUnits.length - 1);
            const subUnit = serviceSetup.setupData.subUnits[subUnitIndex];
            if (subUnit?.avgServeTime) {
                serveTime = parseInt(subUnit.avgServeTime, 10);
                this.logger.log(`Using subunit ${subUnitIndex} (${subUnit.name}) serve time: ${serveTime} minutes`);
            }
        }
        else if (serviceSetup?.setupData?.servingTime) {
            serveTime = parseInt(serviceSetup.setupData.servingTime, 10);
            this.logger.log(`Using service setup serve time: ${serveTime} minutes`);
        }
        if (queue.serveTime) {
            serveTime = queue.serveTime;
            this.logger.log(`Using queue's stored serve time: ${serveTime} minutes`);
        }
        const servingQueue = await this.queueRepository.findOne({
            where: {
                serviceId: queue.serviceId,
                date: queue.date,
                timeSlot: queue.timeSlot,
                status: 'serving'
            }
        });
        let waitTimeMinutes = 0;
        if (queue.position === 1) {
            if (!servingQueue) {
                waitTimeMinutes = serveTime;
                this.logger.log(`Queue ${queueId} is at position 1 and no one is being served. Wait time: ${waitTimeMinutes} minutes`);
            }
            else {
                if (servingQueue.servingStartedAt) {
                    const servingStartTime = new Date(servingQueue.servingStartedAt);
                    const now = new Date();
                    const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);
                    const remainingMinutes = Math.max(0, serveTime - elapsedMinutes);
                    waitTimeMinutes = remainingMinutes;
                    this.logger.log(`Queue ${queueId} is at position 1 and someone is being served. Remaining time: ${remainingMinutes} minutes`);
                }
                else {
                    waitTimeMinutes = serveTime;
                    this.logger.log(`Queue ${queueId} is at position 1 and someone is being served but no start time. Using full serve time: ${serveTime} minutes`);
                }
            }
        }
        else {
            const positionForCalculation = queue.position;
            let baseWaitTime = 0;
            if (servingQueue && servingQueue.servingStartedAt) {
                const servingStartTime = new Date(servingQueue.servingStartedAt);
                const now = new Date();
                const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);
                const remainingMinutes = Math.max(0, serveTime - elapsedMinutes);
                this.logger.log(`Someone is being served with ${remainingMinutes} minutes remaining out of ${serveTime} minutes total.`);
                if (positionForCalculation === 2) {
                    baseWaitTime = remainingMinutes;
                    this.logger.log(`Queue ${queueId} at position ${positionForCalculation}, base wait time is remaining time for position 1: ${baseWaitTime} minutes`);
                }
                else {
                    baseWaitTime = remainingMinutes + (serveTime * (positionForCalculation - 2));
                    this.logger.log(`Queue ${queueId} at position ${positionForCalculation}, base wait time is remaining time for position 1 plus serve time for positions in between: ${baseWaitTime} minutes`);
                }
            }
            else {
                if (positionForCalculation === 2) {
                    baseWaitTime = serveTime;
                    this.logger.log(`Queue ${queueId} at position ${positionForCalculation}, base wait time is just position 1's serve time: ${baseWaitTime} minutes`);
                }
                else {
                    baseWaitTime = (positionForCalculation - 1) * serveTime;
                    this.logger.log(`Queue ${queueId} at position ${positionForCalculation}, base wait time for all positions ahead: ${baseWaitTime} minutes`);
                }
            }
            const bufferFactor = 1.15;
            waitTimeMinutes = Math.ceil(baseWaitTime * bufferFactor);
            this.logger.log(`Queue ${queueId} at position ${positionForCalculation}, base wait time: ${baseWaitTime} minutes, with buffer: ${waitTimeMinutes} minutes`);
            if (servingQueue && servingQueue.servingStartedAt) {
                const servingStartTime = new Date(servingQueue.servingStartedAt);
                const now = new Date();
                const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);
                const remainingMinutes = Math.max(0, serveTime - elapsedMinutes);
                const isDelayed = elapsedMinutes > serveTime;
                let delayFactor = 1.0;
                if (isDelayed) {
                    const delayMinutes = elapsedMinutes - serveTime;
                    const delayPercentage = delayMinutes / serveTime;
                    delayFactor = Math.min(1.5, 1.0 + delayPercentage);
                    this.logger.log(`Serving is delayed by ${delayMinutes} minutes (${Math.round(delayPercentage * 100)}% of serve time), using delay factor: ${delayFactor.toFixed(2)}`);
                }
                if (positionForCalculation === 2) {
                    if (isDelayed) {
                        const additionalTime = Math.ceil(serveTime * (delayFactor - 1.0) * 0.5);
                        waitTimeMinutes = Math.ceil(remainingMinutes * bufferFactor) + additionalTime;
                        this.logger.log(`Position 1 is delayed, adding ${additionalTime} minutes to position 2 wait time`);
                    }
                    else {
                        waitTimeMinutes = Math.ceil(remainingMinutes * bufferFactor);
                    }
                    this.logger.log(`Position 1 is being served with ${remainingMinutes} minutes remaining, position 2 wait time: ${waitTimeMinutes} minutes`);
                }
                else {
                    if (isDelayed) {
                        const positionMultiplier = Math.min(1.0, (positionForCalculation - 2) * 0.2);
                        const additionalTime = Math.ceil(serveTime * (delayFactor - 1.0) * positionMultiplier);
                        waitTimeMinutes = Math.ceil((baseWaitTime - (serveTime - remainingMinutes)) * bufferFactor) + additionalTime;
                        this.logger.log(`Position 1 is delayed, adding ${additionalTime} minutes to position ${positionForCalculation} wait time`);
                    }
                    else {
                        waitTimeMinutes = Math.ceil((baseWaitTime - (serveTime - remainingMinutes)) * bufferFactor);
                    }
                    this.logger.log(`Position 1 is being served with ${remainingMinutes} minutes remaining, position ${positionForCalculation} adjusted wait time: ${waitTimeMinutes} minutes`);
                }
            }
        }
        await this.calculateWaitTimeStatus(queueId);
        const refreshedQueue = await this.queueRepository.findOne({
            where: { id: queueId }
        });
        return {
            waitTimeMinutes,
            waitTimeStatus: refreshedQueue?.waitTimeStatus || 'on-time',
            estimatedServeTime: refreshedQueue?.estimatedServeTime || new Date(),
            position: refreshedQueue?.position || 0,
            initialPositionAtJoin: refreshedQueue?.initialPositionAtJoin || 0
        };
    }
    async getActualServiceTime(queueId) {
        this.logger.log(`Getting actual service time for queue ${queueId}`);
        const queue = await this.queueRepository.findOne({
            where: { id: queueId }
        });
        if (!queue) {
            throw new Error(`Queue ${queueId} not found`);
        }
        const response = {
            serviceTimeMinutes: 0,
            servingStartedAt: queue.servingStartedAt,
            statusUpdatedAt: queue.statusUpdatedAt,
            status: queue.status
        };
        if (queue.status !== 'completed' || !queue.servingStartedAt || !queue.statusUpdatedAt) {
            this.logger.log(`Queue ${queueId} was not served or is missing timestamps`);
            return response;
        }
        const startTime = new Date(queue.servingStartedAt);
        const endTime = new Date(queue.statusUpdatedAt);
        const serviceTimeMinutes = Math.round((endTime.getTime() - startTime.getTime()) / 60000);
        response.serviceTimeMinutes = serviceTimeMinutes;
        this.logger.log(`Queue ${queueId} actual service time: ${serviceTimeMinutes} minutes`);
        return response;
    }
    async moveToEndOfLine(queueId) {
        this.logger.log(`Moving queue ${queueId} to the end of the line`);
        const queue = await this.queueRepository.findOne({
            where: { id: queueId }
        });
        if (!queue) {
            throw new Error(`Queue ${queueId} not found`);
        }
        const allWaitingQueues = await this.queueRepository.find({
            where: {
                serviceId: queue.serviceId,
                date: queue.date,
                timeSlot: queue.timeSlot,
                status: 'waiting',
            },
            order: {
                position: 'ASC',
                createdAt: 'ASC'
            }
        });
        this.logger.log(`Found ${allWaitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);
        const newPosition = allWaitingQueues.length;
        queue.position = newPosition;
        const updatedQueue = await this.queueRepository.save(queue);
        const redisKey = `queue:${queue.id}`;
        let queueData = await this.redisService.get(redisKey) || {};
        queueData.position = newPosition;
        if (!queueData.initialPositionAtJoin) {
            queueData.initialPositionAtJoin = queue.initialPositionAtJoin || queue.position;
        }
        await this.redisService.set(redisKey, queueData, { ex: 3600 });
        const dateStr = new Date(queue.date).toISOString().split('T')[0];
        const positionsKey = `service:${queue.serviceId}:positions:${dateStr}:${queue.timeSlot}`;
        const positionsData = await this.redisService.get(positionsKey) || {};
        positionsData[queue.id] = newPosition;
        await this.redisService.set(positionsKey, positionsData);
        await this.redisService.saveQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot, positionsData);
        await this.redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);
        await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
        await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');
        await this.recalculateEstimatedServeTimes(queue.serviceId, queue.date, queue.timeSlot);
        return updatedQueue;
    }
    async recalculateAllEstimatedServeTimes() {
        this.logger.log('Running scheduled job to recalculate all estimated serve times');
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const activeServices = await this.queueRepository
            .createQueryBuilder('queue')
            .select('DISTINCT queue.serviceId, queue.date, queue.timeSlot')
            .where('queue.date >= :today', { today })
            .andWhere('queue.status = :status', { status: 'waiting' })
            .getRawMany();
        this.logger.log(`Found ${activeServices.length} active services to recalculate`);
        for (const service of activeServices) {
            try {
                await this.recalculateEstimatedServeTimes(service.serviceId, new Date(service.date), service.timeSlot);
            }
            catch (error) {
                this.logger.error(`Error recalculating for service ${service.serviceId}:`, error);
            }
        }
        this.logger.log('Completed recalculation of all estimated serve times');
    }
};
exports.QueueFlowService = QueueFlowService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_30_SECONDS),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], QueueFlowService.prototype, "checkGracePeriods", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_5_MINUTES),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], QueueFlowService.prototype, "recalculateAllEstimatedServeTimes", null);
exports.QueueFlowService = QueueFlowService = QueueFlowService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(queue_entity_1.Queue)),
    __param(1, (0, typeorm_1.InjectRepository)(service_setup_entity_1.ServiceSetup)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        redis_service_1.RedisService])
], QueueFlowService);
//# sourceMappingURL=queue-flow.service.js.map