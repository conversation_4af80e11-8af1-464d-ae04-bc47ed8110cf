"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisService = void 0;
const common_1 = require("@nestjs/common");
const redis_1 = require("@upstash/redis");
let RedisService = class RedisService {
    constructor() {
        const redisUrl = process.env.REDIS_URL || '';
        const redisToken = process.env.REDIS_TOKEN || '';
        if (!redisUrl || !redisToken) {
            throw new Error('Redis configuration missing. Please set REDIS_URL and REDIS_TOKEN environment variables.');
        }
        this.redis = new redis_1.Redis({
            url: redisUrl,
            token: redisToken,
        });
    }
    async get(key) {
        return this.redis.get(key);
    }
    async set(key, value, options) {
        if (options?.ex) {
            await this.redis.set(key, value, { ex: options.ex });
        }
        else {
            await this.redis.set(key, value);
        }
    }
    async getQueue(queueId) {
        const queueKey = `queue:${queueId}`;
        return this.redis.get(queueKey);
    }
    async saveQueue(queueId, queueData, ttlSeconds = 86400) {
        await this.cleanupOldQueueKeys(queueId);
        const queueKey = `queue:${queueId}`;
        const status = queueData.status || 'waiting';
        let ttl = ttlSeconds;
        if (status === 'no-show' || status === 'completed' || status === 'cancelled') {
            ttl = 86400 * 7;
        }
        await this.redis.set(queueKey, queueData, { ex: ttl });
        console.log(`Redis: Saved queue ${queueId} with status ${status} to key ${queueKey}`);
    }
    async cleanupOldQueueKeys(queueId) {
        try {
            const statusKeys = await this.redis.keys(`queue:${queueId}:status:*`);
            if (statusKeys && statusKeys.length > 0) {
                console.log(`Redis: Found ${statusKeys.length} old format keys for queue ${queueId}. Cleaning up...`);
                for (const key of statusKeys) {
                    await this.redis.del(key);
                    console.log(`Redis: Deleted old format key: ${key}`);
                }
            }
        }
        catch (error) {
            console.error(`Redis: Error cleaning up old format keys for queue ${queueId}:`, error);
        }
    }
    async updateQueueStatus(queueId, status, isPriority = false) {
        await this.cleanupOldQueueKeys(queueId);
        const queueKey = `queue:${queueId}`;
        let existingData = await this.redis.get(queueKey);
        if (existingData) {
            const existingDataObj = existingData;
            const isCheckedIn = existingDataObj.isCheckedIn === true;
            const previousStatus = existingDataObj.status || 'unknown';
            const finalStatusAlreadySet = existingDataObj.finalStatusSet === true;
            if (finalStatusAlreadySet &&
                (previousStatus === 'no-show' || previousStatus === 'completed' || previousStatus === 'cancelled') &&
                (status === 'waiting' || status === 'serving' || status === 'active') &&
                !isPriority) {
                console.log(`Redis: Prevented overwriting final status ${previousStatus} with ${status} for queue ${queueId}`);
                return;
            }
            const updatedData = {
                ...JSON.parse(JSON.stringify(existingData)),
                status: status,
                isCheckedIn: isCheckedIn,
                statusLastUpdatedAt: new Date().toISOString(),
                statusUpdatedAt: new Date().toISOString(),
                finalStatusSet: status === 'no-show' || status === 'completed' || status === 'cancelled',
                previousStatus: previousStatus
            };
            console.log(`Redis: Updating queue ${queueId} status from ${previousStatus} to ${status}, isCheckedIn: ${isCheckedIn}`);
            let ttl = 86400;
            if (status === 'no-show' || status === 'completed' || status === 'cancelled') {
                ttl = 86400 * 7;
            }
            else if (status === 'serving') {
                ttl = 86400;
            }
            else if (status === 'waiting') {
                ttl = isPriority ? 3600 : 21600;
            }
            await this.redis.set(queueKey, updatedData, { ex: ttl });
            try {
                const checkData = await this.redis.get(queueKey);
                if (checkData) {
                    const checkObj = checkData;
                    console.log(`Redis: Verified queue ${queueId} now has status: ${checkObj.status}`);
                    if (checkObj.status !== status) {
                        console.warn(`Redis: Status mismatch detected! Force updating queue ${queueId}`);
                        updatedData.status = status;
                        updatedData.statusLastUpdatedAt = new Date().toISOString();
                        updatedData.forceUpdated = true;
                        await this.redis.set(queueKey, updatedData, { ex: ttl });
                        console.log(`Redis: Force-updated queue ${queueId} with status ${status}`);
                    }
                }
            }
            catch (error) {
                console.error(`Redis: Error verifying status update for queue ${queueId}:`, error);
            }
        }
        else {
            console.error(`Redis: No existing data found for queue ${queueId}, cannot update status`);
        }
    }
    async updateQueueCheckInStatus(queueId, isCheckedIn, isPriority = false) {
        const redisKey = `queue:${queueId}`;
        const existingData = await this.redis.get(redisKey);
        if (existingData) {
            const updatedData = {
                ...existingData,
                isCheckedIn,
                checkInUpdatedAt: new Date().toISOString()
            };
            const ttl = isPriority ? 3600 : 21600;
            await this.redis.set(redisKey, updatedData, { ex: ttl });
        }
    }
    async deleteQueue(queueId) {
        await this.cleanupOldQueueKeys(queueId);
        const queueKey = `queue:${queueId}`;
        await this.redis.del(queueKey);
        console.log(`Redis: Deleted queue key ${queueKey}`);
    }
    async saveQueuePosition(serviceId, date, timeSlot, positionData) {
        const redisKey = `service:${serviceId}:position:${date}:${timeSlot}`;
        await this.redis.set(redisKey, positionData, { ex: 600 });
    }
    async getQueuePosition(serviceId, date, timeSlot) {
        const redisKey = `service:${serviceId}:position:${date}:${timeSlot}`;
        return this.redis.get(redisKey);
    }
    async saveServiceQueues(serviceId, date, queuesData) {
        const redisKey = `service:${serviceId}:queues:${date}`;
        await this.redis.set(redisKey, queuesData, { ex: 300 });
    }
    async getServiceQueues(serviceId, date) {
        const redisKey = `service:${serviceId}:queues:${date}`;
        return this.redis.get(redisKey);
    }
    async invalidateServiceQueues(serviceId, date) {
        const redisKey = `service:${serviceId}:queues:${date}`;
        await this.redis.del(redisKey);
    }
    async del(key) {
        await this.redis.del(key);
    }
    async getKeys(pattern) {
        return this.redis.keys(pattern);
    }
    async mget(...keys) {
        return this.redis.mget(...keys);
    }
    async invalidateQueuePosition(serviceId, date, timeSlot) {
        const redisKey = `service:${serviceId}:position:${date}:${timeSlot}`;
        await this.redis.del(redisKey);
        console.log(`Invalidated queue position cache for ${redisKey}`);
    }
    async migrateAllQueueKeys() {
        try {
            console.log('Starting migration of all queue keys to new format...');
            const statusKeys = await this.redis.keys('queue:*:status:*');
            console.log(`Found ${statusKeys.length} status-specific keys to migrate`);
            for (const statusKey of statusKeys) {
                try {
                    const match = statusKey.match(/queue:(\d+):status:/);
                    if (match && match[1]) {
                        const queueId = match[1];
                        const data = await this.redis.get(statusKey);
                        if (data) {
                            const newKey = `queue:${queueId}`;
                            console.log(`Migrating ${statusKey} to ${newKey}`);
                            let ttl = 86400;
                            const dataObj = data;
                            if (dataObj.status === 'no-show' || dataObj.status === 'completed' || dataObj.status === 'cancelled') {
                                ttl = 86400 * 7;
                            }
                            await this.redis.set(newKey, data, { ex: ttl });
                            await this.redis.del(statusKey);
                        }
                    }
                }
                catch (error) {
                    console.error(`Error migrating key ${statusKey}:`, error);
                }
            }
            console.log('Migration of queue keys completed');
        }
        catch (error) {
            console.error('Error during queue key migration:', error);
        }
    }
    async refreshServingQueuesTTL() {
        try {
            const queueKeys = await this.redis.keys('queue:*');
            if (!queueKeys || queueKeys.length === 0) {
                console.log('Redis: No queue keys found to refresh');
                return;
            }
            console.log(`Redis: Found ${queueKeys.length} queue keys, checking for serving status`);
            const queuesData = await this.mget(...queueKeys);
            let refreshedCount = 0;
            for (let i = 0; i < queueKeys.length; i++) {
                const key = queueKeys[i];
                const data = queuesData[i];
                if (!data)
                    continue;
                if (data.status === 'serving') {
                    await this.redis.expire(key, 86400);
                    refreshedCount++;
                    console.log(`Redis: Refreshed TTL for serving queue ${key}`);
                }
            }
            console.log(`Redis: Refreshed TTL for ${refreshedCount} serving queues`);
        }
        catch (error) {
            console.error('Redis: Error refreshing serving queues TTL:', error);
        }
    }
};
exports.RedisService = RedisService;
exports.RedisService = RedisService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], RedisService);
//# sourceMappingURL=redis.service.js.map