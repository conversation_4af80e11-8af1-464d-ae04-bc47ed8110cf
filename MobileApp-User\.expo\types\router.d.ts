/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/AppRouter`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/verify-otp` | `/verify-otp`; params?: Router.UnknownInputParams; } | { pathname: `${'/(location)'}/allow-location` | `/allow-location`; params?: Router.UnknownInputParams; } | { pathname: `${'/(location)'}/select-location` | `/select-location`; params?: Router.UnknownInputParams; } | { pathname: `${'/(notifications)'}/notifications` | `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/add` | `/add`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/enter-details` | `/enter-details`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/join-queue` | `/join-queue`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/payment-success` | `/payment-success`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/queue-completed` | `/queue-completed`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/queue-status` | `/queue-status`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/service-details` | `/service-details`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/summary` | `/summary`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/create` | `/create`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/home` | `/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/queues` | `/queues`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/wishlist` | `/wishlist`; params?: Router.UnknownInputParams; } | { pathname: `${'/(vip)'}/join-vip` | `/join-vip`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/AppRouter`; params?: Router.UnknownOutputParams; } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/verify-otp` | `/verify-otp`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(location)'}/allow-location` | `/allow-location`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(location)'}/select-location` | `/select-location`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(notifications)'}/notifications` | `/notifications`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/add` | `/add`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/enter-details` | `/enter-details`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/join-queue` | `/join-queue`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/payment-success` | `/payment-success`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/queue-completed` | `/queue-completed`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/queue-status` | `/queue-status`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/service-details` | `/service-details`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/summary` | `/summary`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/create` | `/create`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/home` | `/home`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/queues` | `/queues`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/wishlist` | `/wishlist`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(vip)'}/join-vip` | `/join-vip`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/AppRouter${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-in${`?${string}` | `#${string}` | ''}` | `/sign-in${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/verify-otp${`?${string}` | `#${string}` | ''}` | `/verify-otp${`?${string}` | `#${string}` | ''}` | `${'/(location)'}/allow-location${`?${string}` | `#${string}` | ''}` | `/allow-location${`?${string}` | `#${string}` | ''}` | `${'/(location)'}/select-location${`?${string}` | `#${string}` | ''}` | `/select-location${`?${string}` | `#${string}` | ''}` | `${'/(notifications)'}/notifications${`?${string}` | `#${string}` | ''}` | `/notifications${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/add${`?${string}` | `#${string}` | ''}` | `/add${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/enter-details${`?${string}` | `#${string}` | ''}` | `/enter-details${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/join-queue${`?${string}` | `#${string}` | ''}` | `/join-queue${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/payment-success${`?${string}` | `#${string}` | ''}` | `/payment-success${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/queue-completed${`?${string}` | `#${string}` | ''}` | `/queue-completed${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/queue-status${`?${string}` | `#${string}` | ''}` | `/queue-status${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/service-details${`?${string}` | `#${string}` | ''}` | `/service-details${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/summary${`?${string}` | `#${string}` | ''}` | `/summary${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(tabs)'}/create${`?${string}` | `#${string}` | ''}` | `/create${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(tabs)'}/home${`?${string}` | `#${string}` | ''}` | `/home${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(tabs)'}/queues${`?${string}` | `#${string}` | ''}` | `/queues${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(tabs)'}/wishlist${`?${string}` | `#${string}` | ''}` | `/wishlist${`?${string}` | `#${string}` | ''}` | `${'/(vip)'}/join-vip${`?${string}` | `#${string}` | ''}` | `/join-vip${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/AppRouter`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/verify-otp` | `/verify-otp`; params?: Router.UnknownInputParams; } | { pathname: `${'/(location)'}/allow-location` | `/allow-location`; params?: Router.UnknownInputParams; } | { pathname: `${'/(location)'}/select-location` | `/select-location`; params?: Router.UnknownInputParams; } | { pathname: `${'/(notifications)'}/notifications` | `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/add` | `/add`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/enter-details` | `/enter-details`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/join-queue` | `/join-queue`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/payment-success` | `/payment-success`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/queue-completed` | `/queue-completed`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/queue-status` | `/queue-status`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/service-details` | `/service-details`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/summary` | `/summary`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/create` | `/create`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/home` | `/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/queues` | `/queues`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/wishlist` | `/wishlist`; params?: Router.UnknownInputParams; } | { pathname: `${'/(vip)'}/join-vip` | `/join-vip`; params?: Router.UnknownInputParams; } | `/+not-found` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
