import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CustomerController } from './customer.controller';
import { CustomerService } from './customer.service';
import { User } from './user.entity';
import { Queue } from '../partner/entities/queue.entity';
import { Service } from '../partner/entities/service.entity';
import { ServiceSetup } from '../partner/entities/service-setup.entity';
import { RedisModule } from '../services/redis/redis.module';
import { QueueFlowModule } from '../services/queue-flow/queue-flow.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Queue, Service, ServiceSetup]),
    RedisModule,
    QueueFlowModule,
  ],
  controllers: [CustomerController],
  providers: [CustomerService],
  exports: [CustomerService],
})
export class CustomerModule {}