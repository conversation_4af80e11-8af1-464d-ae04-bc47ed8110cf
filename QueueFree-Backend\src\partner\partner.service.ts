import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Service } from './entities/service.entity';
import { ServiceSetup } from './entities/service-setup.entity';
import { BankDetails } from './entities/bank-details.entity';
import { Queue } from './entities/queue.entity';
import { UploadService } from '../services/upload.service';
import { Review } from './entities/review.entity';
import { RedisService } from '../services/redis/redis.service';
import { QueueFlowService } from '../services/queue-flow/queue-flow.service';

@Injectable()
export class PartnerService {
  constructor(
    @InjectRepository(Service)
    private readonly serviceRepository: Repository<Service>,
    @InjectRepository(ServiceSetup)
    private readonly setupRepository: Repository<ServiceSetup>,
    @InjectRepository(BankDetails)
    private readonly bankDetailsRepository: Repository<BankDetails>,
    @InjectRepository(Queue)
    private readonly queueRepository: Repository<Queue>,
    @InjectRepository(Review)
    private readonly reviewRepository: Repository<Review>,
    private readonly uploadService: UploadService,
    private readonly redisService: RedisService,
    private readonly queueFlowService: QueueFlowService,
  ) {}

  async createPartner(email: string) {
    if (!email) {
      throw new BadRequestException('Email is required');
    }

    try {
      console.log('Checking for existing partner:', email);
      let service = await this.serviceRepository.findOne({ where: { email } });

      if (service) {
        console.log('Partner already exists:', email);
        return {
          status: 'success',
          message: 'Partner already registered',
          isExisting: true,
          serviceId: service.id // Add this
        };
      }

      console.log('Creating new service entry for partner:', email);
      service = this.serviceRepository.create({ email });
      const savedService = await this.serviceRepository.save(service);
      console.log('New service entry created for partner:', savedService);

      return {
        status: 'success',
        message: 'Partner registered successfully',
        isExisting: false,
        serviceId: savedService.id // Add this
      };
    } catch (error) {
      console.error('Error in createPartner:', error);
      throw new BadRequestException('Failed to process partner registration: ' + error.message);
    }
  }

  async registerService(serviceData: any) {
    try {
      // Find existing service by email
      const existingService = await this.serviceRepository.findOne({
        where: { email: serviceData.email }
      });

      if (!existingService) {
        throw new NotFoundException('Service not found. Please register first.');
      }

      // Upload images
      const uploadedImageUrls = await this.uploadService.uploadMultipleImages(serviceData.images);

      // Handle PAN card image
      let panCardImageUrl;
      if (serviceData.documents?.panCardImage?.base64) {
        panCardImageUrl = await this.uploadService.uploadImage(
          serviceData.documents.panCardImage,
          `documents/pan/${Date.now()}-${existingService.id}.jpg`
        );
      } else {
        throw new BadRequestException('PAN card image data is missing or invalid');
      }

      // Update existing service
      existingService.serviceName = serviceData.serviceName.trim();
      existingService.serviceType = serviceData.serviceType;
      existingService.businessPhone = serviceData.businessPhone?.trim();
      existingService.serviceDescription = serviceData.serviceDescription?.trim();
      existingService.address = serviceData.address;
      existingService.images = uploadedImageUrls;
      existingService.documents = {
        panNumber: serviceData.documents.panNumber,
        gstin: serviceData.documents.gstin,
        panCardImage: panCardImageUrl
      };
      existingService.verificationStatus = 'pending';

      const savedService = await this.serviceRepository.save(existingService);

      return {
        serviceId: savedService.id,
        status: 'success'
      };
    } catch (error) {
      console.error('Error in registerService:', error);
      throw new BadRequestException(error.message || 'Failed to register service');
    }
  }

  async updateService(serviceData: any) {
    try {
      const existingService = await this.serviceRepository.findOne({
        where: { email: serviceData.email }
      });

      if (!existingService) {
        throw new NotFoundException('Service not found');
      }

      // Address is now passed as complete object
      existingService.serviceName = serviceData.serviceName;
      existingService.serviceType = serviceData.serviceType;
      existingService.businessPhone = serviceData.businessPhone;
      existingService.serviceDescription = serviceData.serviceDescription;
      existingService.address = serviceData.address;

      // Handle image uploads
      if (serviceData.images?.length) {
        const newImages = serviceData.images.filter((img: any) => img.base64);
        if (newImages.length) {
          const uploadedUrls = await this.uploadService.uploadMultipleImages(newImages);
          const oldImages = serviceData.images
            .filter((img: any) => !img.base64)
            .map((img: any) => img.uri);
          existingService.images = [...oldImages, ...uploadedUrls];
        }
      }

      const savedService = await this.serviceRepository.save(existingService);

      return {
        status: 'success',
        message: 'Service updated successfully',
        service: savedService
      };
    } catch (error) {
      console.error('Error in updateService:', error);
      throw new BadRequestException(error.message || 'Failed to update service');
    }
  }

  async getServiceStatus(serviceId: number) {
    try {
      // Validate serviceId
      if (!serviceId || isNaN(serviceId)) {
        throw new BadRequestException('Invalid service ID');
      }

      const service = await this.serviceRepository.findOne({
        where: { id: serviceId },
        select: ['verificationStatus'] // Only select what we need
      });

      if (!service) {
        throw new NotFoundException('Service not found');
      }

      return {
        success: true,
        status: service.verificationStatus || 'pending'
      };
    } catch (error) {
      console.error('Error getting service status:', error);
      throw error; // Let the controller handle the error
    }
  }

  async updateServiceStatus(serviceId: number, status: 'pending' | 'success' | 'failed') {
    const service = await this.serviceRepository.findOne({
      where: { id: serviceId }
    });

    if (!service) {
      throw new NotFoundException('Service not found');
    }

    service.verificationStatus = status;
    await this.serviceRepository.save(service);

    return {
      status: 'success',
      message: 'Service status updated'
    };
  }

  async saveServiceSetup(serviceId: number, data: any) {
    console.log('Received setup request:', { serviceId, data });

    if (!serviceId || !data?.setupData) {
      throw new BadRequestException('Invalid request: missing serviceId or setupData');
    }

    const service = await this.serviceRepository.findOne({
      where: { id: serviceId },
      relations: ['setup'],
      select: ['id', 'verificationStatus', 'termsAccepted']
    });

    if (!service) {
      throw new NotFoundException(`Service not found with ID: ${serviceId}`);
    }

    if (service.verificationStatus !== 'success') {
      throw new BadRequestException('Service must be verified before saving setup');
    }

    try {
      const setupData = data.setupData;

      // Log the flags that determine which data to use
      console.log('Service setup configuration flags:', {
        hasSubUnits: setupData.hasSubUnits === true ? 'Using sub-units' : 'Using regular service',
        useDayWiseTimeSlots: setupData.useDayWiseTimeSlots === true ? 'Using day-wise time slots' : 'Using regular time slots'
      });

      // Validate setup data - this will also clean the data by removing unused fields
      if (!this.validateSetupData(setupData)) {
        throw new BadRequestException('Invalid setup data structure');
      }

      // Log the cleaned setup data
      console.log('Cleaned setup data after validation:', {
        hasSubUnits: setupData.hasSubUnits,
        useDayWiseTimeSlots: setupData.useDayWiseTimeSlots,
        hasSelectedDays: Array.isArray(setupData.selectedDays),
        hasTimeSlots: Array.isArray(setupData.timeSlots),
        hasTimeSlotsByDay: setupData.timeSlotsByDay ? 'Present' : 'Not present',
        hasSubUnitsArray: Array.isArray(setupData.subUnits),
        subUnitsCount: Array.isArray(setupData.subUnits) ? setupData.subUnits.length : 0,
        subUnitsWithDayWiseTimeSlots: Array.isArray(setupData.subUnits) ?
          setupData.subUnits.map(unit => ({
            name: unit.name,
            useDayWiseTimeSlots: unit.useDayWiseTimeSlots
          })) : []
      });

      // Create or update setup record
      let setup = service.setup;
      if (!setup) {
        setup = this.setupRepository.create({
          service,
          setupData
        });
      } else {
        // Check if hasSubUnits changed and reset leave days if it did
        const previousHasSubUnits = setup.setupData?.hasSubUnits === true;
        const newHasSubUnits = setupData.hasSubUnits === true;

        if (previousHasSubUnits !== newHasSubUnits) {
          console.log(`Service type changed from ${previousHasSubUnits ? 'subunits' : 'regular'} to ${newHasSubUnits ? 'subunits' : 'regular'}, resetting leave days`);

          // Reset leave days to empty based on new service type
          if (newHasSubUnits) {
            // If switching to subunits, initialize empty subunits structure
            setup.leaveDays = {
              main: [],
              subUnits: {}
            };
          } else {
            // If switching to regular service, initialize empty main array
            setup.leaveDays = {
              main: [],
              subUnits: {}
            };
          }
        }

        // Update setup data
        setup.setupData = setupData;
      }

      // After successful save
      setup = await this.setupRepository.save(setup);

      console.log('Setup saved successfully for service:', serviceId);

      return {
        status: 'success',
        message: 'Service setup data saved successfully',
        data: setup.setupData,
        termsAccepted: service.termsAccepted,
        hasSetup: true // Add this to indicate setup is complete
      };
    } catch (error) {
      console.error('Error in saveServiceSetup:', error);
      throw new BadRequestException(error.message || 'Failed to save service setup data');
    }
  }

  private validateSetupData(setupData: any): boolean {
    if (!setupData) return false;

    console.log('Validating setup data with flags:', {
      hasSubUnits: setupData.hasSubUnits,
      useDayWiseTimeSlots: setupData.useDayWiseTimeSlots
    });

    // Check if using sub-units
    if (setupData.hasSubUnits === true) {
      console.log('Processing sub-units configuration');

      // Validate sub-units existence
      if (!Array.isArray(setupData.subUnits) || setupData.subUnits.length === 0) {
        console.log('Invalid subUnits: must be an array with at least one entry');
        return false;
      }

      // Validate each sub-unit
      for (const unit of setupData.subUnits) {
        if (!unit.name || !unit.avgServeTime || !unit.pricePerHead) {
          console.log('Invalid subUnit: missing required fields');
          return false;
        }

        // Check days and availableHours
        if (!Array.isArray(unit.selectedDays) || unit.selectedDays.length === 0) {
          console.log('Invalid subUnit: missing selectedDays');
          return false;
        }

        // Ensure availableHours exists and has entries for each selected day
        if (!unit.availableHours || typeof unit.availableHours !== 'object') {
          console.log('Invalid subUnit: missing availableHours');
          return false;
        }

        // Validate that each selected day has time slots in availableHours
        for (const day of unit.selectedDays) {
          if (!unit.availableHours[day] || !Array.isArray(unit.availableHours[day]) || unit.availableHours[day].length === 0) {
            console.log(`Invalid availableHours: missing or empty time slots for ${day}`);
            return false;
          }
        }

        // Remove legacy fields if they exist
        delete unit.timeSlots;
        delete unit.dayWiseAvailableHours;
        // Ensure useDayWiseTimeSlots is a boolean or set to false if undefined
        unit.useDayWiseTimeSlots = unit.useDayWiseTimeSlots === true;
      }

      // When using sub-units, regular service fields should be ignored
      delete setupData.selectedDays;
      delete setupData.timeSlots;
      delete setupData.servingTime;
      delete setupData.basePrice;
      delete setupData.timeSlotsByDay;
      delete setupData.useDayWiseTimeSlots;
      delete setupData.availableHours;

      return true;
    } else {
      // Regular setup validation (not using sub-units)
      console.log('Processing regular service configuration');

      // When not using sub-units, subUnits array should be ignored
      delete setupData.subUnits;

      // Check required fields
      const requiredFields = ['selectedDays', 'servingTime', 'basePrice'];
    for (const field of requiredFields) {
        if (!setupData[field]) {
          console.log(`Missing required field: ${field}`);
          return false;
        }
    }

      if (!Array.isArray(setupData.selectedDays) || setupData.selectedDays.length === 0) {
        console.log('Invalid selectedDays: must be a non-empty array');
      return false;
    }

      // Check availableHours
      if (!setupData.availableHours || typeof setupData.availableHours !== 'object') {
        console.log('Invalid setup: missing availableHours object');
        return false;
      }

      // Validate that each selected day has time slots in availableHours
      for (const day of setupData.selectedDays) {
        if (!setupData.availableHours[day] || !Array.isArray(setupData.availableHours[day]) || setupData.availableHours[day].length === 0) {
          console.log(`Invalid availableHours: missing or empty time slots for ${day}`);
          return false;
        }
      }

      // Remove legacy fields if they exist, but keep useDayWiseTimeSlots
      delete setupData.timeSlots;
      delete setupData.timeSlotsByDay;
      // Ensure useDayWiseTimeSlots is a boolean or set to false if undefined
      setupData.useDayWiseTimeSlots = setupData.useDayWiseTimeSlots === true;

    return true;
    }
  }

  async getServiceSetup(serviceId: number) {
    try {
      if (!serviceId || isNaN(serviceId)) {
        throw new BadRequestException('Invalid service ID');
      }

      const service = await this.serviceRepository.findOne({
        where: { id: serviceId },
        relations: ['setup']
      });

      if (!service) {
        throw new NotFoundException(`Service not found for ID: ${serviceId}`);
      }

      // If no setup exists, explicitly return hasSetup: false
      if (!service.setup) {
        return {
          status: 'success',
          message: 'No setup data found',
          hasSetup: false,
          data: null
        };
      }

      // If setup exists, return the data with hasSetup: true
      return {
        status: 'success',
        message: 'Service setup data retrieved',
        hasSetup: true,
        data: service.setup.setupData
      };
    } catch (error) {
      console.error('Error getting service setup:', error);
      throw error;
    }
  }

  async getServiceDetailsByEmail(email: string) {
    try {
      const service = await this.serviceRepository.findOne({
        where: { email },
        select: [
          'id',
          'serviceName',
          'termsAccepted',
          'serviceType',
          'businessPhone',
          'serviceDescription',
          'address',
          'images',
          'verificationStatus',
          'documents',
          'isOpen' // Add isOpen to selected fields
        ]
      });

      // Return empty service object rather than throwing error
      if (!service) {
        return {
          id: null,
          serviceName: '',
          termsAccepted: false,
          serviceType: '',
          businessPhone: '',
          serviceDescription: '',
          address: null,
          images: [],
          verificationStatus: 'pending',
          documents: null // Return null documents field in empty response
        };
      }

      // Ensure address has the correct structure
      if (!service.address) {
        service.address = {
          details: {
            buildingNo: '',
            locality: '',
            city: '',
            state: 'Select State',
            pincode: ''
          },
          coordinates: {
            latitude: 0,
            longitude: 0
          }
        };
      }

      return service;
    } catch (error) {
      console.error('Error getting service details:', error);
      throw new BadRequestException('Failed to get service details');
    }
  }

  async checkServiceCompletion(email: string) {
    try {
      const service = await this.serviceRepository.findOne({
        where: { email },
        relations: ['setup'],
        select: [
          'id',
          'serviceName',
          'serviceType',
          'businessPhone',
          'serviceDescription',
          'address',
          'images',
          'verificationStatus',
          'termsAccepted',
          'documents' // Add documents to selected fields
        ]
      });

      if (!service) {
        return {
          exists: false,
          isVerified: false,
          isDetailsComplete: false,
          hasSetup: false,
          verificationStatus: null,
          termsAccepted: false,
          serviceId: null,
          setupComplete: false
        };
      }

      const isDetailsComplete = Boolean(
        service.serviceName &&
        service.serviceType &&
        service.businessPhone &&
        service.serviceDescription &&
        service.images?.length > 0 &&
        service.address
      );

      return {
        exists: true,
        isVerified: service.verificationStatus === 'success',
        isDetailsComplete,
        hasSetup: !!service.setup,
        verificationStatus: service.verificationStatus,
        termsAccepted: service.termsAccepted,
        serviceId: service.id,
        setupComplete: !!service.setup,
        serviceDetails: {
          serviceName: service.serviceName || '',
          serviceType: service.serviceType || '',
          businessPhone: service.businessPhone || '',
          serviceDescription: service.serviceDescription || '',
          address: service.address || null,
          images: service.images || [],
          documents: service.documents || null // Include documents in response
        }
      };
    } catch (error) {
      console.error('Error checking service completion:', error);
      throw new BadRequestException('Failed to check service completion status');
    }
  }

  async acceptTerms(email: string) {
    try {
      const service = await this.serviceRepository.findOne({
        where: { email }
      });

      if (!service) {
        throw new NotFoundException('Service not found');
      }

      service.termsAccepted = true;
      await this.serviceRepository.save(service);

      return {
        status: 'success',
        message: 'Terms accepted successfully'
      };
    } catch (error) {
      console.error('Error accepting terms:', error);
      throw new BadRequestException(error.message || 'Failed to accept terms');
    }
  }

  async getVerificationStatusByEmail(email: string) {
    const service = await this.serviceRepository.findOne({
      where: { email },
      select: ['verificationStatus']
    });

    if (!service) {
      throw new NotFoundException('Service not found');
    }

    return service;
  }

  async toggleServiceStatus(email: string, isOpen: boolean) {
    try {
      const service = await this.serviceRepository.findOne({ where: { email } });

      if (!service) {
        throw new NotFoundException('Service not found');
      }

      service.isOpen = isOpen;
      await this.serviceRepository.save(service);

      return {
        status: 'success',
        message: `Service ${isOpen ? 'opened' : 'closed'} successfully`,
        isOpen
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async getServiceOpenStatus(email: string) {
    const service = await this.serviceRepository.findOne({
      where: { email },
      select: ['isOpen']
    });

    if (!service) {
      throw new NotFoundException('Service not found');
    }

    return service;
  }

  async saveBankDetails(serviceId: number, data: any) {
    try {
      let service = await this.serviceRepository.findOne({
        where: { id: serviceId },
        relations: ['bankDetails']
      });

      if (!service) {
        throw new NotFoundException('Service not found');
      }

      if (!service.bankDetails) {
        const bankDetails = new BankDetails();
        Object.assign(bankDetails, {
          service,
          ...data
        });
        service.bankDetails = bankDetails;
      } else {
        Object.assign(service.bankDetails, data);
      }

      await this.bankDetailsRepository.save(service.bankDetails);

      return {
        status: 'success',
        message: 'Bank details saved successfully'
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async getBankDetails(serviceId: number) {
    const service = await this.serviceRepository.findOne({
      where: { id: serviceId },
      relations: ['bankDetails']
    });

    if (!service) {
      throw new NotFoundException('Service not found');
    }

    return {
      status: 'success',
      data: service.bankDetails
    };
  }

  async saveLeaveDays(serviceId: number, leaveDays: any) {
    try {
      const service = await this.serviceRepository.findOne({
        where: { id: serviceId },
        relations: ['setup']
      });

      if (!service || !service.setup) {
        throw new NotFoundException('Service or setup not found');
      }

      // Determine if service uses subUnits
      const hasSubUnits = service.setup.setupData?.hasSubUnits === true;

      // Initialize a proper leaveDays structure based on service type
      let formattedLeaveDays: { main: string[]; subUnits: { [key: string]: string[] } } = {
        main: [],
        subUnits: {}
      };

      // Handle the provided leave days based on service type
      if (hasSubUnits) {
        // For services with subUnits, only update the subUnits part
        if (typeof leaveDays === 'object' && leaveDays !== null) {
          if (leaveDays.subUnits && typeof leaveDays.subUnits === 'object') {
            // Validate each subunit's leave days
            Object.keys(leaveDays.subUnits).forEach(key => {
              if (!Array.isArray(leaveDays.subUnits[key])) {
                leaveDays.subUnits[key] = [];
              }
            });

            // Update only the subUnits part
            formattedLeaveDays.subUnits = leaveDays.subUnits;

            // Ignore any main data that might have been sent
            formattedLeaveDays.main = [];
          }
        }
      } else {
        // For services without subUnits, only update the main part
        if (typeof leaveDays === 'object' && leaveDays !== null) {
          // If sent as main array
          if (Array.isArray(leaveDays.main)) {
            formattedLeaveDays.main = leaveDays.main as string[];
          }
          // If sent as direct array
          else if (Array.isArray(leaveDays)) {
            formattedLeaveDays.main = leaveDays as string[];
          }

          // For regular services, ensure subUnits is empty
          formattedLeaveDays.subUnits = {};
        }
      }

      // Save the properly structured leaveDays
      service.setup.leaveDays = formattedLeaveDays;
      await this.setupRepository.save(service.setup);

      return {
        status: 'success',
        message: 'Leave days saved successfully'
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async getLeaveDays(serviceId: number) {
    try {
    const service = await this.serviceRepository.findOne({
      where: { id: serviceId },
      relations: ['setup']
    });

    if (!service || !service.setup) {
      throw new NotFoundException('Service or setup not found');
    }

      // Determine if service uses subUnits
      const hasSubUnits = service.setup.setupData?.hasSubUnits === true;

      // Ensure we have a proper leaveDays structure
      let leaveDays = service.setup.leaveDays;

      // If leaveDays is missing or malformed, initialize it properly
      if (!leaveDays || typeof leaveDays !== 'object') {
        leaveDays = { main: [], subUnits: {} };
      }

      // Ensure main is an array
      if (!Array.isArray(leaveDays.main)) {
        leaveDays.main = [];
      }

      // Ensure subUnits is an object
      if (!leaveDays.subUnits || typeof leaveDays.subUnits !== 'object') {
        leaveDays.subUnits = {};
      }

      if (hasSubUnits) {
        // For services with subUnits, return only subUnits data
    return {
      status: 'success',
          data: { subUnits: leaveDays.subUnits },
          hasSubUnits: true
        };
      } else {
        // For services without subUnits, return only main data
        return {
          status: 'success',
          data: leaveDays.main,
          hasSubUnits: false
    };
      }
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async getAllServices() {
    try {
      const services = await this.serviceRepository.find({
        where: { verificationStatus: 'success' },
        relations: ['setup', 'queues'],
        select: [
          'id',
          'serviceName',
          'serviceType',
          'address',
          'images',
          'isOpen',
          'rating',
          'reviewCount',
          'serviceDescription'
        ]
      });

      console.log('Services coordinates check:', services.map(s => ({
        id: s.id,
        coordinates: s.address?.coordinates
      })));

      const servicesWithQueueInfo = await Promise.all(services.map(async service => {
        // Validate coordinates
        if (!service.address?.coordinates?.latitude || !service.address?.coordinates?.longitude) {
          console.warn(`Missing coordinates for service ${service.id}`);
        }
        // Count active queues - include both 'waiting' and 'serving' status
        const activeQueues = service.queues.filter(q => q.status === 'waiting' || q.status === 'serving');
        const queueCount = activeQueues.length;

        // Count VIP and normal queues
        const vipCount = activeQueues.filter(q => q.isVIP).length;
        const normalCount = queueCount - vipCount;

        // Get servingTime from setup
        const servingTime = parseInt(service.setup?.setupData?.servingTime || '30');

        // Calculate total wait time based on queue position and serving time
        const waitingTime = queueCount * servingTime;

        // Get base price from setup
        const basePrice = parseInt(service.setup?.setupData?.basePrice || '50');

        // Ensure the first image is a valid URL
        const firstImage = Array.isArray(service.images) && service.images.length > 0
          ? service.images[0]
          : null;

        return {
          _id: service.id.toString(),
          serviceName: service.serviceName,
          serviceType: service.serviceType,
          address: service.address,
          image: firstImage,
          isOpen: service.isOpen,
          rating: service.rating || 0,
          reviewCount: service.reviewCount || 0,
          serviceDescription: service.serviceDescription || '',
          queueInfo: {
            waitingTime,
            membersInQueue: queueCount,
            vipCount,
            normalCount,
            cost: basePrice,
            servingTime // Include servingTime in response
          }
        };
      }));

      return servicesWithQueueInfo;
    } catch (error) {
      console.error('Error fetching services:', error);
      throw new BadRequestException('Failed to fetch services');
    }
  }

  async getServiceById(id: number) {
    try {
      const service = await this.serviceRepository.findOne({
        where: { id },
        relations: ['setup', 'queues'],
        select: [
          'id',
          'serviceName',
          'serviceType',
          'address',
          'images',
          'isOpen',
          'rating',
          'reviewCount',
          'reviews',
          'serviceDescription',
          'businessPhone',
          'email'
        ]
      });

      if (!service) {
        throw new NotFoundException('Service not found');
      }

      // Count active queues - include both 'waiting' and 'serving' status
      const activeQueues = service.queues.filter(q => q.status === 'waiting' || q.status === 'serving');
      const queueCount = activeQueues.length;

      // Count VIP and normal queues separately
      const vipCount = activeQueues.filter(q => q.isVIP).length;
      const normalCount = queueCount - vipCount;

      // Get servingTime and basePrice from setup
      const servingTime = parseInt(service.setup?.setupData?.servingTime || '30');
      const basePrice = parseInt(service.setup?.setupData?.basePrice || '50');

      // Get working hours and leave days
      const setupData = service.setup?.setupData || {};
      const timeSlots = setupData.timeSlots || [];
      const selectedDays = setupData.selectedDays || [];

      // Calculate working hours from time slots
      const workingHours = timeSlots.length > 0 ? {
        startTime: timeSlots[0].start,
        endTime: timeSlots[timeSlots.length - 1].end
      } : null;

      // Calculate total wait time
      const waitingTime = queueCount * servingTime;

      // Ensure images array is properly formatted - this is crucial
      const formattedImages = Array.isArray(service.images) ? service.images.filter(img => typeof img === 'string' && img.trim() !== '') : [];

      return {
        _id: service.id.toString(),
        serviceName: service.serviceName,
        serviceType: service.serviceType,
        address: service.address,
        images: formattedImages,
        isOpen: service.isOpen,
        rating: service.rating || 0,
        reviewCount: service.reviewCount || 0,
        reviews: service.reviews || [],
        serviceDescription: service.serviceDescription || '',
        businessPhone: service.businessPhone || '',
        email: service.email || '',
        workingHours,
        selectedDays,
        queueInfo: {
          waitingTime,
          membersInQueue: queueCount,
          vipCount,
          normalCount,
          cost: basePrice,
          servingTime
        }
      };
    } catch (error) {
      console.error('Error fetching service details:', error);
      throw error;
    }
  }

  async getServiceReviews(serviceId: number, limit: number = 10, offset: number = 0) {
    try {
      const service = await this.serviceRepository.findOne({
        where: { id: serviceId },
      });

      if (!service) {
        throw new NotFoundException(`Service with ID ${serviceId} not found`);
      }

      // Get total count
      const totalCount = await this.reviewRepository.count({
        where: { serviceId },
      });

      // Get reviews with pagination
      const reviews = await this.reviewRepository.find({
        where: { serviceId },
        order: { createdAt: 'DESC' },
        take: limit,
        skip: offset,
      });

      // Calculate star distribution
      const starDistribution = await this.reviewRepository
        .createQueryBuilder('review')
        .select('review.rating', 'rating')
        .addSelect('COUNT(*)', 'count')
        .where('review.serviceId = :serviceId', { serviceId })
        .groupBy('review.rating')
        .getRawMany();

      // Format star distribution
      const formattedStarDistribution = {
        1: 0,
        2: 0,
        3: 0,
        4: 0,
        5: 0,
      };

      starDistribution.forEach((item) => {
        formattedStarDistribution[item.rating] = parseInt(item.count);
      });

      return {
        reviews,
        totalCount,
        starDistribution: formattedStarDistribution,
      };
    } catch (error) {
      console.error('Error fetching service reviews:', error);
      throw error;
    }
  }

  async addReview(serviceId: number, reviewData: {
    userId: string;
    userName: string;
    userProfilePic?: string;
    rating: number;
    comment: string;
  }) {
    try {
      const service = await this.serviceRepository.findOne({
        where: { id: serviceId },
      });

      if (!service) {
        throw new NotFoundException(`Service with ID ${serviceId} not found`);
      }

      // Validate rating
      if (reviewData.rating < 1 || reviewData.rating > 5) {
        throw new BadRequestException('Rating must be between 1 and 5');
      }

      // Create new review
      const newReview = this.reviewRepository.create({
        serviceId,
        userId: reviewData.userId,
        userName: reviewData.userName,
        userProfilePic: reviewData.userProfilePic,
        rating: reviewData.rating,
        comment: reviewData.comment,
      });

      await this.reviewRepository.save(newReview);

      // Update service rating and review count
      const allReviews = await this.reviewRepository.find({
        where: { serviceId },
      });

      const totalRating = allReviews.reduce((sum, review) => sum + review.rating, 0);
      const averageRating = totalRating / allReviews.length;

      service.rating = parseFloat(averageRating.toFixed(1));
      service.reviewCount = allReviews.length;

      await this.serviceRepository.save(service);

      return newReview;
    } catch (error) {
      console.error('Error adding review:', error);
      throw error;
    }
  }

  async getServiceTimeSlots(serviceId: number, dateString: string, subUnitId?: number) {
    try {
      // Find service with setup and queues
      const service = await this.serviceRepository.findOne({
        where: { id: serviceId },
        relations: ['setup', 'queues']
      });

      if (!service || !service.setup) {
        throw new NotFoundException('Service or setup not found');
      }

      // Parse the requested date
      const requestedDate = new Date(dateString);
      const dayOfWeek = requestedDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const dayName = dayNames[dayOfWeek];
      const dayNameCapitalized = dayName.charAt(0).toUpperCase() + dayName.slice(1);

      // Get the setupData
      const setupData = service.setup.setupData;

      console.log('Getting time slots with flags:', {
        hasSubUnits: setupData.hasSubUnits,
        date: dateString,
        dayName,
        subUnitId
      });

      // Check if using sub-units
      if (setupData.hasSubUnits === true && Array.isArray(setupData.subUnits) && setupData.subUnits.length > 0) {
        console.log('Using sub-units configuration for time slots');

        // Use the provided subUnitId if available, otherwise default to the first sub-unit
        let subUnitIndex = 0; // Default to first sub-unit

        // If subUnitId is provided and valid, use it
        if (subUnitId !== undefined && subUnitId >= 0 && subUnitId < setupData.subUnits.length) {
          subUnitIndex = subUnitId;
          console.log(`Using provided subUnitId: ${subUnitId}`);
        } else {
          console.log(`Using default subUnitId: 0 (first subunit)`);
        }

        const subUnit = setupData.subUnits[subUnitIndex];

        // Check if the sub-unit works on this day
        const subUnitSelectedDays = subUnit.selectedDays?.map(day => day.toLowerCase()) || [];
        if (!subUnitSelectedDays.includes(dayName)) {
          return {
            status: 'error',
            message: 'The requested date is not a working day for this service',
            timeSlots: []
          };
        }

        // Check if the day is a leave day
        const leaveDays = service.setup.leaveDays || { main: [], subUnits: {} };

        // For services with subUnits, only check leave days for the specific subunit
        const isSubunitLeaveDay = setupData.hasSubUnits &&
          leaveDays.subUnits &&
          leaveDays.subUnits[`subunit-${subUnitIndex}`] ?
          leaveDays.subUnits[`subunit-${subUnitIndex}`].some(leaveDay => {
            const leaveDateObj = new Date(leaveDay);
            return leaveDateObj.toISOString().split('T')[0] === dateString;
          }) :
          false;

        if (isSubunitLeaveDay) {
          return {
            status: 'error',
            message: 'The requested date is a leave day',
            timeSlots: []
          };
        }

        // Get time slots from availableHours
        const subUnitDaySlots = subUnit.availableHours && subUnit.availableHours[dayNameCapitalized]
          ? subUnit.availableHours[dayNameCapitalized]
          : [];

        if (subUnitDaySlots.length === 0) {
          return {
            status: 'error',
            message: 'No available time slots for this day',
            timeSlots: []
          };
        }

        // Map time slots with queue information
        const timeSlotsWithQueue = this.mapTimeSlotsWithQueues(subUnitDaySlots, service.queues, dateString, subUnitIndex);

        return {
          status: 'success',
          workingDay: true,
          timeSlots: timeSlotsWithQueue
        };

      } else {
        console.log('Using regular service configuration for time slots');
        // Regular service (no sub-units)

      // Check if the day is a working day - case insensitive comparison
        const selectedDays = setupData.selectedDays || [];
      const normalizedSelectedDays = selectedDays.map(day => day.toLowerCase());

      if (!normalizedSelectedDays.includes(dayName)) {
        return {
          status: 'error',
          message: 'The requested date is not a working day',
          timeSlots: []
        };
      }

      // Check if the day is a leave day
        const leaveDays = service.setup.leaveDays || { main: [], subUnits: {} };

        const isLeaveDay = leaveDays.main.some(leaveDay => {
        const leaveDateObj = new Date(leaveDay);
        return leaveDateObj.toISOString().split('T')[0] === dateString;
      });

      if (isLeaveDay) {
        return {
          status: 'error',
          message: 'The requested date is a leave day',
          timeSlots: []
        };
      }

        // Get time slots from availableHours
        const daySlots = setupData.availableHours && setupData.availableHours[dayNameCapitalized]
          ? setupData.availableHours[dayNameCapitalized]
          : [];

        if (daySlots.length === 0) {
          // Try to use legacy fields if available
          let legacyTimeSlots: Array<{ start: string; end: string; }> = [];

          if (setupData.timeSlotsByDay && setupData.timeSlotsByDay[dayNameCapitalized]) {
            legacyTimeSlots = setupData.timeSlotsByDay[dayNameCapitalized];
          } else if (Array.isArray(setupData.timeSlots)) {
            legacyTimeSlots = setupData.timeSlots;
          }

          if (legacyTimeSlots.length > 0) {
            // Map time slots with queue information
            const timeSlotsWithQueue = this.mapTimeSlotsWithQueues(legacyTimeSlots, service.queues, dateString);

            return {
              status: 'success',
              workingDay: true,
              timeSlots: timeSlotsWithQueue,
              isLegacy: true
            };
          }

          return {
            status: 'error',
            message: 'No available time slots for this day',
            timeSlots: []
          };
        }

        // Map time slots with queue information
        const timeSlotsWithQueue = this.mapTimeSlotsWithQueues(daySlots, service.queues, dateString);

        return {
          status: 'success',
          workingDay: true,
          timeSlots: timeSlotsWithQueue
        };
      }
    } catch (error) {
      console.error('Error in getServiceTimeSlots:', error);
      throw new BadRequestException(error.message || 'Failed to get time slots');
    }
  }

  // Helper method to map time slots with queue information
  private mapTimeSlotsWithQueues(timeSlots: any[], queues: Queue[], dateString: string, subUnitId?: number) {
      // Get queues for the requested date
    const queuesForDate = queues.filter(queue => {
        const queueDate = new Date(queue.date);

        // Basic date filter
        const dateMatches = queueDate.toISOString().split('T')[0] === dateString;

        // If subUnitId is provided, also filter by subUnitId
        if (subUnitId !== undefined && 'subUnitId' in queue) {
          return dateMatches && (queue as any).subUnitId === subUnitId;
        }

        return dateMatches;
      });

      // Map time slots with queue information
    return timeSlots.map(slot => {
        // Format the time slot as "HH:MM - HH:MM"
        const timeSlotString = `${slot.start} - ${slot.end}`;

        const normalQueue = queuesForDate.filter(q =>
          q.timeSlot === timeSlotString && !q.isVIP
        );

        const vipQueue = queuesForDate.filter(q =>
          q.timeSlot === timeSlotString && q.isVIP
        );

        return {
          timeSlot: timeSlotString,
          normalQueueCount: normalQueue.length,
          vipQueueCount: vipQueue.length
        };
      });
  }

  async updateQueueStatus(queueId: number, status: string, forceUpdate: boolean = false): Promise<Queue> {
    // Validate status
    const validStatuses = ['pending', 'active', 'completed', 'cancelled', 'no-show', 'serving', 'waiting'];
    if (!validStatuses.includes(status)) {
      throw new BadRequestException(`Invalid status: ${status}. Must be one of: ${validStatuses.join(', ')}`);
    }

    // Get queue from database with full user details and service information
    const queue = await this.queueRepository.findOne({
      where: { id: queueId },
      relations: ['service']
    });

    if (!queue) {
      throw new NotFoundException(`Queue with ID ${queueId} not found`);
    }

    // Store original status for logging
    const originalStatus = queue.status;

    // Define final statuses
    const finalStatuses = ['no-show', 'completed', 'cancelled'];
    const nonFinalStatuses = ['waiting', 'serving', 'active', 'pending'];

    // Prevent status change from final status to non-final status unless forceUpdate is true
    if (finalStatuses.includes(originalStatus) &&
        nonFinalStatuses.includes(status) &&
        !forceUpdate) {
      console.log(`Prevented status change from ${originalStatus} to ${status} for queue ${queueId} - status is final`);
      throw new BadRequestException(`Cannot change status from ${originalStatus} to ${status} - status is final. Use forceUpdate=true to override.`);
    }

    // Get the current isCheckedIn status before updating
    const isCheckedIn = queue.isCheckedIn;
    console.log(`Current DB isCheckedIn status for queue ${queueId}: ${isCheckedIn}`);

    // Update status in database while preserving isCheckedIn
    queue.status = status;

    // Explicitly retain the isCheckedIn value
    queue.isCheckedIn = isCheckedIn;

    // Store the status update time
    const statusUpdateTime = new Date();

    // If status is changing to 'serving', set servingStartedAt
    if (status === 'serving') {
      console.log(`Setting servingStartedAt for queue ${queueId} to ${statusUpdateTime.toISOString()}`);
      queue.servingStartedAt = statusUpdateTime;
      queue.currentlyServing = true;
    }

    // Save the queue with updated fields
    const updatedQueue = await this.queueRepository.save(queue);
    console.log(`Updated queue ${queueId} in database: status=${status}, servingStartedAt=${queue.servingStartedAt ? queue.servingStartedAt.toISOString() : 'null'}`);

    // For final statuses, recalculate estimated serve times for all queues in this service
    if (status === 'completed' || status === 'no-show' || status === 'cancelled' || status === 'serving') {
      try {
        console.log(`Recalculating estimated serve times for service ${queue.serviceId} after queue ${queueId} status changed to ${status}`);
        this.queueFlowService.recalculateEstimatedServeTimes(
          queue.serviceId,
          queue.date,
          queue.timeSlot
        ).catch(error => {
          console.error(`Error recalculating estimated serve times: ${error.message}`);
        });
      } catch (error) {
        console.error(`Error triggering recalculation of estimated serve times: ${error.message}`);
      }
    }

    // Get additional user data for Redis
    let userName = '';
    let userPhone = '';

    try {
      // Try to extract user details from the queue object or related data
      userName = queue.userName || '';
      if ('fullName' in queue) {
        userName = (queue as any).fullName || userName;
      }

      if ('mobileNumber' in queue) {
        userPhone = (queue as any).mobileNumber || '';
      }
    } catch (error) {
      console.error('Error extracting user details:', error);
    }

    // Try to get more user data if available
    if (queue.userId && (!userName || !userPhone)) {
      try {
        // Make a direct DB query to get user details if we have userId
        // This ensures we have complete user data for Redis
        const user = await this.fetchUserDetails(queue.userId);
        if (user) {
          userName = user.fullName || userName;
          userPhone = user.mobileNumber || userPhone;
        }
      } catch (error) {
        console.error(`Error fetching additional user details for queue ${queueId}:`, error);
      }
    }

    // Get service setup to determine serving time if status is 'serving'
    let servingTime = 15; // Default 15 minutes
    if (status === 'serving') {
      try {
        const serviceSetup = await this.setupRepository.findOne({
          where: { service: { id: queue.serviceId } }
        });

        if (serviceSetup?.setupData?.servingTime) {
          servingTime = parseInt(serviceSetup.setupData.servingTime, 10);
          console.log(`Using service setup serving time: ${servingTime} minutes for queue ${queueId}`);
        }
      } catch (error) {
        console.error(`Error getting service setup for queue ${queueId}:`, error);
      }
    }

    // Build a rich queue data object for Redis
    const queueData: Record<string, any> = {
      id: queue.id,
      serviceId: queue.serviceId,
      serviceName: queue.service?.serviceName || 'Unknown Service',
      serviceType: queue.service?.serviceType || 'Unknown Type',
      date: queue.date,
      timeSlot: queue.timeSlot,
      status,
      isVIP: queue.isVIP,
      userId: queue.userId,
      createdAt: queue.createdAt,
      updatedAt: statusUpdateTime.toISOString(),
      uniqueSlotId: queue.uniqueSlotId || Math.floor(1000 + Math.random() * 9000).toString(),
      isCheckedIn,
      fullName: userName || 'Anonymous',
      mobileNumber: userPhone || null,
      statusUpdatedAt: statusUpdateTime.toISOString(),
      finalStatusSet: (status === 'no-show' || status === 'completed' || status === 'cancelled')
    };

    // Add serving-specific data if status is 'serving'
    if (status === 'serving' && queue.servingStartedAt) {
      // Calculate estimated end time
      const estimatedEndTime = new Date(queue.servingStartedAt);
      estimatedEndTime.setMinutes(estimatedEndTime.getMinutes() + servingTime);

      console.log(`Serving details for queue ${queueId}:`);
      console.log(`- Started at: ${queue.servingStartedAt.toISOString()}`);
      console.log(`- Estimated end time: ${estimatedEndTime.toISOString()}`);
      console.log(`- Serving time: ${servingTime} minutes`);

      // Add all serving-related fields
      queueData.servingStartedAt = queue.servingStartedAt.toISOString();
      queueData.estimatedEndTime = estimatedEndTime.toISOString();
      queueData.servingTime = servingTime;
      queueData.servingTimeMinutes = servingTime;
      queueData.remainingMinutes = servingTime;
      queueData.remainingSeconds = servingTime * 60;
      queueData.currentlyServing = true;
    }

    // Update status in Redis with enhanced data and longer TTL
    try {
      console.log(`Updating queue ${queueId} status to ${status} in Redis, isCheckedIn: ${isCheckedIn}`);

      // First, get the current Redis data to check isCheckedIn status there
      const redisQueue = await this.redisService.getQueue(queueId.toString());
      let redisIsCheckedIn = isCheckedIn; // Default to DB value

      if (redisQueue) {
        // If Redis has a value for isCheckedIn, use that instead
        const redisQueueObj = redisQueue as Record<string, any>;
        if (redisQueueObj.isCheckedIn === true) {
          redisIsCheckedIn = true;
          console.log(`Redis has isCheckedIn=true for queue ${queueId}, using that value`);
        }

        // Preserve any additional data from Redis that we want to keep
        if (redisQueueObj.address) queueData.address = redisQueueObj.address;
        if (redisQueueObj.contactPhone) queueData.contactPhone = redisQueueObj.contactPhone;
        if (redisQueueObj.images) queueData.images = redisQueueObj.images;
        if (redisQueueObj.position) queueData.position = redisQueueObj.position;
      }

      // For 'no-show', 'completed', or 'cancelled' statuses, we need to be extra careful to ensure Redis consistency
      if (status === 'no-show' || status === 'completed' || status === 'cancelled') {
        console.log(`Special handling for final status ${status} for queue ${queueId}`);

        // First update the status-specific key directly
        const statusKey = `queue:${queueId}:status:${status}`;
        await this.redisService.set(statusKey, queueData, { ex: 86400 * 7 }); // 7 days TTL

        // Then update the main key with the same data
        await this.redisService.set(`queue:${queueId}`, queueData, { ex: 86400 }); // 24 hours TTL

        // Double-check that the main key has the correct status
        const mainKeyData = await this.redisService.get(`queue:${queueId}`);
        if (mainKeyData) {
          const mainKeyObj = mainKeyData as Record<string, any>;
          console.log(`Main key queue:${queueId} now has status: ${mainKeyObj.status}`);

          // If status is still not updated, force update it one more time
          if (mainKeyObj.status !== status) {
            console.log(`Status mismatch detected! Forcing update of main key queue:${queueId}`);
            // Create a fresh object with the status explicitly set
            const forcedData = {
              ...queueData,
              status: status, // Explicitly set status again
              statusLastUpdatedAt: new Date().toISOString(),
              forcedUpdate: true
            };
            await this.redisService.set(`queue:${queueId}`, forcedData, { ex: 86400 });
          }
        }

        // Finally use the standard update method for additional processing
        await this.redisService.updateQueueStatus(queueId.toString(), status, true);
      } else if (status === 'serving') {
        // For serving status, set a longer TTL to ensure visibility
        console.log(`Setting special handling for serving status on queue ${queueId}`);

        // Update the main Redis key with serving status and longer TTL
        await this.redisService.set(`queue:${queueId}`, queueData, { ex: 86400 }); // 24 hours TTL for serving

        // Also explicitly update the status with our enhanced queue data
        await this.redisService.updateQueueStatus(queueId.toString(), status, true);

        // Verify Redis update was successful
        const verifiedData = await this.redisService.getQueue(queueId.toString());
        if (verifiedData) {
          console.log(`Verified serving status for queue ${queueId} in Redis:`);
          console.log(`- Status: ${verifiedData.status}`);
          console.log(`- servingStartedAt: ${verifiedData.servingStartedAt || 'not set'}`);
          console.log(`- servingTimeMinutes: ${verifiedData.servingTimeMinutes || 'not set'}`);
        }
      } else {
        // For other statuses, use the standard flow
        // Save the enhanced queue data
        await this.redisService.saveQueue(queueId.toString(), queueData);

        // Also explicitly update the status with our enhanced queue data
        await this.redisService.updateQueueStatus(queueId.toString(), status, true);
      }

      // Also explicitly update isCheckedIn to be sure
      await this.redisService.updateQueueCheckInStatus(queueId.toString(), redisIsCheckedIn, true);

      // Invalidate any service queue caches to ensure fresh data on next request
      if (queue.service) {
        const date = new Date(queue.date).toISOString().split('T')[0];
        await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), date);

        // Also invalidate the 'all' date key which is used by mobile app endpoints
        await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');

        // For final statuses (no-show, completed, cancelled), explicitly delete the active-queues keys
        if (status === 'no-show' || status === 'completed' || status === 'cancelled') {
          console.log(`Explicitly invalidating active-queues keys for queue ${queueId} with final status ${status}`);

          // Delete the active-queues keys for both specific date and 'all'
          const activeQueuesKey = `service:${queue.serviceId}:active-queues:${date}`;
          const allActiveQueuesKey = `service:${queue.serviceId}:active-queues:all`;
          await this.redisService.del(activeQueuesKey);
          await this.redisService.del(allActiveQueuesKey);

          console.log(`Deleted active-queues keys: ${activeQueuesKey} and ${allActiveQueuesKey}`);
        }

        // Also clear any cached queue position data
        if (queue.timeSlot) {
          await this.redisService.invalidateQueuePosition(queue.serviceId.toString(), date, queue.timeSlot);
        }
      }

      console.log(`Successfully updated Redis for queue ${queueId} with status ${status}`);
    } catch (error) {
      console.error(`Error updating Redis for queue ${queueId}:`, error);
      // Continue even if Redis update fails - the database is the source of truth
    }

    return updatedQueue;
  }

  // Helper method to fetch user details by userId/clerkId
  private async fetchUserDetails(userId: string): Promise<any> {
    try {
      // This is a simplified example - implement the actual database query
      // to fetch user details based on your schema
      const queryBuilder = this.queueRepository.createQueryBuilder('queue')
        .select(['queue.fullName', 'queue.mobileNumber'])
        .where('queue.userId = :userId', { userId })
        .andWhere('queue.fullName IS NOT NULL')
        .orderBy('queue.createdAt', 'DESC')
        .limit(1);

      const result = await queryBuilder.getOne();
      return result;
    } catch (error) {
      console.error(`Error fetching user details for userId ${userId}:`, error);
      return null;
    }
  }

  async startGracePeriod(queueId: number): Promise<any> {
    // Find the queue first to get the serviceId
    const queue = await this.queueRepository.findOne({ where: { id: queueId } });

    if (!queue) {
      throw new NotFoundException(`Queue with ID ${queueId} not found`);
    }

    // Start the grace period using the queue flow service
    const updatedQueue = await this.queueFlowService.startGracePeriod(queueId, queue.serviceId);

    // Return with additional info
    return {
      ...updatedQueue,
      message: 'Grace period started successfully'
    };
  }

  async getGracePeriodStatus(queueId: number): Promise<any> {
    // Get the queue data from Redis which has grace period info
    const queueData = await this.redisService.getQueue(queueId.toString());

    if (!queueData) {
      // Fall back to database if not in Redis
      const queue = await this.queueRepository.findOne({ where: { id: queueId } });

      if (!queue) {
        throw new NotFoundException(`Queue with ID ${queueId} not found`);
      }

      // Get service setup for default grace time
      const serviceSetup = await this.setupRepository.findOne({
        where: { service: { id: queue.serviceId } }
      });

      // Default to 2 minutes if not specified
      const graceTime = serviceSetup?.graceTime || 120;

      // Calculate remaining time if in grace period
      let remainingSeconds = 0;
      let isExpired = true;

      if (queue.inGracePeriod && queue.graceStartedAt) {
        const now = new Date();
        const graceEndTime = new Date(queue.graceStartedAt);
        graceEndTime.setSeconds(graceEndTime.getSeconds() + graceTime);

        if (now < graceEndTime) {
          isExpired = false;
          remainingSeconds = Math.floor((graceEndTime.getTime() - now.getTime()) / 1000);
        }
      }

      return {
        queueId: queue.id,
        inGracePeriod: queue.inGracePeriod,
        confirmedPresence: queue.confirmedPresence,
        graceStartedAt: queue.graceStartedAt,
        graceTimeSeconds: graceTime,
        remainingSeconds: queue.inGracePeriod ? remainingSeconds : 0,
        isExpired: queue.inGracePeriod ? isExpired : false,
        status: queue.status
      };
    }

    // Process Redis data
    const now = new Date();
    let remainingSeconds = 0;
    let isExpired = true;

    if (queueData.inGracePeriod && queueData.graceStartedAt && queueData.graceEndTime) {
      const graceEndTime = new Date(queueData.graceEndTime);

      if (now < graceEndTime) {
        isExpired = false;
        remainingSeconds = Math.floor((graceEndTime.getTime() - now.getTime()) / 1000);
      }
    }

    return {
      queueId: queueData.id,
      inGracePeriod: queueData.inGracePeriod || false,
      confirmedPresence: queueData.confirmedPresence || false,
      graceStartedAt: queueData.graceStartedAt,
      graceEndTime: queueData.graceEndTime,
      graceTimeSeconds: queueData.graceTimeSeconds || 120,
      remainingSeconds: queueData.inGracePeriod ? remainingSeconds : 0,
      isExpired: queueData.inGracePeriod ? isExpired : false,
      status: queueData.status
    };
  }
}
