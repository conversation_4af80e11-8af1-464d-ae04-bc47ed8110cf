import { Injectable, OnModuleInit } from '@nestjs/common';
import { RedisService } from './redis/redis.service';

@Injectable()
export class MigrationService implements OnModuleInit {
  constructor(private readonly redisService: RedisService) {}

  async onModuleInit() {
    console.log('Running Redis key migration on application startup...');
    
    try {
      // Migrate all queue keys to the new format
      await this.redisService.migrateAllQueueKeys();
      
      // Clean up any old format keys that might still exist
      const oldFormatKeys = await this.redisService.getKeys('queue:*:status:*');
      if (oldFormatKeys && oldFormatKeys.length > 0) {
        console.log(`Found ${oldFormatKeys.length} old format keys after migration. Cleaning up...`);
        
        for (const key of oldFormatKeys) {
          await this.redisService.del(key);
          console.log(`Deleted old format key: ${key}`);
        }
      }
      
      console.log('Redis key migration completed successfully');
    } catch (error) {
      console.error('Error during Redis key migration:', error);
    }
  }
}
