android {
  project.afterEvaluate {
    // Publishing to maven
    if (findProperty('PUBLISH_GRADLE_OUTPUT') == true) {
      // Check if "release" component exists before using it
      if (components.findByName('release') != null) {
        components.release.withSourcesJar()
      }
      
      def javaSourcesJarTask = tasks.create(name: "javaSourcesJar", type: Jar) {
        classifier = 'sources'
      }
    }
  }
} 