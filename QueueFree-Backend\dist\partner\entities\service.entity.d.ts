import { ServiceSetup } from './service-setup.entity';
import { BankDetails } from './bank-details.entity';
import { Queue } from './queue.entity';
import { Review } from './review.entity';
export declare class Service {
    id: number;
    serviceName: string;
    email: string;
    serviceType: string;
    businessPhone: string;
    serviceDescription: string;
    address: {
        details: {
            buildingNo: string;
            locality: string;
            city: string;
            state: string;
            pincode: string;
        };
        coordinates: {
            latitude: number;
            longitude: number;
        };
        area?: string;
        fullAddress?: string;
        googleMapsLink?: string;
    };
    images: string[];
    documents: {
        panNumber: string;
        gstin?: string;
        panCardImage: string;
    };
    verificationStatus: string;
    termsAccepted: boolean;
    isOpen: boolean;
    rating: number;
    reviewCount: number;
    reviews: Review[];
    setup: ServiceSetup;
    bankDetails: BankDetails;
    queues: Queue[];
    createdAt: Date;
    updatedAt: Date;
}
