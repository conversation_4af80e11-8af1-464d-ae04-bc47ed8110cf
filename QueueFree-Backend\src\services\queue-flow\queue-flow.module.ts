import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QueueFlowService } from './queue-flow.service';
import { Queue } from '../../partner/entities/queue.entity';
import { ServiceSetup } from '../../partner/entities/service-setup.entity';
import { RedisModule } from '../redis/redis.module';
import { ScheduleModule } from '@nestjs/schedule';

/**
 * This module provides queue flow management services including wait time estimation.
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([Queue, ServiceSetup]),
    RedisModule,
    ScheduleModule.forRoot(),
  ],
  providers: [QueueFlowService],
  exports: [QueueFlowService],
})
export class QueueFlowModule {}