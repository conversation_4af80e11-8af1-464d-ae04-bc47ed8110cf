import { HttpStatus } from '@nestjs/common';
import { CustomerService } from '../customer/customer.service';
import { PartnerService } from '../partner/partner.service';
export declare class AuthController {
    private readonly customerService;
    private readonly partnerService;
    private registrationInProgress;
    constructor(customerService: CustomerService, partnerService: PartnerService);
    registerUser(userData: {
        email: string;
        fullName?: string;
        clerkId?: string;
    }): Promise<{
        status: string;
        statusCode: HttpStatus;
        message: string;
        data?: undefined;
    } | {
        status: string;
        statusCode: HttpStatus;
        message: string;
        data: {
            status: string;
            message: string;
            user: import("../customer/user.entity").User;
            isExisting: boolean;
        };
    }>;
    registerPartner(partnerData: {
        email: string;
    }): Promise<{
        status: string;
        statusCode: HttpStatus;
        message: string;
        data?: undefined;
    } | {
        status: string;
        statusCode: HttpStatus;
        message: string;
        data: {
            status: string;
            message: string;
            isExisting: boolean;
            serviceId: number;
        };
    }>;
}
