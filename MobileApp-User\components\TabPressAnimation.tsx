import React, { useEffect, useRef } from 'react';
import { View, Animated } from 'react-native';

const TabPressAnimation = () => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 700,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
    ]);

    animation.start();

    return () => {
      animation.stop();
      scaleAnim.setValue(0);
      opacityAnim.setValue(0);
    };
  }, []);

  return (
    <Animated.View
      style={{
        position: 'absolute',
        width: 200,
        height: 200,
        borderRadius: 100,
        backgroundColor: 'black',
        opacity: opacityAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0.05, 0],
        }),
        transform: [
          {
            scale: scaleAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0.5, 1],
            }),
          },
        ],
      }}
    />
  );
};

export default TabPressAnimation;
