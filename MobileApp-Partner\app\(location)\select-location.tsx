import React, { useState, useEffect, useRef } from "react";
import {
  View,
  SafeAreaView,
  TouchableOpacity,
  Image,
  StatusBar,
  Text,
  Dimensions,
  ActivityIndicator,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Keyboard
} from "react-native";
import { router } from "expo-router";
import MapView, { <PERSON><PERSON>, LatLng, PROVIDER_GOOGLE } from "@/lib/MapView";
import * as Location from "expo-location";
import { images } from "@/constants";
import ButtonBlueMain from "@/components/ButtonBlueMain";
import { GooglePlacesAutocomplete, GooglePlacesAutocompleteRef } from "react-native-google-places-autocomplete";
import * as SecureStore from "expo-secure-store";

const SelectLocationScreen = () => {
  const [location, setLocation] = useState<Location.LocationObject | null>(
    null
  );
  const [selectedLocation, setSelectedLocation] = useState<LatLng | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const mapRef = useRef<MapView | null>(null);
  const [locationDetails, setLocationDetails] = useState({
    area: "",
    fullAddress: "",
  });
  const [mapReady, setMapReady] = useState(false);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const searchRef = useRef<GooglePlacesAutocompleteRef>(null);

  useEffect(() => {
    getCurrentLocation();
  }, []);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => setKeyboardVisible(true)
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => setKeyboardVisible(false)
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const getCurrentLocation = async () => {
    try {
      setIsLoading(true);
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced, // Changed from High to Balanced
      });

      const newLocation = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };

      setLocation(location);
      setSelectedLocation(newLocation);

      // Faster animation
      mapRef.current?.animateToRegion(
        {
          ...newLocation,
          latitudeDelta: 0.005,
          longitudeDelta: 0.005,
        },
        500
      ); // Reduced from 1000ms to 500ms
    } catch (error) {
      console.error("Error getting location:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getAddressDetails = async (latitude: number, longitude: number) => {
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=AIzaSyAUIQg4PbIdD4MuC2PWJbo1W7njP902gpI`
      );
      const data = await response.json();

      if (data.results && data.results.length > 0) {
        const result = data.results[0];
        let area = result.address_components.find(
          (component: any) =>
            component.types.includes("sublocality") ||
            component.types.includes("locality")
        )?.long_name;

        // If no area is found, use the second field from formatted_address
        if (!area) {
          const addressParts = result.formatted_address.split(',');
          area = addressParts.length > 1 ? addressParts[1].trim() : addressParts[0].trim();
        }

        setLocationDetails({
          area: area,
          fullAddress: result.formatted_address,
        });
      }
    } catch (error) {
      console.error("Error fetching address:", error);
    }
  };

  const onRegionChangeComplete = (region: any) => {
    setSelectedLocation({
      latitude: region.latitude,
      longitude: region.longitude,
    });
    getAddressDetails(region.latitude, region.longitude);
  };

  const handleConfirmLocation = async () => {
    if (selectedLocation && locationDetails) {
      try {
        // Save selected location data
        await SecureStore.setItemAsync('selectedLocation', JSON.stringify({
          coordinates: {
            latitude: selectedLocation.latitude,
            longitude: selectedLocation.longitude,
          },
          area: locationDetails.area,
          fullAddress: locationDetails.fullAddress
        }));

        router.push({
          pathname: "/(location)/enter-address",
          params: {
            latitude: selectedLocation.latitude,
            longitude: selectedLocation.longitude,
            area: locationDetails.area,
            fullAddress: locationDetails.fullAddress
          }
        });
      } catch (error) {
        console.error("Error saving location:", error);
      }
    }
  };

  return (
    <SafeAreaView className="flex-1">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <View
        className="bg-white border-b-[1px] border-secondary-300/35 pb-6"
        style={{ zIndex: 1 }}
      >
        <View className="px-8 pt-12">
          <View className="w-full flex-row mt-4">
            <TouchableOpacity
              className="flex-row items-center absolute left-0 -top-2 border border-secondary-600 w-12 h-12 rounded-full justify-center"
              onPress={() => router.back()}
            >
              <Image source={images.back} className="w-4 h-4" />
            </TouchableOpacity>
            <View className="flex-1 items-center">
              <Text className="font-poppins-medium text-2xl mb-2">
                Confirm Your Location
              </Text>
            </View>
          </View>
          <View className="w-full items-center mt-12">
            {/* Search container */}
            <View
              style={{
                width: "100%",
                position: "relative",
                zIndex: 2, // Ensure search container is above everything
              }}
            >
              <Image
                source={images.search}
                style={{
                  position: "absolute",
                  left: 15,
                  top: 12,
                  width: 20,
                  height: 20,
                  zIndex: 3,
                  tintColor: "#666",
                }}
                className="mt-1"
              />
              
              {searchText.length > 0 && (
                <TouchableOpacity
                  style={{
                    position: "absolute",
                    right: 15,
                    top: 12,
                    width: 20,
                    height: 20,
                    zIndex: 3,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  onPress={() => {
                    searchRef.current?.setAddressText('');
                    setSearchText('');
                  }}
                >
                  <Image 
                    source={images.close}
                    style={{
                      marginTop: 3,
                      width: 24,
                      height: 24,
                      tintColor: '#666',
                    }}
                  />
                </TouchableOpacity>
              )}

              <GooglePlacesAutocomplete
                ref={searchRef}
                placeholder="Search location"
                enablePoweredByContainer={false}
                minLength={2}
                debounce={300}
                fetchDetails={true}
                onPress={(data, details = null) => {
                  console.log("Selected data:", data);
                  console.log("Selected details:", details);
                  if (details) {
                    const newLocation = {
                      latitude: details.geometry.location.lat,
                      longitude: details.geometry.location.lng,
                    };
                    setSelectedLocation(newLocation);
                    mapRef.current?.animateToRegion({
                      ...newLocation,
                      latitudeDelta: 0.005,
                      longitudeDelta: 0.005,
                    });
                  }
                }}
                onFail={(error) => {
                  console.error("Detailed error:", JSON.stringify(error));
                }}
                query={{
                  key: "AIzaSyAUIQg4PbIdD4MuC2PWJbo1W7njP902gpI", // Replace with your new key
                  language: "en",
                  components: "country:in",
                  types: "geocode",
                }}
                GooglePlacesDetailsQuery={{
                  fields: "geometry,formatted_address",
                }}
                onNotFound={() => console.log("No results found")}
                onTimeout={() => console.log("Request timeout")}
                textInputProps={{
                  errorStyle: { color: "red" },
                  placeholderTextColor: "#666",
                  onFocus: () => console.log("Focus"),
                  onBlur: () => console.log("Blur"),
                  onChangeText: (text) => setSearchText(text),
                }}
                styles={{
                  container: {
                    flex: 0,
                    width: "100%",
                  },
                  textInputContainer: {
                    width: "100%",
                  },
                  textInput: {
                    height: 48,
                    borderRadius: 24,
                    paddingLeft: 45,
                    backgroundColor: "#F5F5F5",
                    fontSize: 16,
                    paddingRight: 45, // Add right padding for the clear button
                  },
                  listView: {
                    position: "absolute",
                    top: 55,
                    left: 0,
                    right: 0,
                    backgroundColor: "white",
                    borderRadius: 8,
                    elevation: 5,
                    zIndex: 2,
                    shadowColor: "#C2C2C2",
                    shadowOffset: { width: 0, height: 3 },
                    shadowOpacity: 0.5,
                    shadowRadius: 1,
                  },
                  row: {
                    padding: 15,
                    height: 50,
                  },
                  separator: {
                    height: 1,
                    backgroundColor: "#f0f0f0",
                  },
                  description: {
                    fontSize: 14,
                  },
                }}
              />
            </View>

            {/* Current location button - Moved outside and below search container */}
            <View
              style={{
                width: "100%",
                paddingHorizontal: 24,
                marginTop: 24,
                position: "relative",
                zIndex: 1, // Ensure it's below the search results
              }}
            >
              <TouchableOpacity
                className="flex-row items-center"
                onPress={getCurrentLocation}
                disabled={isLoading}
                style={{ opacity: isLoading ? 0.5 : 1 }}
              >
                {isLoading ? (
                  <ActivityIndicator
                    size="small"
                    color="#159AFF"
                    style={{ marginRight: 12 }}
                  />
                ) : (
                  <Image
                    source={images.myLocation}
                    className="w-5 h-5 mr-3"
                    tintColor="#159AFF"
                  />
                )}
                <Text className="font-poppins-medium text-primary-500">
                  {isLoading
                    ? "Getting location..."
                    : "Use my current location"}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      {/* Map */}
      
      <View style={{ flex: 1, zIndex: 0 }}>
        {!mapReady && (
          <View style={[StyleSheet.absoluteFill, { backgroundColor: '#f5f5f5', justifyContent: 'center', alignItems: 'center' }]}>
            <ActivityIndicator size="large" color="#159AFF" />
            <Text className="font-poppins-regular mt-2">Loading map...</Text>
          </View>
        )}
        {location && (
          <MapView
            ref={mapRef}
            provider={PROVIDER_GOOGLE}
            style={{ width: "100%", height: "100%" }}
            initialRegion={{
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
              latitudeDelta: 0.005,
              longitudeDelta: 0.005,
            }}
            onMapReady={() => setMapReady(true)}
            onRegionChangeComplete={onRegionChangeComplete}
            loadingEnabled={true}
            loadingIndicatorColor="#159AFF"
            loadingBackgroundColor="#f5f5f5"
            moveOnMarkerPress={false}
            showsUserLocation={true}
            showsMyLocationButton={false}
          />
        )}

        {/* Only show the marker when map is ready */}
        {mapReady && (
          <View
            style={{
              position: "absolute",
              top: "50%",
              left: "50%",
              marginLeft: -24,
              marginTop: -48,
              width: "100%",
              height: "100%",
            }}
          >
            <Image
              source={
                images.pin || {
                  uri: "https://maps.gstatic.com/mapfiles/api-3/images/spotlight-poi2.png",
                }
              }
              style={{ width: 45, height: 48 }}
            />
          </View>
        )}
      </View>

      {/* Confirm Button - Now conditionally rendered based on keyboard state */}
      {!keyboardVisible && (
        <View 
          className="py-6 bg-white w-full items-center"
          style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            backgroundColor: 'white',
            zIndex: 9999,
            elevation: 999,
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: -2,
            },
            shadowOpacity: 0.1,
            shadowRadius: 2,
            paddingBottom: Platform.OS === 'ios' ? 40 : 20,
          }}
        >
          <View className="flex-row items-center w-full ml-20">
            <Image source={images.mapPin} className="w-6 h-6 mr-2" />
            <Text className="font-poppins-medium text-lg text-secondary-900">
              {locationDetails.area || "Selected Location"}
            </Text>
          </View>
          <View className="flex-row items-center w-full ml-20 mb-5">
            <Text className="text-secondary-600 text-sm ml-7">
              {locationDetails.fullAddress || "Loading address..."}
            </Text>
          </View>
          <ButtonBlueMain
            label="Confirm Location"
            onPress={handleConfirmLocation}
            bgVariant="primary"
            textVariant="primary"
            disabled={!selectedLocation}
            className="w-[400px] h-[80px]"
          />
        </View>
      )}
    </SafeAreaView>
  );
};

export default SelectLocationScreen;
