import { IsNotEmpty, IsString, IsBoolean, IsOptional, IsN<PERSON>ber, IsDateString } from 'class-validator';

export class RescheduleQueueDto {
  @IsNotEmpty()
  @IsString()
  oldQueueId: string;

  @IsNotEmpty()
  @IsString()
  serviceId: string;

  @IsNotEmpty()
  @IsString()
  userId: string;

  @IsNotEmpty()
  @IsString()
  userName: string;

  @IsNotEmpty()
  @IsString()
  mobileNumber: string;

  @IsNotEmpty()
  @IsDateString()
  date: string;

  @IsNotEmpty()
  @IsString()
  timeSlot: string;

  @IsOptional()
  @IsBoolean()
  isVIP?: boolean;

  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @IsOptional()
  @IsBoolean()
  hasSubUnits?: boolean;

  @IsOptional()
  @IsString()
  subUnitId?: string;

  @IsOptional()
  @IsString()
  subUnitName?: string;
}
