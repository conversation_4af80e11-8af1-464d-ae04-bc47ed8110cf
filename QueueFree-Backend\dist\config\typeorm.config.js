"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const typeorm_1 = require("typeorm");
const dotenv_1 = require("dotenv");
const service_entity_1 = require("../partner/entities/service.entity");
const service_setup_entity_1 = require("../partner/entities/service-setup.entity");
const user_entity_1 = require("../customer/user.entity");
const _1710538800000_AddEmailToServices_1 = require("../migrations/1710538800000-AddEmailToServices");
const _1710539000000_UpdateSetupDataToJsonb_1 = require("../migrations/1710539000000-UpdateSetupDataToJsonb");
const _1710539100000_AddTermsAcceptedToServices_1 = require("../migrations/1710539100000-AddTermsAcceptedToServices");
(0, dotenv_1.config)();
exports.default = new typeorm_1.DataSource({
    type: 'postgres',
    url: process.env.DATABASE_URL,
    entities: [service_entity_1.Service, service_setup_entity_1.ServiceSetup, user_entity_1.User],
    migrations: [
        _1710538800000_AddEmailToServices_1.AddEmailToServices1710538800000,
        _1710539000000_UpdateSetupDataToJsonb_1.UpdateSetupDataToJsonb1710539000000,
        _1710539100000_AddTermsAcceptedToServices_1.AddTermsAcceptedToServices1710539100000
    ],
    ssl: true,
    extra: {
        ssl: {
            rejectUnauthorized: false,
        },
    },
});
//# sourceMappingURL=typeorm.config.js.map