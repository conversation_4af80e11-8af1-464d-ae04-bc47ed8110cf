import React, { useCallback, useRef, useMemo, useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator, Platform, KeyboardAvoidingView, TextInput } from 'react-native';
import {
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetBackdrop,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';

interface ReviewSheetProps {
  visible: boolean;
  onClose: () => void;
  userRating: number;
  setUserRating: (rating: number) => void;
  userReview: string;
  setUserReview: (review: string) => void;
  onSubmit: () => void;
  isSubmitting: boolean;
}

const ReviewSheet: React.FC<ReviewSheetProps> = ({
  visible,
  onClose,
  userRating,
  setUserRating,
  userReview,
  setUserReview,
  onSubmit,
  isSubmitting,
}) => {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  // Local state for text input
  const [localReview, setLocalReview] = useState('');
  
  // Sync local state with parent state when component mounts or userReview changes
  useEffect(() => {
    setLocalReview(userReview);
  }, [userReview]);

  // Handle text change with memoization to prevent re-renders
  const onChangeText = useCallback((text: string) => {
    setLocalReview(text);
    setUserReview(text);
  }, [setUserReview]);

  // Snap points
  const snapPoints = useMemo(() => ['50%', '50%'], []);

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  // Handle sheet changes
  useEffect(() => {
    if (visible) {
      bottomSheetRef.current?.present();
    } else {
      bottomSheetRef.current?.dismiss();
    }
  }, [visible]);

  // Handle rating selection
  const handleRatingPress = (rating: number) => {
    setUserRating(rating);
  };

  // Render star based on selected rating
  const renderStar = (position: number) => {
    const filled = position <= userRating;
    return (
      <TouchableOpacity
        key={position}
        onPress={() => handleRatingPress(position)}
        className="p-1"
      >
        <View className={`w-8 h-8 items-center justify-center`}>
          <Text className={`text-3xl ${filled ? 'text-warning-500' : 'text-secondary-300'}`}>★</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <BottomSheetModalProvider>
      <BottomSheetModal
        ref={bottomSheetRef}
        index={0}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        enablePanDownToClose={true}
        onDismiss={onClose}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
      >
        <KeyboardAvoidingView 
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          className="flex-1"
          keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
        >
          <BottomSheetScrollView contentContainerClassName="px-10 pt-4 pb-8">
            <View className='w-full flex justify-center items-center'>

            <Text className="font-poppins-medium text-xl mb-6">Write a Review</Text>
            </View>
            
            <Text className="font-poppins-medium mb-2">Rating</Text>
            <View className="flex-row mb-6">
              {[1, 2, 3, 4, 5].map((star) => renderStar(star))}
            </View>

            <Text className="font-poppins-medium mb-2">Your review</Text>
            <View className='mb-6'>
              <TextInput
                className="p-3 h-32 border border-gray-300 rounded-lg text-secondary-500 font-[Poppins-Regular]"
                placeholder="Share your experience with this service..."
                placeholderTextColor="#9CA3AF"
                multiline
                scrollEnabled
                onChangeText={onChangeText}
                defaultValue={localReview}
                textAlignVertical="top"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <TouchableOpacity
              className={`w-full py-6 rounded-2xl items-center ${
                isSubmitting ? "bg-primary-300" : "bg-primary-500"
              }`}
              onPress={() => {
                // Update parent state with local state before submitting
                setUserReview(localReview);
                onSubmit();
              }}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text className="text-white font-poppins-medium">Submit Review</Text>
              )}
            </TouchableOpacity>
          </BottomSheetScrollView>
        </KeyboardAvoidingView>
      </BottomSheetModal>
    </BottomSheetModalProvider>
  );
};

export default ReviewSheet;
