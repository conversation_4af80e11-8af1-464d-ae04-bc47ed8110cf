"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddGracePeriodFields1744884495817 = void 0;
class AddGracePeriodFields1744884495817 {
    async up(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE "service_setups" 
            ADD COLUMN IF NOT EXISTS "graceTime" integer DEFAULT 120
        `);
        await queryRunner.query(`
            ALTER TABLE "queues" 
            ADD COLUMN IF NOT EXISTS "graceStartedAt" TIMESTAMP WITH TIME ZONE,
            ADD COLUMN IF NOT EXISTS "confirmedPresence" boolean DEFAULT false,
            ADD COLUMN IF NOT EXISTS "inGracePeriod" boolean DEFAULT false,
            ADD COLUMN IF NOT EXISTS "currentlyServing" boolean DEFAULT false,
            ADD COLUMN IF NOT EXISTS "servingStartedAt" TIMESTAMP WITH TIME ZONE,
            ADD COLUMN IF NOT EXISTS "statusUpdatedAt" TIMESTAMP WITH TIME ZONE
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE "service_setups" 
            DROP COLUMN IF EXISTS "graceTime"
        `);
        await queryRunner.query(`
            ALTER TABLE "queues" 
            DROP COLUMN IF EXISTS "graceStartedAt",
            DROP COLUMN IF EXISTS "confirmedPresence",
            DROP COLUMN IF EXISTS "inGracePeriod",
            DROP COLUMN IF EXISTS "currentlyServing",
            DROP COLUMN IF EXISTS "servingStartedAt",
            DROP COLUMN IF EXISTS "statusUpdatedAt"
        `);
    }
}
exports.AddGracePeriodFields1744884495817 = AddGracePeriodFields1744884495817;
//# sourceMappingURL=1744884495817-AddGracePeriodFields.js.map