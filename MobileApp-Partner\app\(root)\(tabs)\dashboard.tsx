import React, { useState, useEffect } from "react";
import {
  Text,
  SafeAreaView,
  View,
  StatusBar,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
  ScrollView,
  RefreshControl,
  Modal,
} from "react-native";
import { useAuth, useUser } from "@clerk/clerk-expo";
import { Redirect, router } from "expo-router";
import { useFocusEffect } from "@react-navigation/native";
import * as SecureStore from "expo-secure-store";
import ButtonBlueMain from "@/components/ButtonBlueMain";
import { images } from "@/constants";
import { clearAllSecureStoreData } from "@/utils/secureStore";

// Interface for time slot data
interface TimeSlot {
  timeSlot: string;
  normalQueueCount: number;
  vipQueueCount: number;
  status: "not-started" | "ongoing" | "ended";
}

// Interface for SubUnit
interface SubUnit {
  name: string;
  avgServeTime: string;
  pricePerHead: string;
  selectedDays: string[];
  useDayWiseTimeSlots?: boolean;
  availableHours: { [key: string]: Array<{ start: string; end: string }> };
}

export default function DashboardScreen() {
  const { isSignedIn } = useAuth();
  const { user } = useUser();
  const [setupRequired, setSetupRequired] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isServiceOpen, setIsServiceOpen] = useState(false);
  const [isTogglingService, setIsTogglingService] = useState(false);
  const [isStatusTextVisible, setIsStatusTextVisible] = useState(false);
  const [bankDetailsRequired, setBankDetailsRequired] = useState(false);
  const [leaveDaysRequired, setLeaveDaysRequired] = useState(false);

  // New state variables for time slots
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [serviceId, setServiceId] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [fetchingTimeSlots, setFetchingTimeSlots] = useState(false);

  // Add new state variable for service setup data
  const [serviceSetupData, setServiceSetupData] = useState<any>(null);

  // State for subunits functionality
  const [hasSubUnits, setHasSubUnits] = useState(false);
  const [subUnits, setSubUnits] = useState<SubUnit[]>([]);
  const [selectedSubUnit, setSelectedSubUnit] = useState<SubUnit | null>(null);
  const [showSubUnitsDropdown, setShowSubUnitsDropdown] = useState(false);
  const [allSubUnitTimeSlots, setAllSubUnitTimeSlots] = useState<
    Record<string, TimeSlot[]>
  >({});

  // API base URL
  const API_BASE_URL =
    "http://**************:3000/api";

  // Add a ref to track priority subunit
  const prioritySubUnitRef = React.useRef<string | null>(null);

  useEffect(() => {
    if (!isSignedIn) {
      clearAllSecureStoreData();
    }
  }, [isSignedIn]);

  const checkServiceSetup = async () => {
    try {
      if (!user?.primaryEmailAddress?.emailAddress) return;

      const email = encodeURIComponent(user.primaryEmailAddress?.emailAddress);

      // First get service ID
      const serviceResponse = await fetch(
        `${API_BASE_URL}/partner/service-details/${email}`
      );

      if (!serviceResponse.ok) {
        console.error("Failed to get service details");
        setSetupRequired(true);
        return;
      }

      const serviceData = await serviceResponse.json();
      console.log("Service details:", serviceData);

      if (!serviceData.id) {
        console.error("No service ID found");
        setSetupRequired(true);
        return;
      }

      // Save service ID for later use
      setServiceId(serviceData.id.toString());

      // Then check setup data
      const setupResponse = await fetch(
        `${API_BASE_URL}/partner/service-setup/${serviceData.id}`
      );

      if (!setupResponse.ok) {
        console.error("Failed to get setup data");
        setSetupRequired(true);
        return;
      }

      const setupData = await setupResponse.json();
      console.log("Setup data:", setupData);

      // Save service setup data
      if (setupData.hasSetup && setupData.data) {
        setServiceSetupData(setupData.data);

        // Debug service setup data structure
        console.log("Service setup data loaded:", {
          hasSubUnits: setupData.data.hasSubUnits,
          subUnitsCount: setupData.data.subUnits?.length || 0,
        });

        if (
          setupData.data.hasSubUnits === true &&
          Array.isArray(setupData.data.subUnits)
        ) {
          // Log detailed info for each subunit
          setupData.data.subUnits.forEach((subUnit: SubUnit, index: number) => {
            console.log(`Subunit ${index} info:`, {
              name: subUnit.name,
              selectedDays: subUnit.selectedDays,
              availableDaysKeys: Object.keys(subUnit.availableHours || {}),
            });
          });

          setHasSubUnits(true);
          setSubUnits(setupData.data.subUnits);

          // Set first subunit as default selected
          if (setupData.data.subUnits.length > 0) {
            setSelectedSubUnit(setupData.data.subUnits[0]);
          }
        }
      }

      // Show setup button only if hasSetup is false or if there's no setup data
      setSetupRequired(!setupData.hasSetup || !setupData.data);
    } catch (error) {
      console.error("Error checking setup status:", error);
      setSetupRequired(true);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchTodayTimeSlots = async () => {
    if (!serviceId) {
      console.log("Cannot fetch time slots: No serviceId available");
      return;
    }

    setFetchingTimeSlots(true);
    setRefreshing(true);

    try {
      // Get today's date in format YYYY-MM-DD
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, "0");
      const day = String(currentDate.getDate()).padStart(2, "0");
      const today = `${year}-${month}-${day}`;

      console.log("Fetching time slots for date:", today);

      if (hasSubUnits && subUnits.length > 0) {
        // For subunits, we'll directly use the service setup data instead of API
        // because the API is not correctly handling the subUnitId parameter
        const allTimeSlots: Record<string, TimeSlot[]> = {};

        // If we have a priority subunit (from restore), handle it first
        if (prioritySubUnitRef.current) {
          const prioritySubUnit = subUnits.find(
            (unit) => unit.name === prioritySubUnitRef.current
          );
          if (prioritySubUnit) {
            console.log(
              `Processing priority subunit first: ${prioritySubUnit.name}`
            );
            await processSubUnitTimeSlots(prioritySubUnit, today, allTimeSlots);
          }
          // Clear the priority ref after processing
          prioritySubUnitRef.current = null;
        }

        // Process each subunit to get its time slots
        for (let i = 0; i < subUnits.length; i++) {
          const subUnit = subUnits[i];
          // Skip if this is the priority subunit we already processed
          if (prioritySubUnitRef.current === subUnit.name) continue;

          await processSubUnitTimeSlots(subUnit, today, allTimeSlots);
        }

        // Update state with the new time slots map
        setAllSubUnitTimeSlots(allTimeSlots);

        // Set time slots for the currently selected subunit
        if (selectedSubUnit) {
          console.log(
            `Setting time slots for selected subunit: ${selectedSubUnit.name}`
          );
          setTimeSlots(allTimeSlots[selectedSubUnit.name] || []);
        } else if (subUnits.length > 0) {
          setSelectedSubUnit(subUnits[0]); // Ensure a subunit is selected
          setTimeSlots(allTimeSlots[subUnits[0].name] || []);
        }
      } else {
        // Regular service (no subunits) - use existing logic
        try {
          const response = await fetch(
            `${API_BASE_URL}/partner/services/${serviceId}/time-slots/${today}`
          );

          if (!response.ok) {
            throw new Error("Failed to fetch time slots from API");
          }

          const data = await response.json();
          console.log("Time slots from API:", data);

          if (data?.status === "success" && Array.isArray(data?.timeSlots)) {
            // Process API response
            const processedTimeSlots = await processApiTimeSlots(
              data.timeSlots,
              today
            );
            setTimeSlots(processedTimeSlots);
          } else {
            // Fallback to service setup data
            const setupTimeSlots = getTimeSlotsFromSetup(today).map(
              (slot: any) => ({
                ...slot,
                normalQueueCount: 0,
                vipQueueCount: 0,
                status: "not-started",
              })
            );

            // Determine status and fetch queue counts for each time slot
            const updatedTimeSlots = [];

            for (const slot of setupTimeSlots) {
              // Determine the status of the time slot based on current time
              const status = determineTimeSlotStatus(slot.timeSlot);

              // Fetch queue counts
              const queueCounts = await fetchSlotQueueCounts(
                serviceId,
                today,
                slot.timeSlot
              );

              updatedTimeSlots.push({
                ...slot,
                status,
                normalQueueCount: queueCounts?.normalCount || 0,
                vipQueueCount: queueCounts?.vipCount || 0,
              });
            }

            setTimeSlots(updatedTimeSlots);
          }
        } catch (error) {
          console.error("Error fetching time slots from API:", error);
          // Fallback to service setup data
          const setupTimeSlots = getTimeSlotsFromSetup(today).map(
            (slot: any) => ({
              ...slot,
              normalQueueCount: 0,
              vipQueueCount: 0,
              status: "not-started",
            })
          );

          const updatedTimeSlots = [];

          for (const slot of setupTimeSlots) {
            const status = determineTimeSlotStatus(slot.timeSlot);
            const queueCounts = await fetchSlotQueueCounts(
              serviceId,
              today,
              slot.timeSlot
            );

            updatedTimeSlots.push({
              ...slot,
              status,
              normalQueueCount: queueCounts?.normalCount || 0,
              vipQueueCount: queueCounts?.vipCount || 0,
            });
          }

          setTimeSlots(updatedTimeSlots);
        }
      }
    } catch (error) {
      console.error("Error in fetchTodayTimeSlots:", error);
      setTimeSlots([]);
    } finally {
      setFetchingTimeSlots(false);
      setRefreshing(false);
    }
  };

  // Modified getTimeSlotsFromSetup to accept a specific subunit
  const getTimeSlotsFromSetup = (date: string, specificSubUnit?: SubUnit) => {
    if (!serviceSetupData) {
      return [];
    }

    // Parse the date to get the day of week
    const dateParts = date.split("-");
    const year = parseInt(dateParts[0], 10);
    const month = parseInt(dateParts[1], 10) - 1; // Months are 0-indexed in JavaScript
    const day = parseInt(dateParts[2], 10);

    const dateObj = new Date(year, month, day);
    const dayOfWeek = dateObj.getDay();

    const dayNames = [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
    ];
    const dayName = dayNames[dayOfWeek];

    console.log(`Checking time slots for ${dayName} (from date: ${date})`);

    // Use a helper function to find matching day regardless of case
    const findMatchingDay = (
      haystack: { [key: string]: any },
      needle: string
    ): string | null => {
      // First try direct match
      if (haystack[needle]) {
        return needle;
      }

      // Try case-insensitive match
      const keys = Object.keys(haystack);
      const match = keys.find(
        (key) => key.toLowerCase() === needle.toLowerCase()
      );

      // Special handling for Sunday
      if (!match && needle.toLowerCase() === "sunday") {
        // Try common variants
        const specialMatch = keys.find(
          (key) => key === "sunday" || key === "Sunday" || key === "SUNDAY"
        );
        if (specialMatch) {
          console.log(`Found special match for Sunday: ${specialMatch}`);
          return specialMatch;
        }
      }

      return match || null;
    };

    // Helper to check if a day is in the selectedDays array regardless of case
    const isDaySelected = (selectedDays: string[], day: string): boolean => {
      return selectedDays.some((d) => d.toLowerCase() === day.toLowerCase());
    };

    // Function to get slots from availableHours with proper case handling
    const getSlotsFromAvailableHours = (
      availableHours: any,
      dayName: string
    ) => {
      const matchingDay = findMatchingDay(availableHours, dayName);

      if (!matchingDay) {
        return [];
      }

      console.log(`Found matching day '${matchingDay}' in availableHours`);
      return availableHours[matchingDay].map((slot: any) => {
        let startTime = slot.start;
        let endTime = slot.end;

        // Ensure time format has AM/PM
        if (
          startTime &&
          !startTime.includes("AM") &&
          !startTime.includes("PM")
        ) {
          startTime = formatTimeWithAMPM(startTime);
        }

        if (endTime && !endTime.includes("AM") && !endTime.includes("PM")) {
          endTime = formatTimeWithAMPM(endTime);
        }

        return {
          timeSlot: `${startTime} - ${endTime}`,
        };
      });
    };

    // If a specific subunit is provided
    if (specificSubUnit) {
      if (!isDaySelected(specificSubUnit.selectedDays, dayName)) {
        console.log(
          `${dayName} is not a selected day for ${specificSubUnit.name}`
        );
        return [];
      }

      console.log(
        `Processing time slots for subunit: ${specificSubUnit.name} on ${dayName}`
      );

      // Debug the structure of availableHours
      const availableDays = Object.keys(specificSubUnit.availableHours || {});
      console.log(
        `Available day keys for ${specificSubUnit.name}:`,
        availableDays
      );

      // Check for any form of "Sunday" case-insensitively
      const sundayVariations = availableDays.filter(
        (day) => day.toLowerCase() === "sunday"
      );
      if (sundayVariations.length > 0) {
        console.log(`Found Sunday variations:`, sundayVariations);
        console.log(
          `Value for first Sunday variation:`,
          specificSubUnit.availableHours[sundayVariations[0]]
        );
      }

      return getSlotsFromAvailableHours(
        specificSubUnit.availableHours,
        dayName
      );
    }
    // If we're using the current selected subunit
    else if (hasSubUnits && selectedSubUnit) {
      if (!isDaySelected(selectedSubUnit.selectedDays, dayName)) {
        console.log(
          `${dayName} is not a selected day for ${selectedSubUnit.name}`
        );
        return [];
      }

      console.log(
        `Processing time slots for selected subunit: ${selectedSubUnit.name} on ${dayName}`
      );

      // Debug the structure of availableHours
      const availableDays = Object.keys(selectedSubUnit.availableHours || {});
      console.log(
        `Available day keys for ${selectedSubUnit.name}:`,
        availableDays
      );

      // Check for any form of "Sunday" case-insensitively
      const sundayVariations = availableDays.filter(
        (day) => day.toLowerCase() === "sunday"
      );
      if (sundayVariations.length > 0) {
        console.log(`Found Sunday variations:`, sundayVariations);
        console.log(
          `Value for first Sunday variation:`,
          selectedSubUnit.availableHours[sundayVariations[0]]
        );
      }

      return getSlotsFromAvailableHours(
        selectedSubUnit.availableHours,
        dayName
      );
    }
    // Regular service - no subunits
    else {
      if (
        !serviceSetupData.selectedDays ||
        !isDaySelected(serviceSetupData.selectedDays, dayName)
      ) {
        console.log(`${dayName} is not a selected day for this service`);
        return [];
      }

      // Try to get time slots from availableHours
      if (serviceSetupData.availableHours) {
        return getSlotsFromAvailableHours(
          serviceSetupData.availableHours,
          dayName
        );
      }

      // Fallback to legacy timeSlots if availableHours doesn't exist
      if (serviceSetupData.timeSlots) {
        return serviceSetupData.timeSlots.map((slot: any) => {
          let startTime = slot.start;
          let endTime = slot.end;

          // Ensure time format has AM/PM
          if (
            startTime &&
            !startTime.includes("AM") &&
            !startTime.includes("PM")
          ) {
            startTime = formatTimeWithAMPM(startTime);
          }

          if (endTime && !endTime.includes("AM") && !endTime.includes("PM")) {
            endTime = formatTimeWithAMPM(endTime);
          }

          return {
            timeSlot: `${startTime} - ${endTime}`,
          };
        });
      }

      return [];
    }
  };

  // Helper function to format time with AM/PM
  const formatTimeWithAMPM = (timeString: string) => {
    try {
      const [hourStr, minuteStr] = timeString.split(":");
      const hour = parseInt(hourStr, 10);

      if (hour === 0) {
        return `12:${minuteStr} AM`;
      } else if (hour < 12) {
        return `${hour}:${minuteStr} AM`;
      } else if (hour === 12) {
        return `12:${minuteStr} PM`;
      } else {
        return `${hour - 12}:${minuteStr} PM`;
      }
    } catch (err) {
      return timeString; // Return original if parsing fails
    }
  };

  // Helper function to determine time slot status based on current time
  const determineTimeSlotStatus = (
    timeSlotStr: string
  ): "not-started" | "ongoing" | "ended" => {
    try {
      const [startTime, endTime] = timeSlotStr.split(" - ");

      // Parse start and end times into minutes
      const startMinutes = parseTimeToMinutes(startTime);
      const endMinutes = parseTimeToMinutes(endTime);

      // Get current time in minutes
      const now = new Date();
      const currentMinutes = now.getHours() * 60 + now.getMinutes();

      if (currentMinutes < startMinutes) {
        return "not-started";
      } else if (
        currentMinutes >= startMinutes &&
        currentMinutes < endMinutes
      ) {
        return "ongoing";
      } else {
        return "ended";
      }
    } catch (error) {
      console.error("Error determining time slot status:", error);
      return "not-started"; // Default fallback
    }
  };

  // Helper function to parse time string to minutes
  const parseTimeToMinutes = (timeString: string, period?: string) => {
    try {
      const [hourStr, minuteStr] = timeString.split(":");
      let hour = parseInt(hourStr, 10);
      const minute = parseInt(minuteStr, 10);

      // If period is provided, use it
      if (period) {
        if (period.toUpperCase() === "PM" && hour < 12) {
          hour += 12;
        } else if (period.toUpperCase() === "AM" && hour === 12) {
          hour = 0;
        }
      }
      // If no period but time string has AM/PM in it
      else if (minuteStr.includes("AM") || minuteStr.includes("PM")) {
        const isPM = minuteStr.includes("PM");
        // Extract the actual minutes without AM/PM
        const actualMinutes = parseInt(minuteStr.replace(/(AM|PM)/i, ""), 10);

        if (isPM && hour < 12) {
          hour += 12;
        } else if (!isPM && hour === 12) {
          hour = 0;
        }

        return hour * 60 + actualMinutes;
      }
      // Assume it's 24-hour format if no period is present

      return hour * 60 + minute;
    } catch (err) {
      console.error("Error parsing time:", err, timeString, period);
      return 0; // Default to midnight if parsing fails
    }
  };

  // Fetch queue counts for a specific time slot using Redis API
  const fetchSlotQueueCounts = async (
    serviceId: string,
    date: string,
    timeSlot: string,
    subUnitId?: number
  ): Promise<{ normalCount: number; vipCount: number } | null> => {
    try {
      // Log the date being used for debugging
      console.log(
        `Fetching queue counts for date: ${date}, timeSlot: ${timeSlot}${subUnitId !== undefined ? `, subUnitId: ${subUnitId}` : ""}`
      );

      // Use the Redis API to get all active queues
      let url = `${API_BASE_URL}/customer/queues/redis/${serviceId}/upcoming`;

      // Add subUnitId parameter if provided
      if (subUnitId !== undefined) {
        url += `?subUnitId=${subUnitId}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        console.log(
          `Redis queue data not available. Status: ${response.status}`
        );
        return null;
      }

      const data = await response.json();

      if (data.status !== "success" || !data.queues) {
        console.log("No queue data available from Redis");
        return { normalCount: 0, vipCount: 0 };
      }

      // Filter queues for this date and time slot
      const slotQueues = data.queues.filter((queue: any) => {
        if (!queue || !queue.date || !queue.timeSlot) return false;

        // Extract date in YYYY-MM-DD format
        let queueDateStr;
        try {
          if (typeof queue.date === "string") {
            // If date is a string, try to parse it to ensure correct format
            const queueDate = new Date(queue.date);
            const year = queueDate.getFullYear();
            const month = String(queueDate.getMonth() + 1).padStart(2, "0");
            const day = String(queueDate.getDate()).padStart(2, "0");
            queueDateStr = `${year}-${month}-${day}`;
          } else {
            // If date is already a Date object
            const queueDate = new Date(queue.date);
            const year = queueDate.getFullYear();
            const month = String(queueDate.getMonth() + 1).padStart(2, "0");
            const day = String(queueDate.getDate()).padStart(2, "0");
            queueDateStr = `${year}-${month}-${day}`;
          }
          console.log(
            `Queue date: ${queueDateStr}, comparing with: ${date}, match: ${queueDateStr === date}`
          );
        } catch (err) {
          console.error("Error parsing queue date:", err);
          return false;
        }

        // Also check if subUnitId matches, if provided
        if (subUnitId !== undefined && queue.subUnitId !== undefined) {
          return (
            queueDateStr === date &&
            queue.timeSlot === timeSlot &&
            Number(queue.subUnitId) === subUnitId
          );
        }

        return queueDateStr === date && queue.timeSlot === timeSlot;
      });

      // Count normal and VIP queues
      const normalCount = slotQueues.filter(
        (q: any) =>
          q &&
          q.isVIP !== true &&
          q.status !== "cancelled" &&
          q.status !== "completed"
      ).length;

      const vipCount = slotQueues.filter(
        (q: any) =>
          q &&
          q.isVIP === true &&
          q.status !== "cancelled" &&
          q.status !== "completed"
      ).length;

      return { normalCount, vipCount };
    } catch (error) {
      console.error(`Error fetching queue counts for time slot:`, error);
      return null;
    }
  };

  // Add the processApiTimeSlots function
  const processApiTimeSlots = async (timeSlotData: any[], date: string) => {
    try {
      const processedTimeSlots = [];

      for (const slot of timeSlotData) {
        // Determine the status of the time slot based on current time
        const status = determineTimeSlotStatus(slot.timeSlot);

        // Fetch queue counts
        const queueCounts = await fetchSlotQueueCounts(
          serviceId!,
          date,
          slot.timeSlot
        );

        processedTimeSlots.push({
          timeSlot: slot.timeSlot,
          status,
          normalQueueCount: queueCounts?.normalCount || 0,
          vipQueueCount: queueCounts?.vipCount || 0,
        });
      }

      return processedTimeSlots;
    } catch (error) {
      console.error("Error processing API time slots:", error);
      return [];
    }
  };

  const checkRequiredSetups = async () => {
    try {
      if (!user?.primaryEmailAddress?.emailAddress) return;

      const email = encodeURIComponent(user.primaryEmailAddress?.emailAddress);
      const serviceResponse = await fetch(
        `${API_BASE_URL}/partner/service-details/${email}`
      );

      if (!serviceResponse.ok) return;

      const serviceData = await serviceResponse.json();
      if (!serviceData.id) return;

      // Save service ID
      setServiceId(serviceData.id.toString());

      // Check service setup first to get hasSubUnits value
      let hasSubUnitsValue = hasSubUnits;
      const setupResponse = await fetch(
        `${API_BASE_URL}/partner/service-setup/${serviceData.id}`
      );
      if (setupResponse.ok) {
        const setupData = await setupResponse.json();
        setSetupRequired(!setupData.hasSetup || !setupData.data);

        // Get the hasSubUnits value from the setup data
        if (setupData.data && typeof setupData.data.hasSubUnits === "boolean") {
          hasSubUnitsValue = setupData.data.hasSubUnits;
          setHasSubUnits(hasSubUnitsValue);
        }
      }

      // Check bank details
      const bankResponse = await fetch(
        `${API_BASE_URL}/partner/bank-details/${serviceData.id}`
      );
      const bankData = await bankResponse.json();
      setBankDetailsRequired(!bankData.data);

      // Check leave days
      const leaveResponse = await fetch(
        `${API_BASE_URL}/partner/leave-days/${serviceData.id}`
      );
      const leaveData = await leaveResponse.json();

      // Check if leave days are required based on whether the service has subunits
      if (hasSubUnitsValue) {
        // For subunits, check if the subUnits object exists and has at least one entry with valid data
        const hasLeaveDays =
          leaveData.data?.subUnits &&
          Object.keys(leaveData.data.subUnits).length > 0 &&
          Object.values(leaveData.data.subUnits).some(
            (days: any) => Array.isArray(days) && days.length > 0
          );
        setLeaveDaysRequired(!hasLeaveDays);
      } else {
        // For regular services, check the length of the data array
        const hasLeaveDays =
          Array.isArray(leaveData.data) && leaveData.data.length > 0;
        setLeaveDaysRequired(!hasLeaveDays);
      }
    } catch (error) {
      console.error("Error checking required setups:", error);
    }
  };

  const toggleService = async (value: boolean) => {
    try {
      if (!user?.primaryEmailAddress?.emailAddress || isTogglingService) return;

      setIsTogglingService(true);
      setIsStatusTextVisible(false);

      const email = encodeURIComponent(user.primaryEmailAddress.emailAddress);
      const response = await fetch(
        `${API_BASE_URL}/partner/toggle-service/${email}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ isOpen: value }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to toggle service status");
      }

      // Get the updated status from backend instead of setting directly
      const data = await response.json();
      setIsServiceOpen(data.isOpen);
      setIsStatusTextVisible(true);
    } catch (error) {
      console.error("Error toggling service:", error);
      Alert.alert("Error", "Failed to toggle service status");
      // Fetch current state from backend instead of toggling
      fetchServiceStatus();
    } finally {
      setIsTogglingService(false);
    }
  };

  const fetchServiceStatus = async () => {
    try {
      setIsStatusTextVisible(false);
      if (!user?.primaryEmailAddress?.emailAddress) return;

      const email = encodeURIComponent(user.primaryEmailAddress.emailAddress);
      const response = await fetch(
        `${API_BASE_URL}/partner/service-details/${email}`
      );

      if (response.ok) {
        const data = await response.json();
        setIsServiceOpen(data.isOpen);
      }
      setIsStatusTextVisible(true);
    } catch (error) {
      console.error("Error fetching service status:", error);
    }
  };

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchTodayTimeSlots();
    setRefreshing(false);
  };

  useEffect(() => {
    const initialize = async () => {
      await checkServiceSetup();
      await fetchServiceStatus();
      await checkRequiredSetups();
    };
    initialize();
  }, []);

  // Fetch time slots whenever serviceId changes or when subunits are loaded
  useEffect(() => {
    if (serviceId) {
      console.log(
        "Triggering time slot fetch due to serviceId or subunits change"
      );
      loadSelectedSubUnit();
      fetchTodayTimeSlots();
    }
  }, [serviceId, hasSubUnits, subUnits.length]);

  // When the component comes into focus
  useFocusEffect(
    React.useCallback(() => {
      checkRequiredSetups();

      // If we have a service ID, always fetch time slots
      if (serviceId) {
        console.log("Triggering time slot fetch due to screen focus");

        // If we have a selected subunit, prioritize it
        if (selectedSubUnit) {
          prioritySubUnitRef.current = selectedSubUnit.name;
        }

        fetchTodayTimeSlots();
      }
    }, [serviceId, hasSubUnits, subUnits.length, selectedSubUnit?.name])
  );

  // Handler for subunit selection
  const handleSubUnitSelect = (subUnit: SubUnit) => {
    setSelectedSubUnit(subUnit);
    setTimeSlots(allSubUnitTimeSlots[subUnit.name] || []);
    setShowSubUnitsDropdown(false);

    // Save the selected subunit to SecureStore
    if (serviceId) {
      SecureStore.setItemAsync(
        `selected-subunit-${serviceId}`,
        subUnit.name
      ).catch((err) => console.error("Error saving selected subunit:", err));
    }
  };

  // Load saved selected subunit from SecureStore
  const loadSelectedSubUnit = async () => {
    if (!serviceId || !hasSubUnits || subUnits.length === 0) return;

    try {
      const savedSubUnitName = await SecureStore.getItemAsync(
        `selected-subunit-${serviceId}`
      );
      if (savedSubUnitName) {
        const subUnit = subUnits.find((unit) => unit.name === savedSubUnitName);
        if (subUnit) {
          console.log(`Restored selected subunit: ${subUnit.name}`);
          setSelectedSubUnit(subUnit);
          // Force time slot update for this subunit
          const currentSlotsForSubunit =
            allSubUnitTimeSlots[subUnit.name] || [];
          setTimeSlots(currentSlotsForSubunit);
          // Set a flag to prioritize this subunit when fetching time slots
          prioritySubUnitRef.current = subUnit.name;
        }
      }
    } catch (error) {
      console.error("Error loading selected subunit:", error);
    }
  };

  // Add the SubUnitsDropdown component
  const SubUnitsDropdown = () => {
    if (!hasSubUnits || subUnits.length === 0) return null;

    return (
      <View className="mb-4">
        {/* Dropdown button */}
        <TouchableOpacity
          onPress={() => setShowSubUnitsDropdown(!showSubUnitsDropdown)}
          className="flex-row items-center justify-between border border-gray-300 rounded-xl p-4"
        >
          <View className="flex-row items-center">
            <Image
              source={images.service}
              className="w-5 h-5 mr-3"
              tintColor="#159AFF"
            />
            <Text className="font-poppins-medium text-base">
              {selectedSubUnit?.name ||
                (subUnits.length > 0 ? subUnits[0].name : "Select Sub-Unit")}
            </Text>
          </View>
          <Image
            source={images.down}
            className={`w-4 h-3 ${showSubUnitsDropdown ? "rotate-180" : ""}`}
            tintColor="#6B7280"
          />
        </TouchableOpacity>

        {/* Dropdown Options */}
        {showSubUnitsDropdown && (
          <>
            {/* Touchable overlay to close dropdown */}
            <TouchableOpacity
              className="absolute inset-0 w-full h-full"
              style={{ top: -100, bottom: -1000 }}
              activeOpacity={0}
              onPress={() => setShowSubUnitsDropdown(false)}
            />

            <View
              className="absolute top-16 left-0 right-0 bg-white z-10 border border-gray-200 rounded-xl shadow-lg"
              style={{ maxHeight: 200 }}
            >
              <ScrollView nestedScrollEnabled={true} className="max-h-[200px]">
                {subUnits.map((unit, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => handleSubUnitSelect(unit)}
                    className={`p-4  ${index !== subUnits.length - 1 ? "border-b border-gray-200" : ""} ${selectedSubUnit?.name === unit.name ? "bg-primary-500/10" : ""}`}
                  >
                    <Text
                      className={`font-poppins-medium ${selectedSubUnit?.name === unit.name ? "text-primary-500" : "text-secondary-600"}`}
                    >
                      {unit.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </>
        )}
      </View>
    );
  };

  // Update time slots when selectedSubUnit changes
  useEffect(() => {
    if (selectedSubUnit && allSubUnitTimeSlots[selectedSubUnit.name]) {
      console.log(
        `Updating time slots for selected subunit: ${selectedSubUnit.name}`
      );
      setTimeSlots(allSubUnitTimeSlots[selectedSubUnit.name] || []);
    }
  }, [selectedSubUnit, allSubUnitTimeSlots]);

  // Helper function to process time slots for a single subunit
  const processSubUnitTimeSlots = async (
    subUnit: SubUnit,
    today: string,
    allTimeSlots: Record<string, TimeSlot[]>
  ) => {
    console.log(
      `Processing time slots for subunit: ${subUnit.name} (Index: ${subUnits.indexOf(subUnit)})`
    );

    // Get time slots directly from service setup data
    const setupTimeSlots = getTimeSlotsFromSetup(today, subUnit);

    if (setupTimeSlots.length === 0) {
      // No time slots available for this subunit on this day
      console.log(`No time slots available for ${subUnit.name} on ${today}`);
      allTimeSlots[subUnit.name] = [];
      return;
    }

    console.log(
      `Found ${setupTimeSlots.length} time slots for ${subUnit.name} on ${today}:`,
      setupTimeSlots
        .map((slot: { timeSlot: string }) => slot.timeSlot)
        .join(", ")
    );

    // Determine status and fetch queue counts for each time slot
    const updatedTimeSlots = [];

    for (const slot of setupTimeSlots) {
      // Determine the status of the time slot based on current time
      const status = determineTimeSlotStatus(slot.timeSlot);

      // Fetch queue counts
      const queueCounts = await fetchSlotQueueCounts(
        serviceId!,
        today,
        slot.timeSlot,
        subUnits.indexOf(subUnit)
      );

      updatedTimeSlots.push({
        ...slot,
        status,
        normalQueueCount: queueCounts?.normalCount || 0,
        vipQueueCount: queueCounts?.vipCount || 0,
      });
    }

    // Store the time slots for this subunit
    allTimeSlots[subUnit.name] = updatedTimeSlots;
  };

  if (!isSignedIn) {
    return <Redirect href="/(auth)/sign-in" />;
  }

  if (isLoading) {
    return null;
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      <View className="p-4">
        <View className="flex-row items-center justify-between mb-4 mx-4 mt-4">
          <View className="flex-row items-center">
            <TouchableOpacity
              onPress={() => toggleService(!isServiceOpen)}
              disabled={isTogglingService}
              style={{
                width: 60,
                flexDirection: "row",
                alignItems: "center",
                backgroundColor: isServiceOpen ? "#159AFF" : "#E5E5E5",
                borderRadius: 20,
                paddingVertical: 5,
                paddingHorizontal: 5,
                opacity: isTogglingService ? 0.5 : 1,
              }}
            >
              {isServiceOpen ? (
                <>
                  <View style={{ flex: 1 }} />
                  <View
                    style={{
                      width: 20,
                      height: 20,
                      borderRadius: 10,
                      backgroundColor: "white",
                    }}
                  />
                </>
              ) : (
                <>
                  <View
                    style={{
                      width: 20,
                      height: 20,
                      borderRadius: 10,
                      backgroundColor: "white",
                    }}
                  />
                  <View style={{ flex: 1 }} />
                </>
              )}
            </TouchableOpacity>
            {isTogglingService ? (
              <ActivityIndicator
                size="small"
                color="#455A64"
                style={{ marginLeft: 12 }}
              />
            ) : (
              <Text
                style={{
                  fontSize: 16,
                  fontWeight: "500",
                  marginLeft: 12,
                  opacity: isStatusTextVisible ? 1 : 0,
                }}
                className="font-poppins-medium text-black"
              >
                {isServiceOpen ? "Open" : "Closed"}
              </Text>
            )}
          </View>

          <TouchableOpacity
            className="flex-row items-center bg-secondary-500 px-4 py-3 rounded-xl"
            onPress={() => {}}
          >
            <View className="w-6 h-6  mr-2 items-center justify-center">
              <Image source={images.qr} className="w-5 h-5" />
            </View>
            <Text className="text-white font-poppins-medium text-base">
              View QR
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={["#159AFF"]}
            tintColor="#159AFF"
          />
        }
      >
        {setupRequired && (
          <View className="mt-2 flex justify-center items-center border-2 border-primary-400 rounded-3xl py-8 px-10 mx-16">
            <View>
              <View className="flex-row  items-center mb-1">
                <Image
                  source={images.tool}
                  style={{
                    width: 18,
                    height: 18,
                    marginRight: 6,
                    marginBottom: 3,
                    tintColor: "#000",
                  }}
                />
                <Text className="font-poppins-medium text-lg text-black">
                  Setup Your Service
                </Text>
              </View>
              <Text className="font-poppins-regular text-sm text-secondary-500  mb-5">
                Complete Your Setup to Get Started!
              </Text>
            </View>
            <ButtonBlueMain
              label="Service Setup"
              onPress={() => router.push("/service-setup/setup")}
              bgVariant="primary"
              textVariant="primary"
              className="w-full h-[50px] rounded-xl "
              textClassName="text-sm"
              iconClassName="w-3 h-3 mr-2 mb-1"
              iconStyle={{ tintColor: "#fff" }}
              icon={images.setup}
              noBorder
            />
          </View>
        )}

        {bankDetailsRequired && (
          <View className="mt-8 flex justify-center items-center border-2 border-primary-400 rounded-3xl py-8 px-10 mx-16">
            <View>
              <View className="flex-row items-center mb-1">
                <Image
                  source={images.payment}
                  className="w-[22px] h-[17px] mr-2 mb-1"
                  tintColor="#000"
                />
                <Text className="font-poppins-medium text-lg text-black">
                  Add Bank Details
                </Text>
              </View>
              <Text className="font-poppins-regular text-sm text-secondary-500 mb-5">
                Add your bank account for payments
              </Text>
            </View>
            <ButtonBlueMain
              label="Add Bank Details"
              onPress={() => router.push("/service-setup/update-bank-details")}
              bgVariant="primary"
              textVariant="primary"
              className="w-full h-[50px] rounded-xl"
              textClassName="text-sm"
              iconStyle={{ tintColor: "#fff" }}
              iconClassName="w-3 h-3 mr-2 mb-1"
              icon={images.bank}
              noBorder
            />
          </View>
        )}

        {leaveDaysRequired && (
          <View className="mt-8 flex justify-center items-center border-2 border-primary-400 rounded-3xl py-8 px-10 mx-16">
            <View>
              <View className="flex-row items-center mb-1">
                <Image
                  source={images.holiday}
                  className="w-[15px] h-[18px] mr-2 mb-1"
                  tintColor="#000"
                />
                <Text className="font-poppins-medium text-lg text-black">
                  Set Leave Days
                </Text>
              </View>
              <Text className="font-poppins-regular text-sm text-secondary-500 mb-5">
                Mark your holidays and leave days
              </Text>
            </View>
            <ButtonBlueMain
              label="Set Leave Days"
              onPress={() => router.push("/service-setup/update-leave-days")}
              bgVariant="primary"
              textVariant="primary"
              className="w-full h-[50px] rounded-xl"
              textClassName="text-sm"
              iconStyle={{ tintColor: "#fff" }}
              iconClassName="w-3 h-3 mr-2 mb-1"
              icon={images.leave}
              noBorder
            />
          </View>
        )}

        {hasSubUnits && (
          <View className="px-10 mt-8">
            <Text className="font-poppins-medium text-lg text-black mb-6">
              Select Sub-Unit
            </Text>
            <SubUnitsDropdown />
          </View>
        )}

        {/* Today's Time Slots Section */}
        <View className="mt-8 px-10">
          <View className="flex-row items-center justify-between mb-8">
            <Text className="font-poppins-medium text-lg text-black">
              Today's Time Slots
            </Text>
            <Text className="font-poppins-medium text-sm text-secondary-600">
              {new Date().toLocaleDateString("en-US", {
                weekday: "short",
                month: "short",
                day: "numeric",
              })}
            </Text>
          </View>

          {/* SubUnit selector dropdown (only shown when hasSubUnits is true) */}

          {fetchingTimeSlots && timeSlots.length === 0 ? (
            <View className="py-10 flex justify-center items-center">
              <ActivityIndicator size="large" color="#159AFF" />
              <Text className="mt-4 text-secondary-600">
                Loading time slots...
              </Text>
            </View>
          ) : timeSlots.length === 0 ? (
            <View className="py-10 flex justify-center items-center bg-gray-100 rounded-xl">
              <Image
                source={images.history}
                className="w-10 h-10 mb-2"
                tintColor="#6B7280"
              />
              <Text className="text-secondary-600 font-poppins-medium">
                No time slots available today
              </Text>
            </View>
          ) : (
            timeSlots.map((slot, index) => (
              <View
                key={index}
                className="mb-8 px-4 bg-white border flex-row items-center justify-between border-primary-200 rounded-3xl drop-shadow-lg shadow-lg shadow-primary-300"
              >
                <TouchableOpacity
                  className="flex-1 flex-row"
                  onPress={() => {
                    // Force a fresh date calculation, using the same method as in fetchTodayTimeSlots
                    const currentDate = new Date();
                    const year = currentDate.getFullYear();
                    const month = String(currentDate.getMonth() + 1).padStart(
                      2,
                      "0"
                    );
                    const day = String(currentDate.getDate()).padStart(2, "0");
                    const today = `${year}-${month}-${day}`;

                    // Include subUnit info in params if hasSubUnits is true
                    const params: any = {
                      timeSlot: slot.timeSlot,
                      date: today,
                      status: slot.status,
                      serviceId: serviceId,
                    };

                    // Add subunit information if applicable
                    if (hasSubUnits && selectedSubUnit) {
                      const subUnitIndex = subUnits.findIndex(
                        (unit) => unit.name === selectedSubUnit.name
                      );
                      params.subUnitName = selectedSubUnit.name;
                      params.subUnitId = subUnitIndex.toString();
                    }

                    router.push({
                      pathname: "/(root)/view-queue",
                      params,
                    });
                  }}
                >
                  <View className="p-6 flex-1">
                    <View className="flex-col justify-start items-start mb-2">
                      <Text className="font-poppins-medium text-lg mb-3">
                        {slot.timeSlot}
                      </Text>
                      <View
                        className={`px-4 py-2 rounded-xl ${
                          slot.status === "ongoing"
                            ? "bg-primary-500/10"
                            : slot.status === "ended"
                              ? "bg-gray-200"
                              : "bg-warning-500/10"
                        }`}
                      >
                        <Text
                          className={`text-xs font-poppins-medium ${
                            slot.status === "ongoing"
                              ? "text-primary-500"
                              : slot.status === "ended"
                                ? "text-secondary-600"
                                : "text-warning-600"
                          }`}
                        >
                          {slot.status === "ongoing"
                            ? "Ongoing"
                            : slot.status === "ended"
                              ? "Ended"
                              : "Not Started"}
                        </Text>
                      </View>
                    </View>
                  </View>
                  <View className="pr-4 flex-row items-center justify-center">
                    <Image
                      source={images.queueblue}
                      className="w-[22.5px] h-[19.2px] mr-2"
                      tintColor="#159AFF"
                    />
                    <View>
                      <Text className="font-poppins-semibold text-xl text-primary-500">
                        {slot.normalQueueCount + slot.vipQueueCount}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              </View>
            ))
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
