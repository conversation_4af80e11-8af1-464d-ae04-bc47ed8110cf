import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ScrollView,
  ActivityIndicator,
  Alert,
  Image,
} from "react-native";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import { useUser } from "@clerk/clerk-expo";
import { images } from "@/constants";
import { Calendar, DateData } from "react-native-calendars";

interface TimeSlot {
  timeSlot: string;
  normalQueueCount: number;
  vipQueueCount: number;
}

interface SubUnit {
  name: string;
  availableHours: {
    [day: string]: Array<{
      start: string;
      end: string;
    }>;
  };
  avgServeTime: string;
  pricePerHead: string;
  selectedDays: string[];
}

interface ServiceSetup {
  selectedDays: string[];
  timeSlots: Array<{
    start: string;
    end: string;
  }>;
  servingTime: string;
  basePrice: string;
  hasSubUnits?: boolean;
  subUnits?: SubUnit[];
  availableHours?: {
    [day: string]: Array<{
      start: string;
      end: string;
    }>;
  };
}

interface ServiceDetails {
  id: string;
  serviceName: string;
  serviceType: string;
  isOpen: boolean;
  queueInfo: {
    cost: number;
  };
}

interface MarkedDates {
  [date: string]: {
    selected?: boolean;
    marked?: boolean;
    disabled?: boolean;
    disableTouchEvent?: boolean;
    selectedColor?: string;
    dotColor?: string;
  };
}

// Interface for user queue positions
interface UserQueuePosition {
  timeSlot: string;
  position: number;
  isVIP: boolean;
}

const JoinQueueScreen = () => {
  const router = useRouter();
  const { serviceId, selectedSubUnitId } = useLocalSearchParams();
  const { user } = useUser();

  const [isLoading, setIsLoading] = useState(true);
  const [serviceDetails, setServiceDetails] = useState<ServiceDetails | null>(
    null
  );
  const [serviceSetup, setServiceSetup] = useState<ServiceSetup | null>(null);
  const [leaveDays, setLeaveDays] = useState<string[]>([]);
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string | null>(null);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [markedDates, setMarkedDates] = useState<MarkedDates>({});
  const [workingDays, setWorkingDays] = useState<number[]>([]);
  // Add state for user's queue positions
  const [userQueuePositions, setUserQueuePositions] = useState<UserQueuePosition[]>([]);
  const [checkingQueuePositions, setCheckingQueuePositions] = useState(false);
  // Add explanatory message for queue counts
  const [showCountInfo, setShowCountInfo] = useState(false);
  // Add debugging variable for day mapping
  const [dayMapDebug, setDayMapDebug] = useState<Record<string, number>>({});
  const [isCalendarLoading, setIsCalendarLoading] = useState(true);
  // Add separate loading state for timeslots
  const [isTimeSlotsLoading, setIsTimeSlotsLoading] = useState(false);
  // Add state for subunits
  const [hasSubUnits, setHasSubUnits] = useState(false);
  const [subUnits, setSubUnits] = useState<SubUnit[]>([]);
  const [selectedSubUnit, setSelectedSubUnit] = useState<SubUnit | null>(null);
  const [showSubUnitsModal, setShowSubUnitsModal] = useState(false);

  useEffect(() => {
    fetchServiceDetails();
    fetchServiceSetup();
    fetchLeaveDays();
  }, []);

  // Add effect to refresh leave days when selected subunit changes
  useEffect(() => {
    if (hasSubUnits && selectedSubUnit) {
      console.log(`Selected subunit changed to ${selectedSubUnit.name}, refreshing leave days`);
      // Reset leave days state first to avoid combining with previous subunit's leave days
      setLeaveDays([]);
      // Reset calendar state when subunit changes
      setIsCalendarLoading(true);
      setIsTimeSlotsLoading(false);
      setWorkingDays([]);
      setSelectedDate("");
      setSelectedTimeSlot(null);
      setTimeSlots([]);
      // Fetch leave days after resetting state
      fetchLeaveDays();
    }
  }, [selectedSubUnit]);

  useEffect(() => {
    if (serviceSetup && leaveDays !== null) {
      generateMarkedDates();
    }
  }, [serviceSetup, leaveDays, selectedSubUnit]);

  useEffect(() => {
    if (selectedDate) {
      fetchTimeSlotsForDate(selectedDate);
    }
  }, [selectedDate]);

  const fetchServiceDetails = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(
        `http://**************:3000/api/partner/services/${serviceId}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch service details");
      }

      const data = await response.json();
      setServiceDetails(data);
    } catch (error) {
      console.error("Error fetching service details:", error);
      Alert.alert("Error", "Failed to load service details");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchServiceSetup = async () => {
    try {
      console.log(`Fetching service setup for service ID: ${serviceId}`);
      const response = await fetch(
        `http://**************:3000/api/partner/service-setup/${serviceId}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch service setup");
      }

      const data = await response.json();
      console.log("Service setup API response:", JSON.stringify(data));

      if (data.status === "success" && data.hasSetup && data.data) {
        setServiceSetup(data.data);

        // Check if service has subunits
        if (data.data.hasSubUnits === true && Array.isArray(data.data.subUnits) && data.data.subUnits.length > 0) {
          console.log("Service has subunits:", data.data.subUnits.length);
          setHasSubUnits(true);
          setSubUnits(data.data.subUnits);

          // If a subunit was selected from the previous screen, use it
          if (selectedSubUnitId) {
            const subUnitIndex = parseInt(selectedSubUnitId as string, 10);
            if (!isNaN(subUnitIndex) && subUnitIndex >= 0 && subUnitIndex < data.data.subUnits.length) {
              setSelectedSubUnit(data.data.subUnits[subUnitIndex]);
              console.log("Selected subunit from params:", data.data.subUnits[subUnitIndex].name);
            } else {
              // Default to first subunit if invalid index
              setSelectedSubUnit(data.data.subUnits[0]);
              console.log("Selected default subunit:", data.data.subUnits[0].name);
            }
          } else {
            // Default to first subunit if none selected
            setSelectedSubUnit(data.data.subUnits[0]);
            console.log("No subunit selected, using default:", data.data.subUnits[0].name);
          }
        } else {
          // Regular service without subunits
          setHasSubUnits(false);
          setSelectedSubUnit(null);
          console.log("Service does not have subunits");
        }

        // Get working days from server for the next 7 days
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // To store server-determined working days
        const serverWorkingDays: number[] = [];
        const leaveDaysSet = new Set(leaveDays.map(d => new Date(d).toISOString().split('T')[0]));

        // Only check next 7 days from today
        for (let i = 0; i < 7; i++) {
          const testDate = new Date(today);
          testDate.setDate(today.getDate() + i);

          const dateString = testDate.toISOString().split("T")[0];
          const dayOfWeek = testDate.getDay();

          // Skip if it's a known leave day
          if (leaveDaysSet.has(dateString)) {
            console.log(`Skipping test for ${dateString} as it is a leave day.`);
            continue;
          }

          try {
            // Check if server provides time slots for this date
            const response = await fetch(
              `http://**************:3000/api/partner/services/${serviceId}/time-slots/${dateString}`
            );

            if (response.ok) {
              const data = await response.json();
              const hasTimeSlots = data.status === "success" && Array.isArray(data.timeSlots) && data.timeSlots.length > 0;

              console.log(`Server returned ${hasTimeSlots ? data.timeSlots.length : 0} time slots for ${dateString} (${getDayName(dayOfWeek)})`);

              // If server has time slots for this day (and it's not a leave day), it's a working day
              if (hasTimeSlots && !serverWorkingDays.includes(dayOfWeek)) {
                serverWorkingDays.push(dayOfWeek);
                console.log(`Adding ${getDayName(dayOfWeek)} (${dayOfWeek}) to server working days`);
              }
            } else {
              console.log(`API check for ${dateString} failed with status: ${response.status}`);
            }
          } catch (error) {
            console.error(`Error testing date ${dateString}:`, error);
          }

          // Small delay between requests to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        // Ensure working days are unique and sorted for consistency
        const finalWorkingDays = [...new Set(serverWorkingDays)].sort((a, b) => a - b);

        // Update working days state with server-determined days
        console.log("Setting server-determined working days:", finalWorkingDays);
        setWorkingDays(finalWorkingDays);
        setIsCalendarLoading(false);
      }
    } catch (error) {
      console.error("Error fetching service setup:", error);
      Alert.alert("Error", "Failed to load service setup");
      setIsCalendarLoading(false);
    }
  };

  const fetchLeaveDays = async () => {
    console.log(`Fetching leave days for service ID: ${serviceId}${hasSubUnits && selectedSubUnit ? `, subunit: ${selectedSubUnit.name}` : ''}`);
    setIsCalendarLoading(true);
    try {
      const response = await fetch(
        `http://**************:3000/api/partner/leave-days/${serviceId}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch leave days");
      }

      const data = await response.json();
      if (data.status === "success") {
        if (hasSubUnits && selectedSubUnit) {
          // For subunits, check if there are specific leave days for this subunit
          if (data.data && data.data.subUnits) {
            // Find the subunit index to match with leave days structure
            const subUnitIndex = subUnits.findIndex(unit => unit.name === selectedSubUnit.name);
            const subUnitKey = `subunit-${subUnitIndex}`;

            if (data.data.subUnits[subUnitKey] && Array.isArray(data.data.subUnits[subUnitKey])) {
              console.log(`Found leave days for subunit ${selectedSubUnit.name}:`, data.data.subUnits[subUnitKey].length);
              // Only use leave days specific to this subunit
              setLeaveDays(data.data.subUnits[subUnitKey]);
            } else {
              // If no specific leave days for this subunit, use empty array (not main leave days)
              console.log(`No specific leave days for subunit ${selectedSubUnit.name}, using empty array`);
              setLeaveDays([]);
            }
          } else {
            // Fallback to empty array if the new structure isn't available
            console.log("No subUnits structure in leave days data, using empty array");
            setLeaveDays([]);
          }
        } else {
          // For regular services, use main leave days or legacy format
          if (data.data && data.data.main && Array.isArray(data.data.main)) {
            console.log("Using main leave days:", data.data.main.length);
            setLeaveDays(data.data.main);
          } else {
            // Fallback to legacy format
            setLeaveDays(Array.isArray(data.data) ? data.data : []);
          }
        }
      } else {
        setLeaveDays([]);
      }

      // After setting leave days, update working days for the selected subunit
      if (hasSubUnits && selectedSubUnit) {
        updateWorkingDaysForSubunit();
      }
    } catch (error) {
      console.error("Error fetching leave days:", error);
      setLeaveDays([]);
      setIsCalendarLoading(false);
    }
  };

  // Add function to fetch queue counts directly from Redis
  const fetchRedisQueueCounts = async (serviceId: string): Promise<{ normalCount: number; vipCount: number } | null> => {
    try {
      console.log(`Fetching queue counts for service ${serviceId} using Redis keys`);

      // Determine the API endpoint based on whether we're using subunits
      let apiUrl = `http://**************:3000/api/customer/queues/redis/${serviceId}`;

      // If using subunits, add the subunit parameter
      if (hasSubUnits && selectedSubUnit) {
        // Find the index of the selected subunit
        const subUnitIndex = subUnits.findIndex(unit => unit.name === selectedSubUnit.name);
        if (subUnitIndex !== -1) {
          apiUrl += `?subUnitId=${subUnitIndex}`;
          console.log(`Using subunit-specific queue API for ${selectedSubUnit.name} in fetchRedisQueueCounts`);
        }
      }

      // Get all active queues for this service using the Redis API
      const response = await fetch(apiUrl);

      if (response.ok) {
        const data = await response.json();

        if (data.status === "success" && data.queues) {
          console.log(`Successfully retrieved ${data.queues.length} queues from Redis`);

          // Group queues by time slot
          const timeSlotGroups: Record<string, any[]> = {};
          data.queues.forEach((queue: any) => {
            if (queue && queue.timeSlot) {
              if (!timeSlotGroups[queue.timeSlot]) {
                timeSlotGroups[queue.timeSlot] = [];
              }
              timeSlotGroups[queue.timeSlot].push(queue);
            }
          });

          // Get the current time in 24 hour format
          const now = new Date();
          const currentHours = now.getHours();
          const currentMinutes = now.getMinutes();
          const currentTime = currentHours * 60 + currentMinutes; // in minutes

          // Find current or next time slot
          let selectedTimeSlot = '';
          let selectedTimeSlotQueues: any[] = [];

          const timeSlots = Object.keys(timeSlotGroups);
          if (timeSlots.length > 0) {
            // Convert time slots to comparable format and find current or next slot
            const timeSlotTimes = timeSlots.map(slot => {
              const parts = slot.split(' - ');
              const startTimePart = parts[0].trim();
              const [timeValue, period] = startTimePart.split(' ');
              const [hourStr, minuteStr] = timeValue.split(':');
              let hour = parseInt(hourStr, 10);
              const minute = parseInt(minuteStr, 10);

              if (period.toUpperCase() === 'PM' && hour < 12) {
                hour += 12;
              } else if (period.toUpperCase() === 'AM' && hour === 12) {
                hour = 0;
              }

              return {
                slot,
                time: hour * 60 + minute
              };
            });

            // Sort by time
            timeSlotTimes.sort((a, b) => a.time - b.time);

            // Find the current time slot
            let selectedSlotInfo = timeSlotTimes[0]; // Default to first

            // First, try to find a time slot that contains the current time
            for (let i = 0; i < timeSlotTimes.length; i++) {
              const currentSlot = timeSlotTimes[i];
              const nextSlot = i < timeSlotTimes.length - 1 ? timeSlotTimes[i + 1] : null;

              // Check if current time is within this slot's range
              // For the last slot or if current time is before the next slot starts
              if (!nextSlot || currentTime < nextSlot.time) {
                if (currentTime >= currentSlot.time) {
                  selectedSlotInfo = currentSlot;
                  console.log(`Found current time slot: ${currentSlot.slot} (current time: ${currentHours}:${currentMinutes})`);
                  break;
                }
              }
            }

            // If no current slot found (e.g., we're between slots or before all slots),
            // find the next upcoming slot
            if (selectedSlotInfo.time > currentTime) {
              console.log(`No current time slot active, using next slot: ${selectedSlotInfo.slot}`);
            }

            selectedTimeSlot = selectedSlotInfo.slot;
            selectedTimeSlotQueues = timeSlotGroups[selectedTimeSlot] || [];
          }

          // Count normal and VIP queues for the selected time slot
          const normalCount = selectedTimeSlotQueues.filter((q: any) =>
            q &&
            q.isVIP !== true &&
            q.status !== 'cancelled' &&
            q.status !== 'completed'
          ).length;

          const vipCount = selectedTimeSlotQueues.filter((q: any) =>
            q &&
            q.isVIP === true &&
            q.status !== 'cancelled' &&
            q.status !== 'completed'
          ).length;

          console.log(`Current/next time slot: ${selectedTimeSlot}, Normal: ${normalCount}, VIP: ${vipCount}`);

          return { normalCount, vipCount };
        }
      }

      console.log("No queue data available from Redis");
      return null;
    } catch (error) {
      console.error(`Error fetching Redis queue counts for service ${serviceId}:`, error);
      return null;
    }
  };

  // Function to calculate position in queue using Redis position data - exact same as queue-status.tsx
  const checkUserQueuePositions = async (date: string) => {
    try {
      console.log(`Checking user queue positions for date: ${date}`);
      if (!user?.id || !serviceId) return;

      // First get all user queues for this service and date
      // Determine the API endpoint based on whether we're using subunits
      let apiUrl = `http://**************:3000/api/customer/queues/redis/${serviceId}/upcoming`;

      // If using subunits, add the subunit parameter
      if (hasSubUnits && selectedSubUnit) {
        // Find the index of the selected subunit
        const subUnitIndex = subUnits.findIndex(unit => unit.name === selectedSubUnit.name);
        if (subUnitIndex !== -1) {
          apiUrl += `?subUnitId=${subUnitIndex}`;
          console.log(`Using subunit-specific queue API for ${selectedSubUnit.name}`);
        }
      }

      // Get user queues for this service and date using Redis direct access with upcoming status
      const response = await fetch(apiUrl);

      if (!response.ok) {
        console.error("Failed to fetch queues from Redis:", response.status);
        setUserQueuePositions([]);
        return;
      }

      const data = await response.json();

      if (data.status !== "success" || !data.queues) {
        console.error("No queues data available from Redis");
        setUserQueuePositions([]);
        return;
      }

      // Filter to get only user's queues for this date
      const userQueueData = data.queues.filter((queue: any) => {
        if (!queue || !queue.userId || !queue.date) return false;

        // Carefully extract and format the queue date
        let queueDateStr;
        try {
          if (typeof queue.date === 'string') {
            queueDateStr = queue.date.split('T')[0];
          } else {
            queueDateStr = new Date(queue.date).toISOString().split('T')[0];
          }
        } catch (err) {
          console.error(`Error parsing queue date: ${err}`);
          return false;
        }

        // Compare user ID and date
        const userIdMatch = queue.userId === user.id;
        const dateMatch = queueDateStr === date;

        if (userIdMatch && dateMatch) {
          console.log(`Found user queue match: ${queue.id} for date ${date}`);
        }

        return userIdMatch && dateMatch;
      });

      if (userQueueData.length === 0) {
        console.log(`No queues found for user on date ${date}`);
        setUserQueuePositions([]);
        return;
      }

      console.log(`Found ${userQueueData.length} user queues for date ${date}`);

      // Process each user queue to get position from Redis
      const positions: UserQueuePosition[] = [];

      for (const userQueue of userQueueData) {
        if (!userQueue.timeSlot || !userQueue.id) continue;

        console.log(`Getting position for user queue ${userQueue.id} in time slot ${userQueue.timeSlot}`);

        const start = performance.now();

        // First check if position is already in the queue data
        if (userQueue.position !== undefined) {
          console.log(`Using position directly from queue data: ${userQueue.position}`);
          positions.push({
            timeSlot: userQueue.timeSlot,
            position: userQueue.position,
            isVIP: userQueue.isVIP === true
          });
          continue;
        }

        // If not, try to get the full queue details first
        try {
          const queueDetailsResponse = await fetch(
            `http://**************:3000/api/customer/queue/${userQueue.id}`
          );

          if (queueDetailsResponse.ok) {
            const queueDetailsData = await queueDetailsResponse.json();

            if (queueDetailsData.status === "success" && queueDetailsData.queue) {
              const queueDetails = queueDetailsData.queue;

              // Check if position is available in the queue details
              if (queueDetails.position !== undefined) {
                console.log(`Got position from queue details: ${queueDetails.position}`);
                positions.push({
                  timeSlot: userQueue.timeSlot,
                  position: queueDetails.position,
                  isVIP: queueDetails.isVIP === true || userQueue.isVIP === true
                });

                console.log(`Queue ${userQueue.id} position from queue details: ${queueDetails.position}, Time=${Math.round(performance.now() - start)}ms`);
                continue;
              }
            }
          }
        } catch (error) {
          console.error("Error fetching queue details:", error);
          // Continue to next method if queue details fetch fails
        }

        // If queue details doesn't have position, try the dedicated position endpoint
        try {
          const positionResponse = await fetch(
            `http://**************:3000/api/customer/queue/${userQueue.id}/position`
          );

          if (positionResponse.ok) {
            const positionData = await positionResponse.json();

            if (positionData.status === "success" && positionData.position !== undefined) {
              console.log(`Got position from Redis: ${positionData.position}`);
              positions.push({
                timeSlot: userQueue.timeSlot,
                position: positionData.position,
                isVIP: userQueue.isVIP === true
              });

              console.log(`Queue ${userQueue.id} position from Redis: ${positionData.position}, Time=${Math.round(performance.now() - start)}ms`);
              continue;
            }
          }
        } catch (error) {
          console.error("Error fetching position from Redis:", error);
          // Continue to fallback method if Redis fetch fails
        }

        // Fallback to the active queues approach if Redis position is not available
        console.log("Falling back to active queues approach for position calculation");

        try {
          const activeQueuesResponse = await fetch(
            `http://**************:3000/api/customer/queues/active/${serviceId}/${date}`
          );

          if (!activeQueuesResponse.ok) {
            console.error(`Failed to fetch active queues: ${activeQueuesResponse.status}`);
            // If we can't get position, use 0 as default
            positions.push({
              timeSlot: userQueue.timeSlot,
              position: 0,
              isVIP: userQueue.isVIP === true
            });
            continue;
          }

          const activeQueuesData = await activeQueuesResponse.json();

          if (activeQueuesData.status === "success" && activeQueuesData.queues) {
            // Filter to get queues for this time slot only
            const timeSlotQueues = activeQueuesData.queues.filter(
              (q: any) => q && q.timeSlot === userQueue.timeSlot
            );

            // Sort queues by position first
            const sortedQueues = [...timeSlotQueues].sort((a: any, b: any) => {
              // First sort by position
              const positionDiff = (a.position || 0) - (b.position || 0);

              // If positions are the same, then consider VIP status
              if (positionDiff === 0) {
                if (a.isVIP && !b.isVIP) return -1;
                if (!a.isVIP && b.isVIP) return 1;

                // If VIP status is the same, sort by creation time
                try {
                  const aTime = a.createdAt ? new Date(a.createdAt).getTime() : 0;
                  const bTime = b.createdAt ? new Date(b.createdAt).getTime() : 0;
                  return aTime - bTime;
                } catch (error) {
                  return 0;
                }
              }

              return positionDiff;
            });

            // Find the position of the user's queue
            const userQueueId = userQueue.id || userQueue._id;
            const position = sortedQueues.findIndex((q: any) => {
              if (!q) return false;

              const queueId = q._id || q.id;

              if (!queueId || !userQueueId) return false;

              try {
                return queueId.toString() === userQueueId.toString();
              } catch (error) {
                console.error("Error comparing queue IDs:", error);
                return false;
              }
            });

            // Position is 1-indexed
            const queuePosition = position !== -1 ? position + 1 : 0;

            console.log(`Queue ${userQueue.id} position from sorted queues: ${queuePosition}`);

            positions.push({
              timeSlot: userQueue.timeSlot,
              position: queuePosition,
              isVIP: userQueue.isVIP === true
            });
          } else {
            // If we can't get position, use 0 as default
            positions.push({
              timeSlot: userQueue.timeSlot,
              position: 0,
              isVIP: userQueue.isVIP === true
            });
          }
        } catch (error) {
          console.error("Error in fallback position calculation:", error);
          // If all methods fail, use 0 as default
          positions.push({
            timeSlot: userQueue.timeSlot,
            position: 0,
            isVIP: userQueue.isVIP === true
          });
        }
      }

      setUserQueuePositions(positions);

    } catch (error) {
      console.error("Error checking user queue positions:", error);
      setUserQueuePositions([]);
    }
  };



  // Modified function to use direct Redis access for queue counts
  const fetchRedisTimeSlots = async (serviceId: string, date: string): Promise<TimeSlot[] | null> => {
    try {
      console.log(`Fetching time slots for service: ${serviceId}, date: ${date}`);

      const start = performance.now();

      // Determine the API endpoint based on whether we're using subunits
      let apiUrl = `http://**************:3000/api/partner/services/${serviceId}/time-slots/${date}`;

      // If using subunits, add the subunit parameter
      if (hasSubUnits && selectedSubUnit) {
        // Find the index of the selected subunit
        const subUnitIndex = subUnits.findIndex(unit => unit.name === selectedSubUnit.name);
        if (subUnitIndex !== -1) {
          apiUrl += `?subUnitId=${subUnitIndex}`;
          console.log(`Using subunit-specific time slots API for ${selectedSubUnit.name} in fetchRedisTimeSlots`);
        }
      }

      // Get time slots from partner API
      const timeSlotsResponse = await fetch(apiUrl);

      if (!timeSlotsResponse.ok) {
        throw new Error(`Failed to fetch time slots: ${timeSlotsResponse.status}`);
      }

      const timeSlotsData = await timeSlotsResponse.json();

      if (timeSlotsData.status !== "success" || !timeSlotsData.timeSlots) {
        throw new Error("No time slots available");
      }

      // Filter past time slots first
      const filteredTimeSlots = filterPastTimeSlots(date, timeSlotsData.timeSlots);
      console.log(`After filtering past slots, ${filteredTimeSlots.length} time slots remain`);

      // Try to get queue data from direct Redis access
      // Determine the Redis API endpoint based on whether we're using subunits
      let redisApiUrl = `http://**************:3000/api/customer/queues/redis/${serviceId}/upcoming`;

      // If using subunits, add the subunit parameter
      if (hasSubUnits && selectedSubUnit) {
        // Find the index of the selected subunit
        const subUnitIndex = subUnits.findIndex(unit => unit.name === selectedSubUnit.name);
        if (subUnitIndex !== -1) {
          redisApiUrl += `?subUnitId=${subUnitIndex}`;
          console.log(`Using subunit-specific Redis API for ${selectedSubUnit.name} in fetchRedisTimeSlots`);
        }
      }

      const redisResponse = await fetch(redisApiUrl);

      if (!redisResponse.ok) {
        console.log(`Redis queue data not available, falling back to active queues endpoint. Status: ${redisResponse.status}`);
        return await fetchRedisTimeSlotsOriginal(serviceId, date);
      }

      const redisData = await redisResponse.json();

      // Handle success response with empty queues array
      if (redisData.status === "success") {
        // Initialize queues array if it's missing or null
        const queues = redisData.queues || [];
        console.log(`Found ${queues.length} queues from Redis response`);

        // Filter queues for the requested date - convert both to YYYY-MM-DD format to ensure matching
        const dateQueues = queues.filter((queue: any) => {
          if (!queue || !queue.date) return false;

          // Safely convert queue date to string
          let queueDateStr;
          try {
            if (typeof queue.date === 'string') {
              queueDateStr = queue.date.split('T')[0]; // Handle ISO string
            } else {
              queueDateStr = new Date(queue.date).toISOString().split('T')[0];
            }
          } catch (err) {
            console.error("Error parsing queue date:", err);
            return false;
          }

          // Debug logs for date comparison
          console.log(`Comparing queue date ${queueDateStr} with selected date ${date}`);

          return queueDateStr === date;
        });

        console.log(`Found ${dateQueues.length} queues for date ${date} in Redis`);

        // Group queues by time slot
        const timeSlotGroups: Record<string, any[]> = {};
        dateQueues.forEach((queue: any) => {
          if (queue && queue.timeSlot) {
            if (!timeSlotGroups[queue.timeSlot]) {
              timeSlotGroups[queue.timeSlot] = [];
            }
            timeSlotGroups[queue.timeSlot].push(queue);
          }
        });

        // Update time slots with accurate queue counts
        const updatedTimeSlots = filteredTimeSlots.map(slot => {
          const slotQueues = timeSlotGroups[slot.timeSlot] || [];

          // Count normal and VIP queues with status filtering
          const normalCount = slotQueues.filter((q: any) =>
            q &&
            q.isVIP !== true &&
            q.status !== 'cancelled' &&
            q.status !== 'completed'
          ).length;

          const vipCount = slotQueues.filter((q: any) =>
            q &&
            q.isVIP === true &&
            q.status !== 'cancelled' &&
            q.status !== 'completed'
          ).length;

          console.log(`Slot ${slot.timeSlot}: Normal=${normalCount}, VIP=${vipCount}`);

          return {
            ...slot,
            normalQueueCount: normalCount,
            vipQueueCount: vipCount
          };
        });

        const end = performance.now();
        console.log(`Time slots with Redis queue counts fetched in ${Math.round(end - start)}ms`);

        return updatedTimeSlots;
      } else {
        console.log(`Redis response error: ${redisData.status}, ${redisData.message || 'No message'}`);
        return await fetchRedisTimeSlotsOriginal(serviceId, date);
      }
    } catch (error) {
      console.error("Error fetching Redis time slots:", error);
      return null;
    }
  };

  // Original time slot fetch method for fallback
  const fetchRedisTimeSlotsOriginal = async (serviceId: string, date: string): Promise<TimeSlot[] | null> => {
    try {
      console.log(`Falling back to original time slots endpoint for service: ${serviceId}, date: ${date}`);

      // Determine the API endpoint based on whether we're using subunits
      let apiUrl = `http://**************:3000/api/partner/services/${serviceId}/time-slots/${date}`;

      // If using subunits, add the subunit parameter
      if (hasSubUnits && selectedSubUnit) {
        // Find the index of the selected subunit
        const subUnitIndex = subUnits.findIndex(unit => unit.name === selectedSubUnit.name);
        if (subUnitIndex !== -1) {
          apiUrl += `?subUnitId=${subUnitIndex}`;
          console.log(`Using subunit-specific time slots API for ${selectedSubUnit.name} in fetchRedisTimeSlotsOriginal`);
        }
      }

      // Get time slots from partner API
      const timeSlotsResponse = await fetch(apiUrl);

      if (!timeSlotsResponse.ok) {
        throw new Error("Failed to fetch time slots");
      }

      const timeSlotsData = await timeSlotsResponse.json();

      if (timeSlotsData.status !== "success" || !timeSlotsData.timeSlots) {
        throw new Error("No time slots available");
      }

      // Get active queues
      const activeQueuesResponse = await fetch(
        `http://**************:3000/api/customer/queues/active/${serviceId}/${date}`
      );

      if (!activeQueuesResponse.ok) {
        throw new Error("Failed to fetch active queues");
      }

      const activeQueuesData = await activeQueuesResponse.json();

      if (activeQueuesData.status !== "success" || !activeQueuesData.queues) {
        throw new Error("No active queues available");
      }

      // Filter past time slots first
      const filteredTimeSlots = filterPastTimeSlots(date, timeSlotsData.timeSlots);

      // Group active queues by time slot
      const activeQueuesByTimeSlot: Record<string, any[]> = {};
      activeQueuesData.queues.forEach((queue: any) => {
        if (queue && queue.timeSlot) {
          if (!activeQueuesByTimeSlot[queue.timeSlot]) {
            activeQueuesByTimeSlot[queue.timeSlot] = [];
          }
          activeQueuesByTimeSlot[queue.timeSlot].push(queue);
        }
      });

      // Update time slots with accurate queue counts
      const updatedTimeSlots = filteredTimeSlots.map(slot => {
        const slotQueues = activeQueuesByTimeSlot[slot.timeSlot] || [];
        const normalCount = slotQueues.filter((q: any) => q && q.isVIP !== true).length;
        const vipCount = slotQueues.filter((q: any) => q && q.isVIP === true).length;

        return {
          ...slot,
          normalQueueCount: normalCount,
          vipQueueCount: vipCount
        };
      });

      return updatedTimeSlots;
    } catch (error) {
      console.error("Error fetching original time slots:", error);
      return null;
    }
  };

  // This function is no longer used - we're using fetchTimeSlotsForDate directly
  const fetchTimeSlotsWithAccurateCounts = async (date: string) => {
    // Just call the current implementation
    await fetchTimeSlotsForDate(date);
  };

  // Replace the existing fetchTimeSlotsForDate with our new function
  // Add function to update working days for the selected subunit
  const updateWorkingDaysForSubunit = async () => {
    if (!hasSubUnits || !selectedSubUnit) {
      console.log("No subunit selected, skipping working days update");
      setIsCalendarLoading(false);
      return;
    }

    try {
      console.log(`Updating working days for subunit: ${selectedSubUnit.name}`);

      // Get working days from server for the next 7 days
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // To store server-determined working days
      const serverWorkingDays: number[] = [];

      // Use the subunit-specific leave days that were set in fetchLeaveDays
      const leaveDaysSet = new Set(leaveDays.map(d => new Date(d).toISOString().split('T')[0]));
      console.log(`Using ${leaveDays.length} leave days specific to subunit ${selectedSubUnit.name}`);

      // Only check next 7 days from today
      for (let i = 0; i < 7; i++) {
        const testDate = new Date(today);
        testDate.setDate(today.getDate() + i);

        const dateString = testDate.toISOString().split("T")[0];
        const dayOfWeek = testDate.getDay();

        // Skip if it's a known leave day for this specific subunit
        if (leaveDaysSet.has(dateString)) {
          console.log(`Skipping test for ${dateString} as it is a leave day for subunit ${selectedSubUnit.name}.`);
          continue;
        }

        try {
          // Find the index of the selected subunit
          const subUnitIndex = subUnits.findIndex(unit => unit.name === selectedSubUnit.name);

          // Check if server provides time slots for this date with the subunit parameter
          const response = await fetch(
            `http://**************:3000/api/partner/services/${serviceId}/time-slots/${dateString}?subUnitId=${subUnitIndex}`
          );

          if (response.ok) {
            const data = await response.json();
            const hasTimeSlots = data.status === "success" && Array.isArray(data.timeSlots) && data.timeSlots.length > 0;

            console.log(`Server returned ${hasTimeSlots ? data.timeSlots.length : 0} time slots for ${dateString} (${getDayName(dayOfWeek)}) for subunit ${selectedSubUnit.name}`);

            // If server has time slots for this day (and it's not a leave day), it's a working day
            if (hasTimeSlots && !serverWorkingDays.includes(dayOfWeek)) {
              serverWorkingDays.push(dayOfWeek);
              console.log(`Adding ${getDayName(dayOfWeek)} (${dayOfWeek}) to server working days for subunit ${selectedSubUnit.name}`);
            }
          } else {
            console.log(`API check for ${dateString} failed with status: ${response.status}`);
          }
        } catch (error) {
          console.error(`Error testing date ${dateString}:`, error);
        }

        // Small delay between requests to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Ensure working days are unique and sorted for consistency
      const finalWorkingDays = [...new Set(serverWorkingDays)].sort((a, b) => a - b);

      // Update working days state with server-determined days
      console.log(`Setting server-determined working days for subunit ${selectedSubUnit.name}:`, finalWorkingDays);
      setWorkingDays(finalWorkingDays);
      setIsCalendarLoading(false);
    } catch (error) {
      console.error("Error updating working days for subunit:", error);
      setIsCalendarLoading(false);
    }
  };

  const fetchTimeSlotsForDate = async (date: string) => {
    try {
      setIsTimeSlotsLoading(true);
      setCheckingQueuePositions(true);
      console.log(`Fetching time slots for date: ${date}`);

      // Use fetchRedisTimeSlots to get time slots with accurate queue counts
      const timeSlots = await fetchRedisTimeSlots(serviceId as string, date);

      if (timeSlots && timeSlots.length > 0) {
        console.log(`Successfully fetched ${timeSlots.length} time slots with queue counts from Redis`);
        setTimeSlots(timeSlots);

        // Check if user has any queue positions for this date
        await checkUserQueuePositions(date);
        return;
      }

      // If fetchRedisTimeSlots fails or returns no slots, fall back to the original implementation
      console.log("Redis time slots fetch failed or returned no slots, falling back to API");

      // Determine the API endpoint based on whether we're using subunits
      let apiUrl = `http://**************:3000/api/partner/services/${serviceId}/time-slots/${date}`;

      // If using subunits, add the subunit parameter
      if (hasSubUnits && selectedSubUnit) {
        // Find the index of the selected subunit
        const subUnitIndex = subUnits.findIndex(unit => unit.name === selectedSubUnit.name);
        if (subUnitIndex !== -1) {
          apiUrl += `?subUnitId=${subUnitIndex}`;
          console.log(`Using subunit-specific time slots API for ${selectedSubUnit.name}`);
        }
      }

      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error("Failed to fetch time slots");
      }

      const data = await response.json();
      console.log(`Time slots API response for ${date}:`, JSON.stringify(data));

      if (data.status === "success" && Array.isArray(data.timeSlots)) {
        // Convert API response to our TimeSlot interface
        const slots: TimeSlot[] = data.timeSlots.map((slot: any) => ({
          timeSlot: slot.timeSlot,
          normalQueueCount: slot.normalQueueCount || 0,
          vipQueueCount: slot.vipQueueCount || 0,
        }));

        // Filter out time slots that have already passed for today
        const filteredSlots = filterPastTimeSlots(date, slots);

        console.log(`Setting ${filteredSlots.length} time slots for ${date}`);
        setTimeSlots(filteredSlots);

        // Check if user has any queue positions for this date
        await checkUserQueuePositions(date);
      } else {
        // If no time slots from API, try to generate them from the service setup
        if (hasSubUnits && selectedSubUnit && selectedSubUnit.availableHours) {
          // Get the day of week name for the selected date
          const dayOfWeek = new Date(date).getDay();
          const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
          const dayName = dayNames[dayOfWeek];

          // Check if the subunit has available hours for this day
          if (selectedSubUnit.availableHours[dayName] && selectedSubUnit.availableHours[dayName].length > 0) {
            console.log(`Generating time slots from subunit setup for ${dayName}`);

            // Generate time slots from the subunit's available hours
            const generatedSlots: TimeSlot[] = selectedSubUnit.availableHours[dayName].map(timeRange => {
              // Format the time slot string (e.g., "9:00 AM - 10:00 AM")
              const timeSlot = `${timeRange.start} - ${timeRange.end}`;
              return {
                timeSlot,
                normalQueueCount: 0,
                vipQueueCount: 0
              };
            });

            // Filter out time slots that have already passed for today
            const filteredSlots = filterPastTimeSlots(date, generatedSlots);
            console.log(`Generated ${filteredSlots.length} time slots for ${date} from subunit setup`);
            setTimeSlots(filteredSlots);
            return;
          }
        } else if (serviceSetup && serviceSetup.availableHours) {
          // For regular services, try to generate time slots from availableHours
          const dayOfWeek = new Date(date).getDay();
          const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
          const dayName = dayNames[dayOfWeek];

          if (serviceSetup.availableHours[dayName] && serviceSetup.availableHours[dayName].length > 0) {
            console.log(`Generating time slots from service setup for ${dayName}`);

            // Generate time slots from the service's available hours
            const generatedSlots: TimeSlot[] = serviceSetup.availableHours[dayName].map(timeRange => {
              const timeSlot = `${timeRange.start} - ${timeRange.end}`;
              return {
                timeSlot,
                normalQueueCount: 0,
                vipQueueCount: 0
              };
            });

            // Filter out time slots that have already passed for today
            const filteredSlots = filterPastTimeSlots(date, generatedSlots);
            console.log(`Generated ${filteredSlots.length} time slots for ${date} from service setup`);
            setTimeSlots(filteredSlots);
            return;
          }
        }

        // If we couldn't generate time slots, set empty arrays
        setTimeSlots([]);
        setUserQueuePositions([]);
      }
    } catch (error) {
      console.error("Error fetching time slots:", error);
      setTimeSlots([]);
      setUserQueuePositions([]);
    } finally {
      setIsTimeSlotsLoading(false);
      setCheckingQueuePositions(false);
    }
  };

  // Filter time slots that have already passed for today
  const filterPastTimeSlots = (date: string, slots: TimeSlot[]): TimeSlot[] => {
    // If the date is not today, return all slots
    const today = new Date().toISOString().split('T')[0];
    if (date !== today) {
      return slots;
    }

    // If it's today, check against the end time of each slot
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    console.log(`Current time: ${currentHour}:${currentMinute}`);

    return slots.filter(slot => {
      // Log the slot we're processing
      console.log(`Processing slot: ${slot.timeSlot}`);

      // Extract the end time from the time slot (format: "HH:MM AM/PM - HH:MM AM/PM")
      const timeSlotParts = slot.timeSlot.split(' - ');

      // If the format is unexpected, keep the slot to be safe
      if (timeSlotParts.length !== 2) {
        console.log(`Unexpected format, keeping slot: ${slot.timeSlot}`);
        return true;
      }

      // Parse end time (handle AM/PM format)
      const endTime = timeSlotParts[1].trim(); // "11:00 PM"
      const endTimeParts = endTime.split(' '); // ["11:00", "PM"]

      if (endTimeParts.length !== 2) {
        console.log(`Can't parse time parts, keeping slot: ${endTime}`);
        return true;
      }

      const timeComponents = endTimeParts[0].split(':'); // ["11", "00"]
      if (timeComponents.length !== 2) {
        console.log(`Invalid time format, keeping slot: ${endTime}`);
        return true;
      }

      let endHour = parseInt(timeComponents[0], 10);
      const endMinute = parseInt(timeComponents[1], 10);
      const isPM = endTimeParts[1].toUpperCase() === 'PM';

      // Convert to 24-hour format
      if (isPM && endHour < 12) {
        endHour += 12;
      } else if (!isPM && endHour === 12) {
        endHour = 0;
      }

      console.log(`Parsed end time: ${endHour}:${endMinute} (${isPM ? 'PM' : 'AM'})`);

      // Allow booking if current time is before the end time
      if (endHour > currentHour) {
        console.log(`End hour is future, keeping slot: ${endHour} > ${currentHour}`);
        return true;
      } else if (endHour === currentHour && endMinute > currentMinute) {
        console.log(`Same hour but end minute is future, keeping slot: ${endMinute} > ${currentMinute}`);
        return true;
      }

      console.log(`Slot has ended, filtering out: ${slot.timeSlot}`);
      // Filter out slots where end time has passed
      return false;
    });
  };

  // Effect to generate marked dates whenever working days or leave days change
  useEffect(() => {
    // Ensure service setup is loaded before marking dates
    if (serviceSetup && leaveDays !== null) {
       console.log("Working days or leave days changed, regenerating marked dates...");
       generateMarkedDates();
    }
  }, [workingDays, leaveDays, serviceSetup]); // Depends on the final workingDays state

  // Update generateMarkedDates to use the state variable directly and consider subunits
  const generateMarkedDates = () => {
    if (!serviceSetup) {
      console.log("Cannot generate marked dates: serviceSetup is null");
      return;
    }

    // Determine which days are working days based on selected subunit or regular service
    let selectedDays: string[] = [];
    if (hasSubUnits && selectedSubUnit) {
      console.log(`Generating marked dates for subunit: ${selectedSubUnit.name}`);
      selectedDays = selectedSubUnit.selectedDays || [];
    } else if (serviceSetup.selectedDays) {
      console.log("Generating marked dates for regular service");
      selectedDays = serviceSetup.selectedDays;
    }

    // Map day names to day indices (0 = Sunday, 1 = Monday, etc.)
    const dayNameToIndex: Record<string, number> = {
      "Sunday": 0,
      "Monday": 1,
      "Tuesday": 2,
      "Wednesday": 3,
      "Thursday": 4,
      "Friday": 5,
      "Saturday": 6
    };

    // Convert selected days to day indices
    const selectedDayIndices = selectedDays.map(day => dayNameToIndex[day]).filter(index => index !== undefined);

    // Use either the server-determined working days or the selected days from setup
    const effectiveWorkingDays = workingDays.length > 0 ? workingDays : selectedDayIndices;
    console.log("Generating marked dates using working days:", effectiveWorkingDays);

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize today to start of day
    const todayString = today.toISOString().split("T")[0];
    const nextMonth = new Date();
    nextMonth.setDate(today.getDate() + 7); // Only show next 7 days

    const marked: MarkedDates = {};
    // Ensure leaveDays is initialized before using map
    // Create a new Set to avoid any reference to previous leave days
    const leaveDaysSet = new Set((leaveDays || []).map(d => new Date(d).toISOString().split('T')[0]));
    console.log(`Using ${leaveDaysSet.size} leave days for ${hasSubUnits && selectedSubUnit ? selectedSubUnit.name : 'regular service'} in generateMarkedDates`);

    // Disable all past dates
    const pastMonthStart = new Date(today);
    pastMonthStart.setMonth(today.getMonth() - 1); // Go back one month

    let pastDate = new Date(pastMonthStart);
    while (pastDate < today) {
      const dateString = pastDate.toISOString().split("T")[0];
      marked[dateString] = {
        disabled: true,
        disableTouchEvent: true,
      };
      pastDate.setDate(pastDate.getDate() + 1);
    }

    // Loop through the next 7 days
    let currentDate = new Date(today);
    console.log("Marking dates from", currentDate.toISOString(), "to", nextMonth.toISOString());

    while (currentDate <= nextMonth) {
      const dateString = currentDate.toISOString().split("T")[0];
      const dayOfWeek = currentDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const currentDateObj = new Date(dateString);
      currentDateObj.setHours(0, 0, 0, 0);

      // Check if this day is a working day using the STATE variable
      const isWorkingDay = workingDays.includes(dayOfWeek);
      const isLeaveDay = leaveDaysSet.has(dateString);

      console.log(`Processing date: ${dateString}, Day of week: ${dayOfWeek} (${getDayName(dayOfWeek)})`);
      console.log(`Current working days state: [${workingDays.join(', ')}], Is working day: ${isWorkingDay}, Is leave day: ${isLeaveDay}`);

      // Only process dates from today onwards
      if (currentDateObj >= today) {
        // Mark dates in the calendar
        if (dateString === todayString) {
          if (isLeaveDay) {
             console.log(`Today (${dateString}): DISABLED - Leave day`);
             marked[dateString] = { disabled: true, disableTouchEvent: true };
          } else {
             console.log(`Today (${dateString}): Marking as available`);
             marked[dateString] = { marked: true };
          }
        } else if (!isWorkingDay || isLeaveDay) {
          console.log(`${dateString}: DISABLED - Working day: ${isWorkingDay}, Leave day: ${isLeaveDay}`);
          marked[dateString] = {
            disabled: true,
            disableTouchEvent: true,
          };
        } else {
          console.log(`${dateString}: AVAILABLE - Working day: ${isWorkingDay}`);
          marked[dateString] = {
            marked: true,
          };
        }
      } else {
        marked[dateString] = {
          disabled: true,
          disableTouchEvent: true,
        };
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Disable dates beyond 7 days (logic remains the same)
    const beyondSevenDays = new Date(today);
    beyondSevenDays.setDate(today.getDate() + 8);
    const maxDate = new Date(today);
    maxDate.setMonth(today.getMonth() + 1); // Limit scrolling

    while (beyondSevenDays <= maxDate) {
      const dateString = beyondSevenDays.toISOString().split("T")[0];
      marked[dateString] = {
        disabled: true,
        disableTouchEvent: true,
      };
      beyondSevenDays.setDate(beyondSevenDays.getDate() + 1);
    }

    // Re-apply selection (logic remains the same)
    if (selectedDate && marked[selectedDate] && !marked[selectedDate].disabled) {
        marked[selectedDate] = {
          ...marked[selectedDate],
          selected: true,
          selectedColor: "#159AFF",
        };
    } else if (selectedDate) {
      // If the selected date became disabled (e.g., marked as leave), clear selection
      setSelectedDate("");
    }

    setMarkedDates(marked);
    console.log("Finished generating marked dates.");

    // Auto-select the first available date if no date is currently selected
    if (!selectedDate) {
      // Find the first available date (marked and not disabled)
      const availableDates = Object.entries(marked)
        .filter(([_, dateData]) => dateData.marked && !dateData.disabled)
        .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime());

      if (availableDates.length > 0) {
        const firstAvailableDate = availableDates[0][0];
        console.log(`Auto-selecting first available date: ${firstAvailableDate}`);

        // Update the selected date
        setSelectedDate(firstAvailableDate);

        // Mark the date as selected in the marked dates
        marked[firstAvailableDate] = {
          ...marked[firstAvailableDate],
          selected: true,
          selectedColor: "#159AFF",
        };

        // Fetch time slots for the selected date
        fetchTimeSlotsForDate(firstAvailableDate);
      } else {
        console.log("No available dates found for auto-selection");
      }
    }
  };

  const handleDateSelect = (date: { dateString: string } | DateData) => {
    const dateString = date.dateString;
    const selectedDateObj = new Date(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize today to start of day

    // Prevent selection of past dates
    if (selectedDateObj < today) {
      return;
    }

    // Check if the date is disabled
    if (markedDates[dateString]?.disabled) {
      return;
    }

    // Update selected date
    setSelectedDate(dateString);

    // Update marked dates to highlight the selected date
    const updatedMarkedDates = { ...markedDates };

    // Reset previously selected date
    Object.keys(updatedMarkedDates).forEach((key) => {
      if (updatedMarkedDates[key].selected) {
        updatedMarkedDates[key] = {
          ...updatedMarkedDates[key],
          selected: false,
        };
      }
    });

    // Mark new selected date
    updatedMarkedDates[dateString] = {
      ...updatedMarkedDates[dateString],
      selected: true,
      selectedColor: "#159AFF",
    };

    setMarkedDates(updatedMarkedDates);

    // Reset time slot selection
    setSelectedTimeSlot(null);

    // Fetch time slots for the selected date
    fetchTimeSlotsForDate(dateString);
  };

  const handleTimeSlotSelect = (timeSlot: string) => {
    setSelectedTimeSlot(timeSlot);
  };

  // Function to handle the join queue button
  const handleJoinQueue = async () => {
    if (!selectedTimeSlot) {
      Alert.alert("Error", "Please select a time slot");
      return;
    }

    if (!user?.id) {
      Alert.alert("Error", "Please sign in to join the queue");
      return;
    }

    try {
      setIsLoading(true);

      // Check if user is VIP
      const vipResponse = await fetch(
        `http://**************:3000/api/customer/profile/${user.primaryEmailAddress?.emailAddress}`
      );

      let isVIP = false;
      if (vipResponse.ok) {
        const vipData = await vipResponse.json();
        isVIP = vipData.user?.isVIP || false;
      }

      // Prepare params for enter-details screen
      const params: any = {
        serviceId: serviceId as string,
        selectedDate,
        selectedTimeSlot,
        isVIP: isVIP.toString()
      };

      // Add subunit information if applicable
      if (hasSubUnits && selectedSubUnit) {
        // Find the index of the selected subunit
        const subUnitIndex = subUnits.findIndex(unit => unit.name === selectedSubUnit.name);
        if (subUnitIndex !== -1) {
          params.subUnitId = subUnitIndex.toString();
          params.subUnitName = selectedSubUnit.name;
          params.subUnitPrice = selectedSubUnit.pricePerHead;
        }
      }

      // Navigate to enter-details screen with all parameters
      router.push({
        pathname: "/(root)/enter-details",
        params
      });

    } catch (error) {
      console.error("Error checking VIP status:", error);
      Alert.alert("Error", "An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const getDayName = (dayIndex: number): string => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[dayIndex] || 'Unknown';
  };

  if (isLoading && !serviceDetails) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#159AFF" />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <View className="bg-white pb-8">
        <View className="px-8 pt-12">
          <View className="flex-row justify-start mt-4">
            <TouchableOpacity
              className="flex-row items-center absolute left-0 -top-2 border border-secondary-600 w-12 h-12 rounded-full justify-center"
              onPress={() => router.back()}
            >
              <Image source={images.back} className="w-4 h-4" />
            </TouchableOpacity>

            <View className="flex ml-20 items-center">
              <Text className="font-poppins-medium text-xl mb-2">
                Join Queue
              </Text>
            </View>
          </View>
        </View>
      </View>

      <ScrollView className="flex-1 px-6">
        {/* Subunit selector for services with subunits */}
        {hasSubUnits && subUnits.length > 0 && (
          <View className="mb-6">
            <Text className="font-poppins-medium text-secondary-600 text-base p-4 mb-2">
              Select Service Unit
            </Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              className="pb-2"
            >
              {subUnits.map((unit, index) => (
                <TouchableOpacity
                  key={index}
                  className={`mr-4 px-6 py-3 rounded-xl ${selectedSubUnit?.name === unit.name ? 'bg-primary-500' : 'bg-gray-100'}`}
                  onPress={() => {
                    // Reset leave days state first to avoid combining with previous subunit's leave days
                    setLeaveDays([]);
                    // Reset calendar state when changing subunit
                    setIsCalendarLoading(true);
                    setIsTimeSlotsLoading(false);
                    setWorkingDays([]);
                    setSelectedDate("");
                    setSelectedTimeSlot(null);
                    setTimeSlots([]);
                    // Set the selected subunit after resetting state
                    setSelectedSubUnit(unit);
                    // Note: No need to call fetchLeaveDays() here as it will be triggered by the useEffect
                  }}
                >
                  <Text
                    className={`font-poppins-medium text-base ${selectedSubUnit?.name === unit.name ? 'text-white' : 'text-secondary-600'}`}
                  >
                    {unit.name}
                  </Text>
                  <Text
                    className={`text-xs ${selectedSubUnit?.name === unit.name ? 'text-white' : 'text-secondary-600'}`}
                  >
                    ₹{unit.pricePerHead} • {unit.avgServeTime} mins
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}

        <View className="mb-4">
          <Text className="font-poppins-medium text-secondary-600 text-base p-4 mb-4">
            Select Date
          </Text>

          <View className="mb-6">
            {isCalendarLoading ? (
              <View className="h-[150px] justify-center items-center">
                <ActivityIndicator size="large" color="#159AFF" />
                <Text className="mt-4 text-secondary-600">Loading available dates...</Text>
              </View>
            ) : (
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                className="pb-4"
              >
                {/* Filter and display only available dates */}
                {Object.entries(markedDates)
                  .filter(([_, dateData]) =>
                    // Only show dates that are marked (available) and not disabled
                    dateData.marked && !dateData.disabled
                  )
                  .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())
                  .map(([dateString, _]) => {
                    const date = new Date(dateString);
                    const dayName = getDayName(date.getDay()).substring(0, 3); // Get abbreviated day name
                    const dayNumber = date.getDate();
                    const month = date.toLocaleString('default', { month: 'short' });
                    const isToday = dateString === new Date().toISOString().split('T')[0];
                    const isSelected = dateString === selectedDate;

                    return (
                      <TouchableOpacity
                        key={dateString}
                        className={`mr-4 px-6 py-4 rounded-xl border ${
                          isSelected
                            ? 'bg-primary-500 border-primary-500'
                            : isToday
                            ? 'bg-white border-primary-500'
                            : 'bg-white border-gray-200'
                        }`}
                        onPress={() => handleDateSelect({ dateString, day: dayNumber, month: date.getMonth(), year: date.getFullYear() })}
                      >
                        <View className="items-center justify-center">
                          <Text className={`font-poppins-medium text-base ${isSelected ? 'text-white' : 'text-secondary-600'}`}>
                            {dayName}
                          </Text>
                          <Text className={`text-2xl font-poppins-medium mt-1 ${isSelected ? 'text-white' : 'text-secondary-600'}`}>
                            {dayNumber}
                          </Text>
                          <Text className={`text-xs mt-1 ${isSelected ? 'text-white' : 'text-secondary-400'}`}>
                            {month}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    );
                  })}

                {/* Show message if no available dates */}
                {Object.entries(markedDates).filter(([_, dateData]) => dateData.marked && !dateData.disabled).length === 0 && (
                  <View className="justify-center items-center px-4 py-6">
                    <Text className="text-secondary-600 text-center">
                      No available dates in the next 7 days.
                    </Text>
                    <Text className="text-secondary-400 text-center mt-2">
                      Please check back later or contact the service provider.
                    </Text>
                  </View>
                )}
              </ScrollView>
            )}
          </View>

          <View className="mb-4 px-4">
            <Text className="text-secondary-300 text-sm">
              <Text className="font-poppins-medium">Note:</Text> Only available dates for the next 7 days are shown.
            </Text>
          </View>
        </View>

        {selectedDate && (
          <View className="mb-6">
            <Text className="font-poppins-medium text-secondary-600 text-base p-4 mb-2">
              Select Time Slot
            </Text>

            {isTimeSlotsLoading ? (
              <View className="justify-center items-center py-8">
                <ActivityIndicator size="large" color="#159AFF" />
                <Text className="mt-4 text-secondary-600">Loading available time slots...</Text>
              </View>
            ) : timeSlots.length === 0 ? (
              <Text className="text-secondary-600">
                No time slots available for this date
              </Text>
            ) : (
              <View className="flex-col flex-wrap">
                {timeSlots.map((slot, index) => {
                  // Check if user is already in queue for this time slot
                  const userPosition = userQueuePositions.find(p => p.timeSlot === slot.timeSlot);
                  const isInQueue = !!userPosition;

                  return (
                  <TouchableOpacity
                    key={index}
                    className={`w-full flex-row mr-3 mb-6 px-4 py-4  rounded-3xl ${
                      isInQueue
                        ? "bg-green-100 "
                        : selectedTimeSlot === slot.timeSlot
                        ? "bg-primary-500/20 border border-primary-500"
                        : "bg-white border border-primary-200"
                    }`}
                    onPress={() => !isInQueue && handleTimeSlotSelect(slot.timeSlot)}
                    disabled={isInQueue}
                  >
                    <View className="flex-row justify-between items-center w-full">
                      <Text
                        className={`font-poppins-medium text-base ml-2 text-center ${
                          isInQueue
                            ? "text-green-700"
                            : selectedTimeSlot === slot.timeSlot
                            ? "text-secondary-500"
                            : "text-secondary-600"
                        }`}
                      >
                        {slot.timeSlot}
                      </Text>

                      <View className={`flex-row ${isInQueue ? "bg-green-50" : "bg-white"} px-4 py-2 rounded-xl justify-center items-center`}>
                        {isInQueue ? (
                          // Show position if user is already in queue
                          <View className="flex-row items-center">
                            <Text className="text-green-700 font-poppins-medium text-base">
                              Your Position: {userPosition.position}
                            </Text>
                            {userPosition.isVIP && (
                              <View className="ml-2 bg-warning-500 rounded-lg px-2 py-1">
                                <Text className="text-white text-xs font-poppins-medium">VIP</Text>
                              </View>
                            )}
                          </View>
                        ) : (
                          // Show regular queue counts
                          <>
                            <View className="flex-row items-center mr-3">
                              <Image
                                source={images.run}
                                className="w-4 h-5 mr-2"
                                style={{ tintColor: "#159AFF" }}
                              />
                              <Text
                                className={`text-base font-poppins-medium ${
                                  selectedTimeSlot === slot.timeSlot
                                    ? "text-secondary-600"
                                    : "text-secondary-600"
                                }`}
                              >
                                {slot.normalQueueCount}
                              </Text>
                            </View>
                            <Text className="text-lg font-poppins-medium text-secondary-600 mr-3">
                              |
                            </Text>
                            <View className="flex-row items-center">
                              <Image
                                source={images.run}
                                className="w-4 h-5 mr-2"
                                style={{ tintColor: "#FFB800" }}
                              />
                              <Text
                                className={`text-base font-poppins-medium ${
                                  selectedTimeSlot === slot.timeSlot
                                    ? "text-secondary-600"
                                    : "text-secondary-600"
                                }`}
                              >
                                {slot.vipQueueCount}
                              </Text>
                            </View>
                          </>
                        )}
                      </View>
                    </View>
                  </TouchableOpacity>
                  );
                })}
              </View>
            )}
          </View>
        )}
      </ScrollView>

      <View className="p-6 border-t flex justify-center items-center border-gray-200">
        <TouchableOpacity
          className={`w-[390px] py-6 rounded-2xl items-center ${
            selectedDate && selectedTimeSlot ? "bg-primary-500" : "bg-gray-300"
          }`}
          onPress={handleJoinQueue}
          disabled={!selectedDate || !selectedTimeSlot || isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Text className="text-white font-poppins-medium text-lg">
              Next
            </Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default JoinQueueScreen;
