"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonServicesModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const queue_entity_1 = require("../partner/entities/queue.entity");
const service_setup_entity_1 = require("../partner/entities/service-setup.entity");
const schedule_1 = require("@nestjs/schedule");
const redis_module_1 = require("./redis/redis.module");
const queue_flow_service_1 = require("./queue-flow/queue-flow.service");
const scheduler_service_1 = require("./scheduler/scheduler.service");
const redis_service_1 = require("./redis/redis.service");
let CommonServicesModule = class CommonServicesModule {
};
exports.CommonServicesModule = CommonServicesModule;
exports.CommonServicesModule = CommonServicesModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [
            schedule_1.ScheduleModule.forRoot(),
            typeorm_1.TypeOrmModule.forFeature([queue_entity_1.Queue, service_setup_entity_1.ServiceSetup]),
            redis_module_1.RedisModule,
        ],
        providers: [
            queue_flow_service_1.QueueFlowService,
            {
                provide: scheduler_service_1.SchedulerService,
                useFactory: (queueFlowService, redisService) => {
                    const stubCustomerService = {
                        updateExpiredQueues: async () => {
                            return { updated: 0, message: "Stub implementation - real method will be available at runtime" };
                        }
                    };
                    return new scheduler_service_1.SchedulerService(stubCustomerService, queueFlowService, redisService);
                },
                inject: [queue_flow_service_1.QueueFlowService, redis_service_1.RedisService],
            },
        ],
        exports: [
            queue_flow_service_1.QueueFlowService,
            scheduler_service_1.SchedulerService,
        ],
    })
], CommonServicesModule);
//# sourceMappingURL=common-services.module.js.map