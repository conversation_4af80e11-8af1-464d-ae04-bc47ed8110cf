import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToOne, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ServiceSetup } from './service-setup.entity';
import { BankDetails } from './bank-details.entity';
import { Queue } from './queue.entity';
import { Review } from './review.entity';

@Entity('services')
export class Service {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  serviceName: string;

  @Column({ unique: true })
  email: string;

  @Column({ nullable: true })
  serviceType: string;

  @Column({ nullable: true })
  businessPhone: string;

  @Column('text', { nullable: true })
  serviceDescription: string;

  @Column('json', { nullable: true })
  address: {
    details: {
      buildingNo: string;
      locality: string;
      city: string;
      state: string;
      pincode: string;
    };
    coordinates: {
      latitude: number;
      longitude: number;
    };
    area?: string;
    fullAddress?: string;
    googleMapsLink?: string;
  };

  @Column('text', { array: true, nullable: true })
  images: string[];

  @Column('json', { nullable: true })
  documents: {
    panNumber: string;
    gstin?: string;
    panCardImage: string;
  };

  @Column({
    type: 'enum',
    enum: ['pending', 'success', 'failed'],
    default: 'pending'
  })
  verificationStatus: string;

  @Column({ default: false })
  termsAccepted: boolean;

  @Column({ default: false })
  isOpen: boolean;

  @Column({ type: 'float', default: 0 })
  rating: number;

  @Column({ type: 'int', default: 0 })
  reviewCount: number;

  @OneToMany(() => Review, review => review.service)
  reviews: Review[];

  @OneToOne(() => ServiceSetup, setup => setup.service)
  setup: ServiceSetup;

  @OneToOne(() => BankDetails, bankDetails => bankDetails.service)
  bankDetails: BankDetails;

  @OneToMany(() => Queue, queue => queue.service)
  queues: Queue[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
