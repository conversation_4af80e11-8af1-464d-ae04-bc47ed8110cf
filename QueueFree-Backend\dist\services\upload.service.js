"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadService = void 0;
const common_1 = require("@nestjs/common");
const firebase_config_1 = require("../config/firebase.config");
let UploadService = class UploadService {
    async uploadImage(imageData, path) {
        try {
            console.log('Starting upload for path:', path);
            console.log('Image data type:', typeof imageData);
            let base64Data;
            if (typeof imageData === 'object') {
                if (!imageData.base64) {
                    throw new Error('No base64 data found in image object');
                }
                base64Data = imageData.base64;
            }
            else if (typeof imageData === 'string') {
                base64Data = imageData;
            }
            else {
                throw new Error(`Invalid image data type: ${typeof imageData}`);
            }
            if (!base64Data || typeof base64Data !== 'string') {
                throw new Error('Invalid base64 string');
            }
            const base64String = base64Data.replace(/^data:image\/\w+;base64,/, '');
            const buffer = Buffer.from(base64String, 'base64');
            const file = firebase_config_1.storage.bucket().file(path);
            await file.save(buffer, {
                metadata: {
                    contentType: 'image/jpeg'
                }
            });
            const [url] = await file.getSignedUrl({
                action: 'read',
                expires: '03-01-2500'
            });
            console.log('Upload successful, URL:', url);
            return url;
        }
        catch (error) {
            console.error('Error details:', error);
            throw new common_1.BadRequestException(`Failed to upload image: ${error.message}`);
        }
    }
    async uploadMultipleImages(images) {
        try {
            console.log(`Attempting to upload ${images.length} images`);
            const uploadPromises = images.map((image, index) => {
                const path = `services/images/${Date.now()}-${index}.jpg`;
                return this.uploadImage(image, path);
            });
            return await Promise.all(uploadPromises);
        }
        catch (error) {
            console.error('Error uploading multiple images:', error);
            throw new common_1.BadRequestException('Failed to upload images');
        }
    }
};
exports.UploadService = UploadService;
exports.UploadService = UploadService = __decorate([
    (0, common_1.Injectable)()
], UploadService);
//# sourceMappingURL=upload.service.js.map