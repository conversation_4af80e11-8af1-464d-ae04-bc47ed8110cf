0000000000000000000000000000000000000000 5c04fe295a5647197c87b3e9d8514e1eccec2aae selfiejones <<EMAIL>> 1740601092 +0530	commit (initial): seperate authentication for partner
5c04fe295a5647197c87b3e9d8514e1eccec2aae 18f8efa0e548eaf7b8352a79871e17aadf0eaaab selfiejones <<EMAIL>> 1740687001 +0530	commit: added some animation
18f8efa0e548eaf7b8352a79871e17aadf0eaaab 484ca4f7c8ab46d457327fc589d2412fb53706d8 selfiejones <<EMAIL>> 1741290133 +0530	commit: added service verification and setup flow
484ca4f7c8ab46d457327fc589d2412fb53706d8 fb7d826f47bc9fa75bf09abe74825f1b3f0c6cc6 selfiejones <<EMAIL>> 1741360776 +0530	commit: updated to gorhan bottom sheet and minor fixes
fb7d826f47bc9fa75bf09abe74825f1b3f0c6cc6 e6419da501bad0af46e7e985a16ef465300aba03 selfiejones <<EMAIL>> 1741447410 +0530	commit: updated service deatils to db
e6419da501bad0af46e7e985a16ef465300aba03 2327482f9fc36153ec48af8f706e2119c2fd6184 selfiejones <<EMAIL>> 1742037273 +0530	commit: added setup saving flow
2327482f9fc36153ec48af8f706e2119c2fd6184 2a3fcbaecc0a3653c7e92018d7c277d1e7dc2892 selfiejones <<EMAIL>> ********** +0530	commit: updated edit service and setup flow
2a3fcbaecc0a3653c7e92018d7c277d1e7dc2892 a95f58da8a6acc831b825756c5c7983141d472a5 selfiejones <<EMAIL>> ********** +0530	commit: service open close toggle
a95f58da8a6acc831b825756c5c7983141d472a5 52be1b85459740b6c835df5358ad02983465556f selfiejones <<EMAIL>> ********** +0530	commit: fix service setup
52be1b85459740b6c835df5358ad02983465556f be300efbc44e3d2129794a9c66027351e6ffde9d selfiejones <<EMAIL>> ********** +0530	commit: bank details and leave days updated
be300efbc44e3d2129794a9c66027351e6ffde9d d3de22a2cc425d98e871ece08edee903cf6c7c9f selfiejones <<EMAIL>> ********** +0530	commit: updated gmap link field
d3de22a2cc425d98e871ece08edee903cf6c7c9f 42f3820b9c06dc288e966fd4019db926eea46386 selfiejones <<EMAIL>> ********** +0530	commit: revert
42f3820b9c06dc288e966fd4019db926eea46386 27b1c5a9dd1423ded4cb3e8df9c817fa8aa6793c selfiejones <<EMAIL>> ********** +0530	commit: revert
27b1c5a9dd1423ded4cb3e8df9c817fa8aa6793c 261c87660850ede6aa884673a3f55295f31a80c9 selfiejones <<EMAIL>> ********** +0530	commit: updated view queue
261c87660850ede6aa884673a3f55295f31a80c9 64b38d6568400f9160e11ecf5013181b32dfb949 selfiejones <<EMAIL>> ********** +0530	commit: updated sheduleded queue expiring
64b38d6568400f9160e11ecf5013181b32dfb949 5db9a510608dd61e6a87e48743d7b159506ba998 selfiejones <<EMAIL>> ********** +0530	commit: updated timing update in view queue
5db9a510608dd61e6a87e48743d7b159506ba998 0ec076c0da2372c819a47748603385bc0605b050 selfiejones <<EMAIL>> 1744827955 +0530	commit: updated timing updates
0ec076c0da2372c819a47748603385bc0605b050 2cf6ac3c431d4a492a6eaeed0c0ca78120a24303 selfiejones <<EMAIL>> 1744980277 +0530	commit: updated queue status flow
2cf6ac3c431d4a492a6eaeed0c0ca78120a24303 4e9759e839ecc37cfaadb84da3c471571a176fa0 selfiejones <<EMAIL>> 1744982882 +0530	commit: minor fixes
4e9759e839ecc37cfaadb84da3c471571a176fa0 180e4bb5af74479778d07aad11416e7155d5c314 selfiejones <<EMAIL>> 1745697369 +0530	commit: major update - subunits
