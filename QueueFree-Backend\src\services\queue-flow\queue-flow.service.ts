import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, <PERSON><PERSON>han, <PERSON><PERSON>han, Between } from 'typeorm';
import { Queue } from '../../partner/entities/queue.entity';
import { ServiceSetup } from '../../partner/entities/service-setup.entity';
import { RedisService } from '../redis/redis.service';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class QueueFlowService {
  private readonly logger = new Logger(QueueFlowService.name);

  constructor(
    @InjectRepository(Queue)
    private queueRepository: Repository<Queue>,
    @InjectRepository(ServiceSetup)
    private serviceSetupRepository: Repository<ServiceSetup>,
    private redisService: RedisService,
  ) {}

  /**
   * Start grace period for a queue
   * @param queueId Queue ID to start grace period for
   * @param serviceId Service ID associated with the queue
   * @returns Updated queue object
   */
  async startGracePeriod(queueId: number, serviceId: number): Promise<Queue> {
    this.logger.log(`Starting grace period for queue ${queueId}`);

    // Get the service setup to determine grace time
    const serviceSetup = await this.serviceSetupRepository.findOne({
      where: { service: { id: serviceId } }
    });

    // Default to 2 minutes (120 seconds) if not specified
    const graceTime = serviceSetup?.graceTime || 120;

    // Get the queue
    const queue = await this.queueRepository.findOne({
      where: { id: queueId }
    });

    if (!queue) {
      throw new Error(`Queue ${queueId} not found`);
    }

    // Update queue with grace period info
    queue.graceStartedAt = new Date();
    queue.inGracePeriod = true;
    queue.confirmedPresence = false; // Reset in case this is restarted

    // Save to database
    const updatedQueue = await this.queueRepository.save(queue);

    // Update Redis
    await this.updateRedisWithGracePeriod(updatedQueue, graceTime);

    return updatedQueue;
  }

  /**
   * Handle customer presence confirmation during grace period
   * @param queueId Queue ID to confirm presence for
   * @param isPresent Whether customer confirmed presence
   * @returns Updated queue object
   */
  async handlePresenceConfirmation(queueId: number, isPresent: boolean): Promise<Queue> {
    this.logger.log(`Handling presence confirmation for queue ${queueId}: ${isPresent}`);

    // Get the queue
    const queue = await this.queueRepository.findOne({
      where: { id: queueId },
      relations: ['service']
    });

    if (!queue) {
      throw new Error(`Queue ${queueId} not found`);
    }

    // If grace period already ended or wasn't started, return queue as is
    if (!queue.inGracePeriod || !queue.graceStartedAt) {
      return queue;
    }

    if (isPresent) {
      // Customer confirmed presence
      queue.confirmedPresence = true;
      queue.isCheckedIn = true;
      queue.inGracePeriod = false; // Exit grace period

      // Save to database
      const updatedQueue = await this.queueRepository.save(queue);

      // Update Redis
      await this.redisService.saveQueue(queueId.toString(), {
        ...await this.redisService.getQueue(queueId.toString()),
        confirmedPresence: true,
        isCheckedIn: true,
        inGracePeriod: false
      });

      return updatedQueue;
    } else {
      // Customer confirmed NOT present (no-show)

      // Get all waiting queues for this service, date, and time slot for reordering
      const allWaitingQueues = await this.queueRepository.find({
        where: {
          serviceId: queue.serviceId,
          date: queue.date,
          timeSlot: queue.timeSlot,
          status: 'waiting',
        },
        order: {
          position: 'ASC',
          createdAt: 'ASC'
        }
      });

      this.logger.log(`Found ${allWaitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);

      // Update queue status
      queue.confirmedPresence = false;
      queue.inGracePeriod = false;
      queue.status = 'no-show';
      queue.statusUpdatedAt = new Date();
      // Set position to -1 to indicate it's no longer in the queue
      queue.position = -1;

      // Save to database
      const updatedQueue = await this.queueRepository.save(queue);

      // Update Redis
      // Get the current queue data from Redis
      const queueData = await this.redisService.getQueue(queueId.toString()) || {};

      // Update the status and position
      queueData.status = 'no-show';
      queueData.position = -1;

      // Save the updated queue data to Redis
      await this.redisService.saveQueue(queueId.toString(), queueData);

      // Also update the status using the dedicated method
      await this.redisService.updateQueueStatus(queueId.toString(), 'no-show');

      // Reorder positions for all remaining waiting queues
      // Filter out the no-show queue
      const remainingQueues = allWaitingQueues.filter(q => q.id !== queueId);

      // Reassign positions to all remaining queues
      const positions: Record<string, number> = {};
      let position = 0;

      for (const q of remainingQueues) {
        position++;
        q.position = position;
        positions[q.id] = position;

        // Update Redis for this queue
        const qData = await this.redisService.getQueue(q.id.toString()) || {};
        qData.position = position;
        await this.redisService.saveQueue(q.id.toString(), qData);
      }

      this.logger.log(`Reordered positions for ${remainingQueues.length} queues after marking queue ${queueId} as no-show`);

      // Save all queues to database
      if (remainingQueues.length > 0) {
        await this.queueRepository.save(remainingQueues);
      }

      // Save all positions to Redis
      const dateStr = new Date(queue.date).toISOString().split('T')[0];
      await this.redisService.saveQueuePosition(
        queue.serviceId.toString(),
        dateStr,
        queue.timeSlot,
        positions
      );

      // Invalidate any cached queue positions
      await this.redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);

      // Also invalidate any cached data for this service
      await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
      await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');

      return updatedQueue;
    }
  }

  /**
   * Start serving a customer
   * @param queueId Queue ID to start serving
   * @returns Updated queue object
   */
  async startServing(queueId: number): Promise<Queue> {
    this.logger.log(`Starting service for queue ${queueId}`);

    // Get the queue
    const queue = await this.queueRepository.findOne({
      where: { id: queueId },
      relations: ['service']
    });

    if (!queue) {
      throw new Error(`Queue ${queueId} not found`);
    }

    // Update queue status
    queue.status = 'serving';
    queue.currentlyServing = true;
    queue.servingStartedAt = new Date(); // Set current timestamp
    queue.statusUpdatedAt = new Date();

    // Ensure checked in
    queue.isCheckedIn = true;

    // Exit grace period if it was active
    if (queue.inGracePeriod) {
      queue.inGracePeriod = false;
      queue.confirmedPresence = true;
    }

    // Save to database
    const updatedQueue = await this.queueRepository.save(queue);

    // Get service details for serving time
    let servingTime = 15; // Default 15 minutes
    if (queue.service) {
      const serviceSetup = await this.serviceSetupRepository.findOne({
        where: { service: { id: queue.serviceId } }
      });

      if (serviceSetup?.setupData?.servingTime) {
        servingTime = parseInt(serviceSetup.setupData.servingTime, 10);
        this.logger.log(`Using service configuration serving time: ${servingTime} minutes`);
      } else {
        this.logger.log(`No serving time configured for service ${queue.serviceId}, using default: ${servingTime} minutes`);
      }
    }

    // Calculate estimated completion time
    const servingEndTime = new Date(queue.servingStartedAt);
    servingEndTime.setMinutes(servingEndTime.getMinutes() + servingTime);

    this.logger.log(`Queue ${queueId} serving started at ${queue.servingStartedAt.toISOString()}`);
    this.logger.log(`Estimated completion time: ${servingEndTime.toISOString()} (${servingTime} minutes later)`);

    // Set a much longer TTL for serving status in Redis to prevent premature expiration
    // Use 24 hours (86400 seconds) for serving queues to ensure they remain visible for a full day
    const redisTTL = 86400; // 24 hours in seconds

    this.logger.log(`Setting Redis TTL for queue ${queueId} to ${redisTTL} seconds (24 hours fixed TTL for serving status)`);

    // Try to get existing Redis data to merge with
    let existingData = {};
    try {
      existingData = await this.redisService.getQueue(queueId.toString()) || {};
    } catch (error) {
      this.logger.error(`Error getting existing Redis data for queue ${queueId}:`, error);
    }

    // Merge with existing data
    const updatedRedisData = {
      ...existingData,
      id: queue.id,
      serviceId: queue.serviceId,
      status: 'serving',
      currentlyServing: true,
      servingStartedAt: queue.servingStartedAt.toISOString(),
      estimatedEndTime: servingEndTime.toISOString(),
      servingTime,
      servingTimeMinutes: servingTime, // Add this explicit field for client clarity
      remainingMinutes: servingTime, // Initialize remaining time
      remainingSeconds: servingTime * 60, // Convert to seconds for fine-grained countdown
      statusUpdatedAt: new Date().toISOString(),
      isCheckedIn: true
    };

    // Save to Redis with the longer TTL
    await this.redisService.set(`queue:${queueId}`, updatedRedisData, { ex: redisTTL });

    // Also explicitly update status with a priority flag to ensure it takes precedence
    await this.redisService.updateQueueStatus(queueId.toString(), 'serving', true);

    // Invalidate service queues cache
    const date = new Date(queue.date).toISOString().split('T')[0];
    await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), date);
    await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');

    return updatedQueue;
  }

  /**
   * Complete service for a customer
   * @param queueId Queue ID to complete
   * @returns Updated queue object
   */
  async completeService(queueId: number): Promise<Queue> {
    this.logger.log(`Completing service for queue ${queueId}`);

    // Get the queue
    const queue = await this.queueRepository.findOne({
      where: { id: queueId },
      relations: ['service']
    });

    if (!queue) {
      throw new Error(`Queue ${queueId} not found`);
    }

    // Get all waiting queues for this service, date, and time slot
    const allWaitingQueues = await this.queueRepository.find({
      where: {
        serviceId: queue.serviceId,
        date: queue.date,
        timeSlot: queue.timeSlot,
        status: 'waiting',
      },
      order: {
        position: 'ASC',
        createdAt: 'ASC'
      }
    });

    this.logger.log(`Found ${allWaitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);

    // Update queue status
    queue.status = 'completed';
    queue.currentlyServing = false;
    queue.statusUpdatedAt = new Date();
    // Set position to -1 to indicate it's no longer in the queue
    queue.position = -1;

    // Save to database
    const updatedQueue = await this.queueRepository.save(queue);

    // Update Redis
    // Get the current queue data from Redis
    const queueData = await this.redisService.getQueue(queueId.toString()) || {};

    // Update the status and position
    queueData.status = 'completed';
    queueData.position = -1;

    // Save the updated queue data to Redis
    await this.redisService.saveQueue(queueId.toString(), queueData);

    // Also update the status using the dedicated method
    await this.redisService.updateQueueStatus(queueId.toString(), 'completed');

    // Reorder positions for all remaining waiting queues
    // Filter out the completed queue
    const remainingQueues = allWaitingQueues.filter(q => q.id !== queueId);

    // Reassign positions to all remaining queues
    const positions: Record<string, number> = {};
    let position = 0;

    for (const q of remainingQueues) {
      position++;
      q.position = position;
      positions[q.id] = position;

      // Update Redis for this queue
      const qData = await this.redisService.getQueue(q.id.toString()) || {};
      qData.position = position;
      await this.redisService.saveQueue(q.id.toString(), qData);
    }

    this.logger.log(`Reordered positions for ${remainingQueues.length} queues after completing queue ${queueId}`);

    // Save all queues to database
    if (remainingQueues.length > 0) {
      await this.queueRepository.save(remainingQueues);
    }

    // Save all positions to Redis
    const dateStr = new Date(queue.date).toISOString().split('T')[0];
    await this.redisService.saveQueuePosition(
      queue.serviceId.toString(),
      dateStr,
      queue.timeSlot,
      positions
    );

    // Invalidate service queues cache
    await this.redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);
    await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
    await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');

    return updatedQueue;
  }

  /**
   * Mark a queue as no-show after grace period expiration
   * @param queueId Queue ID to mark as no-show
   * @returns Updated queue object
   */
  async markAsNoShow(queueId: number): Promise<Queue> {
    this.logger.log(`Marking queue ${queueId} as no-show`);

    // Get the queue
    const queue = await this.queueRepository.findOne({
      where: { id: queueId },
      relations: ['service']
    });

    if (!queue) {
      throw new Error(`Queue ${queueId} not found`);
    }

    // Get all waiting queues for this service, date, and time slot
    const allWaitingQueues = await this.queueRepository.find({
      where: {
        serviceId: queue.serviceId,
        date: queue.date,
        timeSlot: queue.timeSlot,
        status: 'waiting',
      },
      order: {
        position: 'ASC',
        createdAt: 'ASC'
      }
    });

    this.logger.log(`Found ${allWaitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);

    // Update queue status
    queue.status = 'no-show';
    queue.inGracePeriod = false;
    queue.currentlyServing = false;
    queue.statusUpdatedAt = new Date();
    // Set position to -1 to indicate it's no longer in the queue
    queue.position = -1;

    // Save to database
    const updatedQueue = await this.queueRepository.save(queue);

    // Update Redis
    // Get the current queue data from Redis
    const queueData = await this.redisService.getQueue(queueId.toString()) || {};

    // Update the status and position
    queueData.status = 'no-show';
    queueData.position = -1;

    // Save the updated queue data to Redis
    await this.redisService.saveQueue(queueId.toString(), queueData);

    // Also update the status using the dedicated method
    await this.redisService.updateQueueStatus(queueId.toString(), 'no-show');

    // Reorder positions for all remaining waiting queues
    // Filter out the no-show queue
    const remainingQueues = allWaitingQueues.filter(q => q.id !== queueId);

    // Reassign positions to all remaining queues
    const positions: Record<string, number> = {};
    let position = 0;

    for (const q of remainingQueues) {
      position++;
      q.position = position;
      positions[q.id] = position;

      // Update Redis for this queue
      const qData = await this.redisService.getQueue(q.id.toString()) || {};
      qData.position = position;
      await this.redisService.saveQueue(q.id.toString(), qData);
    }

    this.logger.log(`Reordered positions for ${remainingQueues.length} queues after marking queue ${queueId} as no-show`);

    // Save all queues to database
    if (remainingQueues.length > 0) {
      await this.queueRepository.save(remainingQueues);
    }

    // Save all positions to Redis
    const dateStr = new Date(queue.date).toISOString().split('T')[0];
    await this.redisService.saveQueuePosition(
      queue.serviceId.toString(),
      dateStr,
      queue.timeSlot,
      positions
    );

    // Invalidate service queues cache
    await this.redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);
    await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
    await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');

    return updatedQueue;
  }

  /**
   * Update Redis with grace period information
   */
  private async updateRedisWithGracePeriod(queue: Queue, graceTime: number): Promise<void> {
    // Ensure graceStartedAt is not null
    if (!queue.graceStartedAt) {
      this.logger.error(`Queue ${queue.id} has no grace start time`);
      return;
    }

    // Calculate when grace period ends
    const graceEndTime = new Date(queue.graceStartedAt as Date);
    graceEndTime.setSeconds(graceEndTime.getSeconds() + graceTime);

    // Get existing Redis data or create new object
    const redisKey = `queue:${queue.id}`;
    let queueData = await this.redisService.get(redisKey) || {};

    // Update with grace period info
    queueData = {
      ...queueData,
      id: queue.id,
      inGracePeriod: true,
      graceStartedAt: queue.graceStartedAt.toISOString(),
      graceEndTime: graceEndTime.toISOString(),
      graceTimeSeconds: graceTime,
      confirmedPresence: false,
      serviceId: queue.serviceId,
      status: queue.status
    };

    // Save to Redis
    await this.redisService.set(redisKey, queueData, { ex: Math.max(graceTime * 2, 300) });
  }

  /**
   * Scheduled job to check and expire grace periods
   * Runs every 30 seconds to check queues in grace period
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  async checkGracePeriods() {
    this.logger.debug('Checking for expired grace periods');

    try {
      // Find all queues currently in grace period
      const queuesInGracePeriod = await this.queueRepository.find({
        where: { inGracePeriod: true },
        relations: ['service']
      });

      if (queuesInGracePeriod.length === 0) {
        return;
      }

      this.logger.debug(`Found ${queuesInGracePeriod.length} queues in grace period`);

      const now = new Date();

      // Process each queue
      for (const queue of queuesInGracePeriod) {
        try {
          // Skip if no grace start time
          if (!queue.graceStartedAt) {
            continue;
          }

          // Get the service setup to determine grace time
          const serviceSetup = await this.serviceSetupRepository.findOne({
            where: { service: { id: queue.serviceId } }
          });

          // Default to 2 minutes if not specified
          const graceTime = serviceSetup?.graceTime || 120;

          // Calculate when grace period ends
          const graceEndTime = new Date(queue.graceStartedAt as Date);
          graceEndTime.setSeconds(graceEndTime.getSeconds() + graceTime);

          // Check if grace period has expired
          if (now > graceEndTime) {
            // If customer hasn't confirmed presence, mark as no-show
            if (!queue.confirmedPresence) {
              this.logger.log(`Grace period expired for queue ${queue.id} - marking as no-show`);
              await this.markAsNoShow(queue.id);
            } else {
              // Customer confirmed presence but grace period ended
              this.logger.log(`Grace period expired for queue ${queue.id} but customer confirmed presence`);

              // Just end the grace period
              queue.inGracePeriod = false;
              await this.queueRepository.save(queue);

              // Update Redis
              await this.redisService.saveQueue(queue.id.toString(), {
                ...await this.redisService.getQueue(queue.id.toString()),
                inGracePeriod: false
              });
            }
          }
        } catch (error) {
          this.logger.error(`Error processing grace period for queue ${queue.id}:`, error);
        }
      }
    } catch (error) {
      this.logger.error('Error checking grace periods:', error);
    }
  }

  /**
   * Automatically check and start grace periods for members in first position who haven't checked in
   * This runs on a schedule to identify queues that need grace periods started automatically
   *
   * This functionality has been removed to ensure grace periods are only started manually
   * from the partner app.
   */
  async autoStartGracePeriods() {
    this.logger.log('Auto grace period starting has been disabled - grace periods are only started manually');
    return { disabled: true, message: 'Auto grace periods disabled' };
  }

  /**
   * Helper method to check if a time slot is currently active
   * This method is no longer used since auto grace period starting has been disabled.
   * Kept for reference only.
   *
   * @deprecated This method is no longer used and will be removed in a future version.
   */
  // private isTimeSlotActive(timeSlot: string): boolean {
  //   this.logger.debug('isTimeSlotActive method is deprecated and no longer used');
  //   return false;
  // }

  /**
   * Check a single queue's grace period and mark as no-show if expired
   * This is used when the client detects a grace period expiration
   * @param queueId The queue ID to check
   */
  async checkSingleGracePeriod(queueId: number): Promise<void> {
    this.logger.debug(`Checking grace period for queue ${queueId}`);

    try {
      // Get the queue with service relation
      const queue = await this.queueRepository.findOne({
        where: { id: queueId, inGracePeriod: true },
        relations: ['service']
      });

      if (!queue) {
        this.logger.debug(`Queue ${queueId} not found or not in grace period`);
        return;
      }

      // Skip if no grace start time
      if (!queue.graceStartedAt) {
        this.logger.debug(`Queue ${queueId} has no grace start time`);
        return;
      }

      // Get the service setup to determine grace time
      const serviceSetup = await this.serviceSetupRepository.findOne({
        where: { service: { id: queue.serviceId } }
      });

      // Default to 2 minutes if not specified
      const graceTime = serviceSetup?.graceTime || 120;

      // Calculate when grace period ends
      const graceEndTime = new Date(queue.graceStartedAt as Date);
      graceEndTime.setSeconds(graceEndTime.getSeconds() + graceTime);

      const now = new Date();

      // Check if grace period has expired
      if (now > graceEndTime) {
        // If customer hasn't confirmed presence, mark as no-show immediately
        if (!queue.confirmedPresence) {
          this.logger.log(`Grace period expired for queue ${queue.id} - marking as no-show immediately`);
          await this.markAsNoShow(queue.id);
        } else {
          // Customer confirmed presence but grace period ended
          this.logger.log(`Grace period expired for queue ${queue.id} but customer confirmed presence`);

          // Just end the grace period
          queue.inGracePeriod = false;
          await this.queueRepository.save(queue);

          // Update Redis
          await this.redisService.saveQueue(queue.id.toString(), {
            ...await this.redisService.getQueue(queue.id.toString()),
            inGracePeriod: false
          });
        }
      } else {
        this.logger.debug(`Grace period for queue ${queueId} has not expired yet`);
      }
    } catch (error) {
      this.logger.error(`Error checking grace period for queue ${queueId}:`, error);
      throw error;
    }
  }

  /**
   * Calculate estimated serve time for a queue
   * @param queueId Queue ID to calculate for
   * @returns Updated queue object with estimated serve time
   */
  async calculateEstimatedServeTime(queueId: number): Promise<Queue> {
    this.logger.log(`Calculating estimated serve time for queue ${queueId}`);

    // Get the queue with service relation
    const queue = await this.queueRepository.findOne({
      where: { id: queueId },
      relations: ['service']
    });

    if (!queue) {
      throw new Error(`Queue ${queueId} not found`);
    }

    // Get service setup to determine serve time
    const serviceSetup = await this.serviceSetupRepository.findOne({
      where: { service: { id: queue.serviceId } }
    });

    // Default to 15 minutes if not specified
    let serveTime = 15;

    // Check if this is a service with subunits
    if (serviceSetup?.setupData?.hasSubUnits && Array.isArray(serviceSetup.setupData.subUnits) && serviceSetup.setupData.subUnits.length > 0) {
      // Get the subunit ID from the queue
      const subUnitId = queue.subUnitId !== undefined ? parseInt(queue.subUnitId, 10) : 0;

      // Make sure the subunit ID is valid
      const subUnitIndex = isNaN(subUnitId) ? 0 : Math.min(subUnitId, serviceSetup.setupData.subUnits.length - 1);

      // Get the subunit
      const subUnit = serviceSetup.setupData.subUnits[subUnitIndex];

      // Get serve time from subunit
      if (subUnit?.avgServeTime) {
        serveTime = parseInt(subUnit.avgServeTime, 10);
        this.logger.log(`Using subunit ${subUnitIndex} (${subUnit.name}) serve time: ${serveTime} minutes`);
      } else {
        this.logger.log(`No serve time configured for subunit ${subUnitIndex}, using default: ${serveTime} minutes`);
      }
    }
    // For regular services, use the service setup serve time
    else if (serviceSetup?.setupData?.servingTime) {
      serveTime = parseInt(serviceSetup.setupData.servingTime, 10);
      this.logger.log(`Using service configuration serving time: ${serveTime} minutes`);
    } else {
      this.logger.log(`No serving time configured for service ${queue.serviceId}, using default: ${serveTime} minutes`);
    }

    // Parse time slot to get start time
    const timeSlotParts = queue.timeSlot.split(' - ');
    const timeStart = timeSlotParts[0].trim(); // e.g., "06:00 PM"

    // Parse the start time considering AM/PM format
    let startHour = 0;
    let startMinute = 0;

    if (timeStart.includes('AM') || timeStart.includes('PM')) {
      const [timeValue, period] = timeStart.split(' ');
      const [hourStr, minuteStr] = timeValue.split(':');
      let hour = parseInt(hourStr, 10);
      const minute = parseInt(minuteStr, 10);

      // Convert to 24-hour format
      if (period.toUpperCase() === 'PM' && hour < 12) {
        hour += 12;
      } else if (period.toUpperCase() === 'AM' && hour === 12) {
        hour = 0;
      }

      startHour = hour;
      startMinute = minute;
    } else {
      // Fallback to original parsing if no AM/PM
      const [hours, minutes] = timeStart.split(':').map(part => parseInt(part, 10));
      startHour = hours;
      startMinute = minutes;
    }

    // Set the time slot start time
    const timeSlotStart = new Date(queue.date);
    timeSlotStart.setHours(startHour, startMinute, 0, 0);

    // Get the current time
    const now = new Date();

    // Check if the current time is after the time slot start time
    const isServiceStarted = now > timeSlotStart;

    if (isServiceStarted) {
      this.logger.log(`Current time (${now.toISOString()}) is after time slot start time (${timeSlotStart.toISOString()}). Service has already started.`);
    } else {
      this.logger.log(`Current time (${now.toISOString()}) is before time slot start time (${timeSlotStart.toISOString()}). Service has not started yet.`);
    }

    // Check if there's a currently serving queue for this service
    const servingQueue = await this.queueRepository.findOne({
      where: {
        serviceId: queue.serviceId,
        date: queue.date,
        timeSlot: queue.timeSlot,
        status: 'serving'
      }
    });

    // Determine the base time to use for calculation
    let baseTime: Date;

    // First check if the current time is after the time slot start time
    if (isServiceStarted) {
      if (servingQueue && servingQueue.servingStartedAt) {
        // If someone is being served, use their start time plus their service time as the base
        const servingStartTime = new Date(servingQueue.servingStartedAt);
        const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);

        // If the service has already started, use the current time as the base
        if (elapsedMinutes >= 0) {
          // Use the current time as the base if the service has already started
          baseTime = now;
          this.logger.log(`Someone is being served and service has already started. Using current time as base for estimated serve time calculation.`);
        } else {
          // Use the time slot start time if the service hasn't started yet
          baseTime = timeSlotStart;
          this.logger.log(`Someone is being served but service hasn't started yet. Using time slot start time as base for estimated serve time calculation.`);
        }
      } else {
        // If no one is being served but the service time has started, use the current time
        baseTime = now;
        this.logger.log(`No one is being served but service time has started. Using current time as base for estimated serve time calculation.`);
      }
    } else {
      // If the service time hasn't started yet, use the time slot start time
      baseTime = timeSlotStart;
      this.logger.log(`Service time hasn't started yet. Using time slot start time as base for estimated serve time calculation.`);
    }

    // Calculate estimated serve time based on current position
    const estimatedServeTime = new Date(baseTime);
    const positionForCalculation = queue.position;

    // If someone is being served and we're using current time as the base,
    // we need to adjust the calculation to account for the remaining service time
    if (servingQueue && servingQueue.servingStartedAt && baseTime.getTime() > timeSlotStart.getTime()) {
      // Get the remaining time for the current serving person
      const servingStartTime = new Date(servingQueue.servingStartedAt);
      const now = new Date();
      const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);
      const remainingMinutes = Math.max(0, serveTime - elapsedMinutes);

      // For position 1, wait time is just the remaining time
      // For position 2+, add full serve time for each position ahead minus position 1
      if (positionForCalculation === 1) {
        // Position 1 is already being served, so estimated time is now
        estimatedServeTime.setMinutes(estimatedServeTime.getMinutes());
      } else if (positionForCalculation === 2) {
        // Position 2 waits for position 1 to finish
        estimatedServeTime.setMinutes(estimatedServeTime.getMinutes() + remainingMinutes);
      } else {
        // Position 3+ waits for position 1 to finish plus full serve time for positions in between
        estimatedServeTime.setMinutes(estimatedServeTime.getMinutes() + remainingMinutes + (serveTime * (positionForCalculation - 2)));
      }

      this.logger.log(`Adjusted estimated serve time for queue ${queue.id} at position ${positionForCalculation} with someone being served (remaining: ${remainingMinutes} minutes).`);
    } else {
      // Standard calculation when no one is being served or service hasn't started yet
      estimatedServeTime.setMinutes(estimatedServeTime.getMinutes() + (serveTime * (positionForCalculation - 1)));
      this.logger.log(`Standard estimated serve time calculation for queue ${queue.id} at position ${positionForCalculation}.`);
    }

    // Update queue with estimated serve time
    queue.estimatedServeTime = estimatedServeTime;
    queue.waitTimeStatus = 'on-time'; // Default status

    // Save to database
    const updatedQueue = await this.queueRepository.save(queue);

    // Update Redis
    await this.updateRedisWithEstimatedServeTime(updatedQueue, serveTime);

    return updatedQueue;
  }

  /**
   * Update Redis with estimated serve time
   */
  private async updateRedisWithEstimatedServeTime(queue: Queue, serveTime: number): Promise<void> {
    // Get existing Redis data or create new object
    const redisKey = `queue:${queue.id}`;
    let queueData = await this.redisService.get(redisKey) || {};

    // Update with estimated serve time info
    queueData = {
      ...queueData,
      id: queue.id,
      estimatedServeTime: queue.estimatedServeTime?.toISOString(),
      waitTimeStatus: queue.waitTimeStatus,
      serveTime: queue.serveTime || serveTime, // Use queue.serveTime if available
      position: queue.position,
      initialPositionAtJoin: queue.initialPositionAtJoin || queue.position
    };

    // Save to Redis with 1 hour TTL
    await this.redisService.set(redisKey, queueData, { ex: 3600 });
  }

  /**
   * Calculate wait time status (early, on-time, delayed) for a queue
   * @param queueId Queue ID to calculate for
   * @returns Wait time status
   */
  async calculateWaitTimeStatus(queueId: number): Promise<string> {
    this.logger.log(`Calculating wait time status for queue ${queueId}`);

    // Get the queue
    const queue = await this.queueRepository.findOne({
      where: { id: queueId }
    });

    if (!queue) {
      throw new Error(`Queue ${queueId} not found`);
    }

    // If estimated serve time is not set, calculate it first
    if (!queue.estimatedServeTime) {
      await this.calculateEstimatedServeTime(queueId);
      // Refresh queue data
      const updatedQueue = await this.queueRepository.findOne({
        where: { id: queueId }
      });

      if (!updatedQueue || !updatedQueue.estimatedServeTime) {
        return 'on-time'; // Default if calculation fails
      }

      queue.estimatedServeTime = updatedQueue.estimatedServeTime;
    }

    // Check if there's a currently serving queue for this service
    const servingQueue = await this.queueRepository.findOne({
      where: {
        serviceId: queue.serviceId,
        date: queue.date,
        timeSlot: queue.timeSlot,
        status: 'serving'
      }
    });

    let waitTimeStatus = 'on-time'; // Default status

    if (servingQueue && servingQueue.servingStartedAt) {
      // Get service setup to determine serve time
      const serviceSetup = await this.serviceSetupRepository.findOne({
        where: { service: { id: queue.serviceId } }
      });

      // Default to 15 minutes if not specified
      let serveTime = 15;
      if (serviceSetup?.setupData?.servingTime) {
        serveTime = parseInt(serviceSetup.setupData.servingTime, 10);
      }

      // Calculate how much time has elapsed for the current serving person
      const servingStartTime = new Date(servingQueue.servingStartedAt);
      const now = new Date();
      const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);

      // Check if the serving person is taking longer than expected
      if (elapsedMinutes > serveTime) {
        // If they've been serving for longer than the expected serve time, mark as delayed
        const delayMinutes = elapsedMinutes - serveTime;
        waitTimeStatus = 'delayed';
        this.logger.log(`Queue ${queueId} wait time status: ${waitTimeStatus} (serving is delayed by ${delayMinutes} minutes)`);
      } else {
        // If they haven't exceeded serve time yet, check against estimated start time
        const actualStartTime = new Date(servingQueue.servingStartedAt);
        const estimatedStartTime = servingQueue.estimatedServeTime || actualStartTime;
        const diffMinutes = Math.round((actualStartTime.getTime() - estimatedStartTime.getTime()) / 60000);

        if (diffMinutes < -10) { // More than 10 minutes early
          waitTimeStatus = 'early';
        } else if (diffMinutes > 10) { // More than 10 minutes late
          waitTimeStatus = 'delayed';
        } else {
          waitTimeStatus = 'on-time';
        }

        this.logger.log(`Queue ${queueId} wait time status: ${waitTimeStatus} (diff: ${diffMinutes} minutes)`);
      }
    } else {
      // No serving queue, check if we're past the estimated serve time
      const now = new Date();
      const estimatedTime = queue.estimatedServeTime;

      if (estimatedTime) {
        const diffMinutes = Math.round((now.getTime() - estimatedTime.getTime()) / 60000);

        if (diffMinutes > 10) { // More than 10 minutes past estimated time
          waitTimeStatus = 'delayed';
        } else if (diffMinutes < -10) { // More than 10 minutes before estimated time
          waitTimeStatus = 'early';
        } else {
          waitTimeStatus = 'on-time';
        }

        this.logger.log(`Queue ${queueId} wait time status: ${waitTimeStatus} (diff: ${diffMinutes} minutes)`);
      }
    }

    // Update queue with wait time status
    queue.waitTimeStatus = waitTimeStatus;
    await this.queueRepository.save(queue);

    // Update Redis
    const redisKey = `queue:${queue.id}`;
    let queueData = await this.redisService.get(redisKey) || {};
    queueData.waitTimeStatus = waitTimeStatus;
    await this.redisService.set(redisKey, queueData, { ex: 3600 });

    return waitTimeStatus;
  }

  /**
   * Calculate actual service time for a completed queue
   * @param queueId Queue ID to calculate for
   * @returns Actual service time in minutes
   */
  async calculateActualServiceTime(queueId: number): Promise<number> {
    this.logger.log(`Calculating actual service time for queue ${queueId}`);

    // Get the queue
    const queue = await this.queueRepository.findOne({
      where: { id: queueId }
    });

    if (!queue) {
      throw new Error(`Queue ${queueId} not found`);
    }

    // Check if queue was served
    if (queue.status !== 'completed' || !queue.servingStartedAt || !queue.statusUpdatedAt) {
      this.logger.log(`Queue ${queueId} was not served or is missing timestamps`);
      return 0;
    }

    // Calculate actual service time in minutes
    const startTime = new Date(queue.servingStartedAt);
    const endTime = new Date(queue.statusUpdatedAt);
    const serviceTimeMinutes = Math.round((endTime.getTime() - startTime.getTime()) / 60000);

    // Update Redis
    const redisKey = `queue:${queue.id}`;
    let queueData = await this.redisService.get(redisKey) || {};
    queueData.actualServiceTime = serviceTimeMinutes; // Store in Redis but not in DB
    await this.redisService.set(redisKey, queueData, { ex: 86400 * 7 }); // 7 days TTL for completed queues

    this.logger.log(`Queue ${queueId} actual service time: ${serviceTimeMinutes} minutes`);

    return serviceTimeMinutes;
  }

  /**
   * Recalculate estimated serve times for all queues in a service
   * @param serviceId Service ID to recalculate for
   * @param date Date to recalculate for
   * @param timeSlot Time slot to recalculate for
   */
  async recalculateEstimatedServeTimes(serviceId: number, date: Date, timeSlot: string): Promise<void> {
    this.logger.log(`Recalculating estimated serve times for service ${serviceId}, date ${date}, timeSlot ${timeSlot}`);

    // Get all waiting queues for this service, date, and time slot
    const waitingQueues = await this.queueRepository.find({
      where: {
        serviceId,
        date,
        timeSlot,
        status: 'waiting'
      },
      order: {
        position: 'ASC'
      }
    });

    this.logger.log(`Found ${waitingQueues.length} waiting queues to recalculate`);

    // Get service setup to determine serve time
    const serviceSetup = await this.serviceSetupRepository.findOne({
      where: { service: { id: serviceId } }
    });

    // Default to 15 minutes if not specified
    let serveTime = 15;

    // Create a map to store serve times for each subunit
    const subunitServeTimes = new Map<string, number>();

    // Check if this is a service with subunits
    if (serviceSetup?.setupData?.hasSubUnits && Array.isArray(serviceSetup.setupData.subUnits) && serviceSetup.setupData.subUnits.length > 0) {
      // Store serve times for each subunit
      serviceSetup.setupData.subUnits.forEach((subUnit, index) => {
        if (subUnit?.avgServeTime) {
          const subUnitServeTime = parseInt(subUnit.avgServeTime, 10);
          subunitServeTimes.set(index.toString(), subUnitServeTime);
          this.logger.log(`Stored subunit ${index} (${subUnit.name}) serve time: ${subUnitServeTime} minutes`);
        }
      });

      // Use the first subunit's serve time as the default if available
      if (subunitServeTimes.has('0')) {
        serveTime = subunitServeTimes.get('0') || 15;
        this.logger.log(`Using first subunit's serve time as default: ${serveTime} minutes`);
      }
    }
    // For regular services, use the service setup serve time
    else if (serviceSetup?.setupData?.servingTime) {
      serveTime = parseInt(serviceSetup.setupData.servingTime, 10);
      this.logger.log(`Using service configuration serving time: ${serveTime} minutes`);
    }

    // Parse time slot to get start time
    const timeSlotParts = timeSlot.split(' - ');
    const timeStart = timeSlotParts[0].trim(); // e.g., "06:00 PM"

    // Parse the start time considering AM/PM format
    let startHour = 0;
    let startMinute = 0;

    if (timeStart.includes('AM') || timeStart.includes('PM')) {
      const [timeValue, period] = timeStart.split(' ');
      const [hourStr, minuteStr] = timeValue.split(':');
      let hour = parseInt(hourStr, 10);
      const minute = parseInt(minuteStr, 10);

      // Convert to 24-hour format
      if (period.toUpperCase() === 'PM' && hour < 12) {
        hour += 12;
      } else if (period.toUpperCase() === 'AM' && hour === 12) {
        hour = 0;
      }

      startHour = hour;
      startMinute = minute;
    } else {
      // Fallback to original parsing if no AM/PM
      const [hours, minutes] = timeStart.split(':').map(part => parseInt(part, 10));
      startHour = hours;
      startMinute = minutes;
    }

    // Set the time slot start time
    const timeSlotStart = new Date(date);
    timeSlotStart.setHours(startHour, startMinute, 0, 0);

    // Get the current time
    const now = new Date();

    // Check if the current time is after the time slot start time
    const isServiceStarted = now > timeSlotStart;

    if (isServiceStarted) {
      this.logger.log(`Current time (${now.toISOString()}) is after time slot start time (${timeSlotStart.toISOString()}). Service has already started.`);
    } else {
      this.logger.log(`Current time (${now.toISOString()}) is before time slot start time (${timeSlotStart.toISOString()}). Service has not started yet.`);
    }

    // Check if there's a currently serving queue
    const servingQueue = await this.queueRepository.findOne({
      where: {
        serviceId,
        date,
        timeSlot,
        status: 'serving'
      }
    });

    // We'll calculate shift time individually for each queue based on their position
    // This ensures each queue gets a unique estimated serve time

    // Recalculate estimated serve time for each queue
    for (const queue of waitingQueues) {
      // Determine which serve time to use based on subunit
      let queueServeTime = serveTime;

      // If this queue has a subunit and we have a serve time for it, use that
      if (queue.subUnitId !== undefined && subunitServeTimes.has(queue.subUnitId)) {
        queueServeTime = subunitServeTimes.get(queue.subUnitId) || serveTime;
        this.logger.log(`Using subunit ${queue.subUnitId} serve time for queue ${queue.id}: ${queueServeTime} minutes`);
      }

      // Determine the base time to use for calculation
      let baseTime: Date;

      // First check if the current time is after the time slot start time
      if (isServiceStarted) {
        if (servingQueue && servingQueue.servingStartedAt) {
          // If someone is being served, use their start time plus their service time as the base
          const servingStartTime = new Date(servingQueue.servingStartedAt);
          const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);

          // If the service has already started, use the current time as the base
          if (elapsedMinutes >= 0) {
            // Use the current time as the base if the service has already started
            baseTime = now;
            this.logger.log(`Someone is being served and service has already started. Using current time as base for estimated serve time calculation.`);
          } else {
            // Use the time slot start time if the service hasn't started yet
            baseTime = timeSlotStart;
            this.logger.log(`Someone is being served but service hasn't started yet. Using time slot start time as base for estimated serve time calculation.`);
          }
        } else {
          // If no one is being served but the service time has started, use the current time
          baseTime = now;
          this.logger.log(`No one is being served but service time has started. Using current time as base for estimated serve time calculation.`);
        }
      } else {
        // If the service time hasn't started yet, use the time slot start time
        baseTime = timeSlotStart;
        this.logger.log(`Service time hasn't started yet. Using time slot start time as base for estimated serve time calculation.`);
      }

      // Calculate estimated serve time based on current position
      const estimatedServeTime = new Date(baseTime);
      const positionForCalculation = queue.position;

      // If someone is being served and we're using current time as the base,
      // we need to adjust the calculation to account for the remaining service time
      if (servingQueue && servingQueue.servingStartedAt && baseTime.getTime() > timeSlotStart.getTime()) {
        // Get the remaining time for the current serving person
        const servingStartTime = new Date(servingQueue.servingStartedAt);
        const now = new Date();
        const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);
        const remainingMinutes = Math.max(0, queueServeTime - elapsedMinutes);

        // For position 1, wait time is just the remaining time
        // For position 2+, add full serve time for each position ahead minus position 1
        if (positionForCalculation === 1) {
          // Position 1 is already being served, so estimated time is now
          estimatedServeTime.setMinutes(estimatedServeTime.getMinutes());
        } else if (positionForCalculation === 2) {
          // Position 2 waits for position 1 to finish
          estimatedServeTime.setMinutes(estimatedServeTime.getMinutes() + remainingMinutes);
        } else {
          // Position 3+ waits for position 1 to finish plus full serve time for positions in between
          estimatedServeTime.setMinutes(estimatedServeTime.getMinutes() + remainingMinutes + (queueServeTime * (positionForCalculation - 2)));
        }

        this.logger.log(`Adjusted estimated serve time for queue ${queue.id} at position ${positionForCalculation} with someone being served (remaining: ${remainingMinutes} minutes).`);
      } else {
        // Standard calculation when no one is being served or service hasn't started yet
        estimatedServeTime.setMinutes(estimatedServeTime.getMinutes() + (queueServeTime * (positionForCalculation - 1)));
        this.logger.log(`Standard estimated serve time calculation for queue ${queue.id} at position ${positionForCalculation}.`);
      }

      // Calculate individual shift time for this queue if someone is being served
      let individualShiftMinutes = 0;

      if (servingQueue && servingQueue.servingStartedAt && servingQueue.estimatedServeTime) {
        const actualStartTime = new Date(servingQueue.servingStartedAt);
        const estimatedStartTime = new Date(servingQueue.estimatedServeTime);

        // Calculate difference in minutes
        individualShiftMinutes = Math.round((actualStartTime.getTime() - estimatedStartTime.getTime()) / 60000);

        this.logger.log(`Service is ${individualShiftMinutes > 0 ? 'delayed' : 'early'} by ${Math.abs(individualShiftMinutes)} minutes for queue ${queue.id}`);
      }

      // Apply individual shift time
      estimatedServeTime.setMinutes(estimatedServeTime.getMinutes() + individualShiftMinutes);

      // Update queue
      queue.estimatedServeTime = estimatedServeTime;

      // Store the serve time in the queue for reference
      queue.serveTime = queueServeTime;

      // Determine wait time status
      if (individualShiftMinutes < -10) {
        queue.waitTimeStatus = 'early';
      } else if (individualShiftMinutes > 10) {
        queue.waitTimeStatus = 'delayed';
      } else {
        queue.waitTimeStatus = 'on-time';
      }

      // Update Redis
      const redisKey = `queue:${queue.id}`;
      let queueData = await this.redisService.get(redisKey) || {};
      queueData.estimatedServeTime = estimatedServeTime.toISOString();
      queueData.waitTimeStatus = queue.waitTimeStatus;
      queueData.shiftMinutes = individualShiftMinutes;
      queueData.position = queue.position;
      queueData.initialPositionAtJoin = queue.initialPositionAtJoin || queue.position;
      queueData.serveTime = queue.serveTime || queueServeTime; // Store the serve time in Redis
      await this.redisService.set(redisKey, queueData, { ex: 3600 });
    }

    // Save all queues to database
    if (waitingQueues.length > 0) {
      await this.queueRepository.save(waitingQueues);
    }

    this.logger.log(`Recalculated estimated serve times for ${waitingQueues.length} queues`);
  }

  /**
   * Get estimated wait time for a queue
   * @param queueId Queue ID to get wait time for
   * @returns Estimated wait time in minutes and status
   */
  async getEstimatedWaitTime(queueId: number): Promise<{ waitTimeMinutes: number; waitTimeStatus: string; estimatedServeTime: Date; position: number; initialPositionAtJoin: number }> {
    this.logger.log(`Getting estimated wait time for queue ${queueId}`);

    // Get the queue
    const queue = await this.queueRepository.findOne({
      where: { id: queueId }
    });

    if (!queue) {
      throw new Error(`Queue ${queueId} not found`);
    }

    // Parse time slot to get start time
    const timeSlotParts = queue.timeSlot.split(' - ');
    const timeStart = timeSlotParts[0].trim(); // e.g., "06:00 PM"

    // Parse the start time considering AM/PM format
    let startHour = 0;
    let startMinute = 0;

    if (timeStart.includes('AM') || timeStart.includes('PM')) {
      const [timeValue, period] = timeStart.split(' ');
      const [hourStr, minuteStr] = timeValue.split(':');
      let hour = parseInt(hourStr, 10);
      const minute = parseInt(minuteStr, 10);

      // Convert to 24-hour format
      if (period.toUpperCase() === 'PM' && hour < 12) {
        hour += 12;
      } else if (period.toUpperCase() === 'AM' && hour === 12) {
        hour = 0;
      }

      startHour = hour;
      startMinute = minute;
    } else {
      // Fallback to original parsing if no AM/PM
      const [hours, minutes] = timeStart.split(':').map(part => parseInt(part, 10));
      startHour = hours;
      startMinute = minutes;
    }

    // Set the time slot start time
    const timeSlotStart = new Date(queue.date);
    timeSlotStart.setHours(startHour, startMinute, 0, 0);

    // Get the current time
    const now = new Date();

    // Check if the current time is after the time slot start time
    const isServiceStarted = now > timeSlotStart;

    if (isServiceStarted) {
      this.logger.log(`Current time (${now.toISOString()}) is after time slot start time (${timeSlotStart.toISOString()}). Service has already started.`);
    } else {
      this.logger.log(`Current time (${now.toISOString()}) is before time slot start time (${timeSlotStart.toISOString()}). Service has not started yet.`);
    }

    // If estimated serve time is not set, calculate it first
    if (!queue.estimatedServeTime) {
      await this.calculateEstimatedServeTime(queueId);
      // Refresh queue data
      const updatedQueue = await this.queueRepository.findOne({
        where: { id: queueId }
      });

      if (!updatedQueue || !updatedQueue.estimatedServeTime) {
        return {
          waitTimeMinutes: 0,
          waitTimeStatus: 'on-time',
          estimatedServeTime: new Date(),
          position: 0,
          initialPositionAtJoin: 0
        };
      }

      queue.estimatedServeTime = updatedQueue.estimatedServeTime;
      queue.waitTimeStatus = updatedQueue.waitTimeStatus;
    }

    // Get service setup to determine serve time
    const serviceSetup = await this.serviceSetupRepository.findOne({
      where: { service: { id: queue.serviceId } }
    });

    // Default to 15 minutes if not specified
    let serveTime = 15;

    // Check if this is a service with subunits
    if (serviceSetup?.setupData?.hasSubUnits && Array.isArray(serviceSetup.setupData.subUnits) && serviceSetup.setupData.subUnits.length > 0) {
      // Get the subunit ID from the queue
      const subUnitId = queue.subUnitId !== undefined ? parseInt(queue.subUnitId, 10) : 0;

      // Make sure the subunit ID is valid
      const subUnitIndex = isNaN(subUnitId) ? 0 : Math.min(subUnitId, serviceSetup.setupData.subUnits.length - 1);

      // Get the subunit
      const subUnit = serviceSetup.setupData.subUnits[subUnitIndex];

      // Get serve time from subunit
      if (subUnit?.avgServeTime) {
        serveTime = parseInt(subUnit.avgServeTime, 10);
        this.logger.log(`Using subunit ${subUnitIndex} (${subUnit.name}) serve time: ${serveTime} minutes`);
      }
    }
    // For regular services, use the service setup serve time
    else if (serviceSetup?.setupData?.servingTime) {
      serveTime = parseInt(serviceSetup.setupData.servingTime, 10);
      this.logger.log(`Using service setup serve time: ${serveTime} minutes`);
    }

    // Use the serve time from the queue if available
    if (queue.serveTime) {
      serveTime = queue.serveTime;
      this.logger.log(`Using queue's stored serve time: ${serveTime} minutes`);
    }

    // Check if there's a currently serving queue
    const servingQueue = await this.queueRepository.findOne({
      where: {
        serviceId: queue.serviceId,
        date: queue.date,
        timeSlot: queue.timeSlot,
        status: 'serving'
      }
    });

    let waitTimeMinutes = 0;

    // Special handling for position 1 (front of queue)
    if (queue.position === 1) {
      // For position 1, wait time should not decrease until they start being served
      // If there's no serving queue, use the full serve time
      if (!servingQueue) {
        waitTimeMinutes = serveTime;
        this.logger.log(`Queue ${queueId} is at position 1 and no one is being served. Wait time: ${waitTimeMinutes} minutes`);
      } else {
        // If someone is being served, position 1 should wait until they're done
        // Calculate remaining time for the serving queue
        if (servingQueue.servingStartedAt) {
          // If we have a valid servingStartedAt timestamp
          const servingStartTime = new Date(servingQueue.servingStartedAt);
          const now = new Date();
          const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);
          const remainingMinutes = Math.max(0, serveTime - elapsedMinutes);

          waitTimeMinutes = remainingMinutes;
          this.logger.log(`Queue ${queueId} is at position 1 and someone is being served. Remaining time: ${remainingMinutes} minutes`);
        } else {
          // If servingStartedAt is null, use the full serve time
          waitTimeMinutes = serveTime;
          this.logger.log(`Queue ${queueId} is at position 1 and someone is being served but no start time. Using full serve time: ${serveTime} minutes`);
        }
      }
    } else {
      // For position 2 and beyond
      // Calculate wait time based on current position
      const positionForCalculation = queue.position;

      // For position 2, the wait time should be the serve time of position 1 (with buffer)
      // For position 3+, add serve time for each position ahead
      let baseWaitTime = 0;

      // Check if there's a currently serving queue
      if (servingQueue && servingQueue.servingStartedAt) {
        // Calculate how much time has elapsed for the current serving person
        const servingStartTime = new Date(servingQueue.servingStartedAt);
        const now = new Date();
        const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);
        const remainingMinutes = Math.max(0, serveTime - elapsedMinutes);

        this.logger.log(`Someone is being served with ${remainingMinutes} minutes remaining out of ${serveTime} minutes total.`);

        if (positionForCalculation === 2) {
          // Position 2 just waits for position 1 to finish (remaining time)
          baseWaitTime = remainingMinutes;
          this.logger.log(`Queue ${queueId} at position ${positionForCalculation}, base wait time is remaining time for position 1: ${baseWaitTime} minutes`);
        } else {
          // Position 3+ waits for position 1 to finish plus full serve time for positions in between
          baseWaitTime = remainingMinutes + (serveTime * (positionForCalculation - 2));
          this.logger.log(`Queue ${queueId} at position ${positionForCalculation}, base wait time is remaining time for position 1 plus serve time for positions in between: ${baseWaitTime} minutes`);
        }
      } else {
        // No one is being served, use standard calculation
        if (positionForCalculation === 2) {
          // Position 2 just waits for position 1 to be served
          baseWaitTime = serveTime;
          this.logger.log(`Queue ${queueId} at position ${positionForCalculation}, base wait time is just position 1's serve time: ${baseWaitTime} minutes`);
        } else {
          // Position 3+ waits for all positions ahead
          // Position 3 waits for positions 1 and 2, so (3-1) * serveTime = 2 * serveTime
          baseWaitTime = (positionForCalculation - 1) * serveTime;
          this.logger.log(`Queue ${queueId} at position ${positionForCalculation}, base wait time for all positions ahead: ${baseWaitTime} minutes`);
        }
      }

      // Add buffer time (15% extra) to account for delays
      const bufferFactor = 1.15; // 15% buffer
      waitTimeMinutes = Math.ceil(baseWaitTime * bufferFactor);

      this.logger.log(`Queue ${queueId} at position ${positionForCalculation}, base wait time: ${baseWaitTime} minutes, with buffer: ${waitTimeMinutes} minutes`);

      // If someone is already being served, we need to adjust the wait time
      if (servingQueue && servingQueue.servingStartedAt) {
        // Calculate how much time has elapsed for the current serving person
        const servingStartTime = new Date(servingQueue.servingStartedAt);
        const now = new Date();
        const elapsedMinutes = Math.floor((now.getTime() - servingStartTime.getTime()) / 60000);
        const remainingMinutes = Math.max(0, serveTime - elapsedMinutes);

        // Check if the serving person is taking longer than expected
        const isDelayed = elapsedMinutes > serveTime;
        let delayFactor = 1.0; // Default, no delay

        if (isDelayed) {
          // Calculate how much longer they're taking as a percentage of the expected serve time
          const delayMinutes = elapsedMinutes - serveTime;
          const delayPercentage = delayMinutes / serveTime;

          // Cap the delay factor at 50% extra time
          delayFactor = Math.min(1.5, 1.0 + delayPercentage);

          this.logger.log(`Serving is delayed by ${delayMinutes} minutes (${Math.round(delayPercentage * 100)}% of serve time), using delay factor: ${delayFactor.toFixed(2)}`);
        }

        // For position 2, wait time is just the remaining time of position 1
        if (positionForCalculation === 2) {
          // If position 1 is delayed, add a percentage of the serve time to the wait time
          if (isDelayed) {
            // Add a percentage of the serve time based on the delay factor
            const additionalTime = Math.ceil(serveTime * (delayFactor - 1.0) * 0.5); // Use half of the calculated delay
            waitTimeMinutes = Math.ceil(remainingMinutes * bufferFactor) + additionalTime;
            this.logger.log(`Position 1 is delayed, adding ${additionalTime} minutes to position 2 wait time`);
          } else {
            waitTimeMinutes = Math.ceil(remainingMinutes * bufferFactor);
          }
          this.logger.log(`Position 1 is being served with ${remainingMinutes} minutes remaining, position 2 wait time: ${waitTimeMinutes} minutes`);
        }
        // For position 3+, subtract the elapsed time of position 1 from the total wait time
        else {
          // If position 1 is delayed, add a percentage of the serve time to the wait time
          if (isDelayed) {
            // Add a percentage of the serve time based on the delay factor and position
            // The further back in line, the more additional time is added
            const positionMultiplier = Math.min(1.0, (positionForCalculation - 2) * 0.2); // 0.2 for position 3, 0.4 for position 4, etc., max 1.0
            const additionalTime = Math.ceil(serveTime * (delayFactor - 1.0) * positionMultiplier);
            waitTimeMinutes = Math.ceil((baseWaitTime - (serveTime - remainingMinutes)) * bufferFactor) + additionalTime;
            this.logger.log(`Position 1 is delayed, adding ${additionalTime} minutes to position ${positionForCalculation} wait time`);
          } else {
            waitTimeMinutes = Math.ceil((baseWaitTime - (serveTime - remainingMinutes)) * bufferFactor);
          }
          this.logger.log(`Position 1 is being served with ${remainingMinutes} minutes remaining, position ${positionForCalculation} adjusted wait time: ${waitTimeMinutes} minutes`);
        }
      }
    }

    // Update wait time status if needed
    await this.calculateWaitTimeStatus(queueId);

    // Refresh queue data to get latest status
    const refreshedQueue = await this.queueRepository.findOne({
      where: { id: queueId }
    });

    return {
      waitTimeMinutes,
      waitTimeStatus: refreshedQueue?.waitTimeStatus || 'on-time',
      estimatedServeTime: refreshedQueue?.estimatedServeTime || new Date(),
      position: refreshedQueue?.position || 0,
      initialPositionAtJoin: refreshedQueue?.initialPositionAtJoin || 0
    };
  }

  /**
   * Get actual service time for a completed queue directly from timestamps
   * @param queueId Queue ID to get service time for
   * @returns Actual service time data
   */
  async getActualServiceTime(queueId: number): Promise<{
    serviceTimeMinutes: number;
    servingStartedAt: Date | null;
    statusUpdatedAt: Date | null;
    status: string;
  }> {
    this.logger.log(`Getting actual service time for queue ${queueId}`);

    // Get the queue
    const queue = await this.queueRepository.findOne({
      where: { id: queueId }
    });

    if (!queue) {
      throw new Error(`Queue ${queueId} not found`);
    }

    // Default response
    const response = {
      serviceTimeMinutes: 0,
      servingStartedAt: queue.servingStartedAt,
      statusUpdatedAt: queue.statusUpdatedAt,
      status: queue.status
    };

    // Check if queue was served
    if (queue.status !== 'completed' || !queue.servingStartedAt || !queue.statusUpdatedAt) {
      this.logger.log(`Queue ${queueId} was not served or is missing timestamps`);
      return response;
    }

    // Calculate actual service time in minutes
    const startTime = new Date(queue.servingStartedAt);
    const endTime = new Date(queue.statusUpdatedAt);
    const serviceTimeMinutes = Math.round((endTime.getTime() - startTime.getTime()) / 60000);

    response.serviceTimeMinutes = serviceTimeMinutes;

    this.logger.log(`Queue ${queueId} actual service time: ${serviceTimeMinutes} minutes`);

    return response;
  }

  /**
   * Move a queue to the end of the line
   * @param queueId Queue ID to move
   * @returns Updated queue object
   */
  async moveToEndOfLine(queueId: number): Promise<Queue> {
    this.logger.log(`Moving queue ${queueId} to the end of the line`);

    // Get the queue
    const queue = await this.queueRepository.findOne({
      where: { id: queueId }
    });

    if (!queue) {
      throw new Error(`Queue ${queueId} not found`);
    }

    // Get all waiting queues for this service, date, and time slot
    const allWaitingQueues = await this.queueRepository.find({
      where: {
        serviceId: queue.serviceId,
        date: queue.date,
        timeSlot: queue.timeSlot,
        status: 'waiting',
      },
      order: {
        position: 'ASC',
        createdAt: 'ASC'
      }
    });

    this.logger.log(`Found ${allWaitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);

    // Calculate the new position (at the end of the line)
    const newPosition = allWaitingQueues.length;

    // Update the queue's position but keep initialPositionAtJoin unchanged
    queue.position = newPosition;
    // Note: We don't update initialPositionAtJoin as it should remain the original position

    // Save to database
    const updatedQueue = await this.queueRepository.save(queue);

    // Update Redis
    const redisKey = `queue:${queue.id}`;
    let queueData = await this.redisService.get(redisKey) || {};
    queueData.position = newPosition;
    // Make sure initialPositionAtJoin is preserved in Redis
    if (!queueData.initialPositionAtJoin) {
      queueData.initialPositionAtJoin = queue.initialPositionAtJoin || queue.position;
    }
    await this.redisService.set(redisKey, queueData, { ex: 3600 });

    // Update positions in Redis
    const dateStr = new Date(queue.date).toISOString().split('T')[0];
    const positionsKey = `service:${queue.serviceId}:positions:${dateStr}:${queue.timeSlot}`;
    const positionsData = await this.redisService.get(positionsKey) || {};

    // Update the position for this queue
    positionsData[queue.id] = newPosition;

    // Save updated positions to Redis
    await this.redisService.set(positionsKey, positionsData);

    // Also save using the dedicated method
    await this.redisService.saveQueuePosition(
      queue.serviceId.toString(),
      dateStr,
      queue.timeSlot,
      positionsData
    );

    // Invalidate any cached queue positions
    await this.redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);

    // Invalidate service queues cache
    await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
    await this.redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');

    // Recalculate estimated serve times for all queues in this service
    await this.recalculateEstimatedServeTimes(
      queue.serviceId,
      queue.date,
      queue.timeSlot
    );

    return updatedQueue;
  }

  /**
   * Scheduled job to recalculate estimated serve times for all active queues
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async recalculateAllEstimatedServeTimes(): Promise<void> {
    this.logger.log('Running scheduled job to recalculate all estimated serve times');

    // Get today's date
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get all services with active queues today
    const activeServices = await this.queueRepository
      .createQueryBuilder('queue')
      .select('DISTINCT queue.serviceId, queue.date, queue.timeSlot')
      .where('queue.date >= :today', { today })
      .andWhere('queue.status = :status', { status: 'waiting' })
      .getRawMany();

    this.logger.log(`Found ${activeServices.length} active services to recalculate`);

    // Recalculate for each service
    for (const service of activeServices) {
      try {
        await this.recalculateEstimatedServeTimes(
          service.serviceId,
          new Date(service.date),
          service.timeSlot
        );
      } catch (error) {
        this.logger.error(`Error recalculating for service ${service.serviceId}:`, error);
      }
    }

    this.logger.log('Completed recalculation of all estimated serve times');
  }
}