"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimplifyTimeSlots1716210000000 = void 0;
class SimplifyTimeSlots1716210000000 {
    constructor() {
        this.name = 'SimplifyTimeSlots1716210000000';
    }
    async up(queryRunner) {
        console.log('Starting migration to simplify time slots...');
        const serviceSetups = await queryRunner.query(`SELECT id, "setupData" FROM service_setups`);
        for (const setup of serviceSetups) {
            try {
                const setupData = setup.setupData;
                let modified = false;
                if (!setupData.hasSubUnits) {
                    if (!setupData.availableHours) {
                        setupData.availableHours = {};
                        modified = true;
                    }
                    if (setupData.timeSlotsByDay && Object.keys(setupData.timeSlotsByDay).length > 0) {
                        for (const day in setupData.timeSlotsByDay) {
                            if (Array.isArray(setupData.timeSlotsByDay[day])) {
                                setupData.availableHours[day] = setupData.timeSlotsByDay[day];
                                modified = true;
                            }
                        }
                    }
                    if (Array.isArray(setupData.timeSlots) && setupData.timeSlots.length > 0 &&
                        (!setupData.availableHours || Object.keys(setupData.availableHours).length === 0)) {
                        if (Array.isArray(setupData.selectedDays)) {
                            setupData.selectedDays.forEach(day => {
                                setupData.availableHours[day] = setupData.timeSlots;
                            });
                            modified = true;
                        }
                    }
                }
                if (setupData.hasSubUnits && Array.isArray(setupData.subUnits)) {
                    for (let i = 0; i < setupData.subUnits.length; i++) {
                        const unit = setupData.subUnits[i];
                        if (!unit.availableHours) {
                            unit.availableHours = {};
                            modified = true;
                        }
                        if (unit.dayWiseAvailableHours && Object.keys(unit.dayWiseAvailableHours).length > 0) {
                            for (const day in unit.dayWiseAvailableHours) {
                                if (Array.isArray(unit.dayWiseAvailableHours[day])) {
                                    unit.availableHours[day] = unit.dayWiseAvailableHours[day];
                                    modified = true;
                                }
                            }
                        }
                        if (Array.isArray(unit.timeSlots) && unit.timeSlots.length > 0 &&
                            (!unit.availableHours || Object.keys(unit.availableHours).length === 0)) {
                            if (Array.isArray(unit.selectedDays)) {
                                unit.selectedDays.forEach(day => {
                                    unit.availableHours[day] = unit.timeSlots;
                                });
                                modified = true;
                            }
                        }
                    }
                }
                if (modified) {
                    await queryRunner.query(`UPDATE service_setups SET "setupData" = $1 WHERE id = $2`, [JSON.stringify(setupData), setup.id]);
                    console.log(`Updated service setup with ID ${setup.id}`);
                }
            }
            catch (error) {
                console.error(`Error processing service setup with ID ${setup.id}:`, error);
            }
        }
        console.log('Completed migration to simplify time slots');
    }
    async down(queryRunner) {
        console.log('No down migration implemented for time slots simplification');
    }
}
exports.SimplifyTimeSlots1716210000000 = SimplifyTimeSlots1716210000000;
//# sourceMappingURL=1716210000000-SimplifyTimeSlots.js.map