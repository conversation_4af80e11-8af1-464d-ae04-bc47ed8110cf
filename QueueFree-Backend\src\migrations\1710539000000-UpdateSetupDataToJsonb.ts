import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateSetupDataToJsonb1710539000000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // First, modify the column type to JSONB
        await queryRunner.query(`
            ALTER TABLE "service_setups" 
            ALTER COLUMN "setupData" TYPE JSONB USING "setupData"::JSONB
        `);

        // Add GIN index for better JSON querying performance
        await queryRunner.query(`
            CREATE INDEX IF NOT EXISTS idx_setup_data ON "service_setups" USING GIN ("setupData")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the index first
        await queryRunner.query(`
            DROP INDEX IF EXISTS idx_setup_data
        `);

        // Convert back to JSON type
        await queryRunner.query(`
            ALTER TABLE "service_setups" 
            ALTER COLUMN "setupData" TYPE JSON USING "setupData"::JSON
        `);
    }
}
