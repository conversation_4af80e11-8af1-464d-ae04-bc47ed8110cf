import React, { useState, useEffect } from "react";
import {
  Text,
  SafeAreaView,
  StatusBar,
  View,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  ScrollView,
  Image,
  Linking,
  Alert,
} from "react-native";
import { useUser } from "@clerk/clerk-expo";
import { useRouter } from "expo-router";
import { images } from "@/constants";
import { cn } from "@/lib/utils";

// Define interface for queue items
interface QueueItem {
  id: number;
  serviceId: number;
  serviceName: string;
  serviceType: string;
  date: string;
  timeSlot: string;
  status: string;
  statusDisplay?: string; // Added for displaying status labels
  isVIP: boolean;
  createdAt: string;
  uniqueSlotId?: string;
  position?: number;
  hasSubUnits?: boolean;
  subUnitId?: string;
  subUnitName?: string;
  address?: {
    details: {
      buildingNo: string;
      locality: string;
      city: string;
    };
    coordinates?: {
      latitude: number;
      longitude: number;
    };
    googleMapsLink?: string;
  };
  contactPhone?: string;
  images?: string[];
}

// Define interface for the sections
interface QueueData {
  upcoming: QueueItem[];
  completed: QueueItem[];
  cancelled: QueueItem[];
}

const QueuesScreen = () => {
  const { user } = useUser();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<
    "upcoming" | "completed" | "cancelled"
  >("upcoming");
  const [queueData, setQueueData] = useState<QueueData>({
    upcoming: [],
    completed: [],
    cancelled: [],
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // API base URL
  const API_BASE_URL = "http://**************:3000/api";

  // Trigger backend to update expired queues
  const triggerExpiredQueuesUpdate = async () => {
    try {
      // Call the backend endpoint to update expired queues
      const response = await fetch(`${API_BASE_URL}/customer/update-expired-queues`);
      if (response.ok) {
        const result = await response.json();
        console.log(`Queue status update result: ${result.message}`);
      }
    } catch (error) {
      console.error('Error triggering queue status update:', error);
      // Continue with fetching queues even if this fails
    }
  };

  // Fetch queue data
  const fetchQueues = async () => {
    if (!user || !user.id) return;

    try {
      setLoading(true);

      // First trigger the backend to update any expired queues
      await triggerExpiredQueuesUpdate();

      const response = await fetch(
        `${API_BASE_URL}/customer/queues/${user.id}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch queue data");
      }

      const data = await response.json();
      console.log("Received queue data:", JSON.stringify(data));

      // Log the raw queue data to check if subunit information is included
      console.log("Raw queue data structure:", JSON.stringify(data, null, 2));

      // Log subunit information for debugging
      if (data.status === "success" && data.queues) {
        // Check upcoming queues
        if (data.queues.upcoming && data.queues.upcoming.length > 0) {
          data.queues.upcoming.forEach((queue: any, index: number) => {
            // Log position data for debugging
            console.log(`Upcoming queue ${index} (ID: ${queue.id}): position=${queue.position}, status=${queue.status}`);

            if (queue.hasSubUnits || queue.subUnitId || queue.subUnitName) {
              console.log(`Upcoming queue ${index} has subunit: ${queue.subUnitName} (ID: ${queue.subUnitId}, hasSubUnits: ${queue.hasSubUnits})`);
            }
          });
        }

        // Check completed queues
        if (data.queues.completed && data.queues.completed.length > 0) {
          data.queues.completed.forEach((queue: any, index: number) => {
            if (queue.hasSubUnits || queue.subUnitId || queue.subUnitName) {
              console.log(`Completed queue ${index} has subunit: ${queue.subUnitName} (ID: ${queue.subUnitId}, hasSubUnits: ${queue.hasSubUnits})`);
            }
          });
        }

        // Check cancelled queues
        if (data.queues.cancelled && data.queues.cancelled.length > 0) {
          data.queues.cancelled.forEach((queue: any, index: number) => {
            if (queue.hasSubUnits || queue.subUnitId || queue.subUnitName) {
              console.log(`Cancelled queue ${index} has subunit: ${queue.subUnitName} (ID: ${queue.subUnitId}, hasSubUnits: ${queue.hasSubUnits})`);
            }
          });
        }
      }

      if (data.status === "success") {
        // Merge no-show queues into completed section
        if (data.queues.noShow && Array.isArray(data.queues.noShow)) {
          // Add a status indicator to each no-show queue
          const noShowWithIndicator = data.queues.noShow.map((queue: any) => ({
            ...queue,
            statusDisplay: 'no-show' // Add a field to display status
          }));

          // Add status indicator to completed queues
          const completedWithIndicator = data.queues.completed.map((queue: any) => ({
            ...queue,
            statusDisplay: 'completed' // Add a field to display status
          }));

          // Merge no-show queues into completed
          data.queues.completed = [...completedWithIndicator, ...noShowWithIndicator];
        }

        // Add status indicators to upcoming and cancelled queues
        if (data.queues.upcoming && Array.isArray(data.queues.upcoming)) {
          data.queues.upcoming = data.queues.upcoming.map((queue: any) => ({
            ...queue,
            statusDisplay: queue.status // Use actual status (waiting, checked-in, etc.)
          }));

          // Check if there are any 'serving' status queues in the server response
          // If not found in 'upcoming', check all queues to find any with 'serving' status
          const hasServingQueue = data.queues.upcoming.some((queue: any) => queue.status === 'serving');
          console.log("Has serving queue in upcoming array:", hasServingQueue);

          if (!hasServingQueue) {
            // If we have more array properties that might contain queues
            const allQueues = [...data.queues.upcoming];
            if (data.queues.serving && Array.isArray(data.queues.serving)) {
              // If backend specifically provides 'serving' array, use it
              console.log("Found serving array in response with", data.queues.serving.length, "queues");
              const servingWithIndicator = data.queues.serving.map((queue: any) => ({
                ...queue,
                statusDisplay: 'serving'
              }));
              allQueues.push(...servingWithIndicator);
            } else {
              // Otherwise check all queues for 'serving' status
              // Query specifically for serving queues
              try {
                console.log("Making additional request for serving queues");
                const servingResponse = await fetch(
                  `${API_BASE_URL}/customer/queues/${user.id}/status/serving`
                );

                if (servingResponse.ok) {
                  const servingData = await servingResponse.json();
                  console.log("Serving queue response:", JSON.stringify(servingData));
                  if (servingData.status === 'success' && Array.isArray(servingData.queues)) {
                    const servingQueues = servingData.queues.map((queue: any) => ({
                      ...queue,
                      statusDisplay: 'serving'
                    }));
                    console.log("Adding", servingQueues.length, "serving queues to upcoming array");
                    allQueues.push(...servingQueues);
                  }
                } else {
                  console.error("Failed to fetch serving queues:", servingResponse.status);
                }
              } catch (error) {
                console.error("Error fetching serving queues:", error);
              }
            }

            // Update the upcoming array with any serving queues we found
            data.queues.upcoming = allQueues;
          }
        }

        if (data.queues.cancelled && Array.isArray(data.queues.cancelled)) {
          data.queues.cancelled = data.queues.cancelled.map((queue: any) => ({
            ...queue,
            statusDisplay: 'cancelled'
          }));
        }

        // Fetch additional details for each queue
        const enhancedQueues = await enhanceQueueData(data.queues);
        setQueueData(enhancedQueues);
      }
    } catch (error) {
      console.error("Error fetching queue data:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Enhance queue data with service details and position
  const enhanceQueueData = async (queues: QueueData) => {
    const enhanced: QueueData = {
      upcoming: [],
      completed: [],
      cancelled: [],
    };

    // Process upcoming queues - get positions and service details
    if (queues.upcoming && queues.upcoming.length > 0) {
      // Log all positions before enhancement
      console.log("Positions before enhancement:", queues.upcoming.map(q => ({
        id: q.id,
        position: q.position,
        status: q.status
      })));

      enhanced.upcoming = await Promise.all(
        queues.upcoming.map(async (queue) => {
          try {
            // Log the initial position for this queue
            console.log(`Enhancing queue ${queue.id} with initial position: ${queue.position}`);
            // If uniqueSlotId is missing, try to fetch it from Redis via a specific endpoint
            if (!queue.uniqueSlotId) {
              try {
                const queueDetailResponse = await fetch(
                  `${API_BASE_URL}/customer/queue/${queue.id}`
                );
                if (queueDetailResponse.ok) {
                  const queueData = await queueDetailResponse.json();
                  if (
                    queueData.status === "success" &&
                    queueData.queue.uniqueSlotId
                  ) {
                    queue.uniqueSlotId = queueData.queue.uniqueSlotId;
                  } else {
                    // Generate a fallback ID if not found
                    queue.uniqueSlotId = Math.floor(1000 + Math.random() * 9000).toString();
                  }
                }
              } catch (error) {
                console.error("Error fetching queue details:", error);
                // Generate a fallback ID if fetch fails
                queue.uniqueSlotId = Math.floor(1000 + Math.random() * 9000).toString();
              }
            }

            // Get service details for address, contact info and images
            const serviceResponse = await fetch(
              `${API_BASE_URL}/partner/services/${queue.serviceId}`
            );
            let serviceDetails = {};

            if (serviceResponse.ok) {
              const serviceData = await serviceResponse.json();

              // Process images like in summary.tsx
              let processedImages = [];
              if (serviceData.images) {
                console.log("Service images:", serviceData.images);
                // Filter out any invalid URLs
                processedImages = Array.isArray(serviceData.images)
                  ? serviceData.images.filter(
                      (url: string) =>
                        typeof url === "string" && url.trim() !== ""
                    )
                  : [];
              }

              serviceDetails = {
                address: serviceData.address,
                contactPhone: serviceData.businessPhone || "",
                images: processedImages, // Store the processed images array
              };
            }

            // Get position in queue - using the same approach as queue-status.tsx
            let position: number | undefined = undefined;

            try {
              console.log(`Getting position for queue ${queue.id} (${queue.timeSlot})`);

              // First, try to get the full queue details from the backend
              // This is the same endpoint used in queue-status.tsx
              try {
                const queueDetailsResponse = await fetch(
                  `${API_BASE_URL}/customer/queue/${queue.id}`
                );

                if (queueDetailsResponse.ok) {
                  const queueDetailsData = await queueDetailsResponse.json();

                  if (queueDetailsData.status === "success" && queueDetailsData.queue) {
                    const queueDetails = queueDetailsData.queue;

                    // Check if position is available in the queue details
                    if (queueDetails.position !== undefined) {
                      console.log(`Got position from queue details: ${queueDetails.position}`);
                      position = queueDetails.position;

                      // Update other fields from queue details if available
                      const updatedQueue = {
                        ...queue,
                        ...serviceDetails,
                        position,
                        hasSubUnits: queueDetails.hasSubUnits || queue.hasSubUnits || false,
                        subUnitId: queueDetails.subUnitId || queue.subUnitId || '',
                        subUnitName: queueDetails.subUnitName || queue.subUnitName || '',
                        uniqueSlotId: queueDetails.uniqueSlotId || queue.uniqueSlotId,
                        isCheckedIn: queueDetails.isCheckedIn !== undefined ? queueDetails.isCheckedIn : (queueDetails.status === 'checked-in'),
                      };

                      console.log(`Queue ${queue.id} updated with position: ${position}`);
                      return updatedQueue;
                    }
                  }
                }
              } catch (error) {
                console.error("Error fetching queue details:", error);
                // Continue to fallback methods if queue details fetch fails
              }

              // If position is not in queue details, try to get it directly from the position endpoint
              try {
                const positionResponse = await fetch(
                  `${API_BASE_URL}/customer/queue/${queue.id}/position`
                );

                if (positionResponse.ok) {
                  const positionData = await positionResponse.json();

                  if (positionData.status === "success" && positionData.position !== undefined) {
                    console.log(`Got position from position endpoint: ${positionData.position}`);
                    position = positionData.position;
                    console.log(`Queue ${queue.id} position from position endpoint: ${position}`);

                    // Exit early if we got the position from the position endpoint
                    return {
                      ...queue,
                      ...serviceDetails,
                      position,
                      hasSubUnits: queue.hasSubUnits || false,
                      subUnitId: queue.subUnitId || '',
                      subUnitName: queue.subUnitName || '',
                    };
                  }
                }
              } catch (error) {
                console.error("Error fetching position from position endpoint:", error);
                // Continue to fallback method if position endpoint fetch fails
              }

              // Fallback to the active queues approach if Redis position is not available
              console.log("Falling back to active queues approach for position calculation");

              // Get all active queues for this service on this date
              const activeQueuesResponse = await fetch(
                `${API_BASE_URL}/customer/queues/active/${queue.serviceId}/${queue.date.split("T")[0]}`
              );

              if (activeQueuesResponse.ok) {
                const activeQueuesData = await activeQueuesResponse.json();

                if (activeQueuesData.status === "success" && activeQueuesData.queues) {
                  // Filter to get only queues for this specific time slot
                  // Active queues have status: waiting, checked-in, or serving (not completed, no-show, or cancelled)
                  const timeSlotQueues = activeQueuesData.queues.filter(
                    (q: any) =>
                      q.timeSlot === queue.timeSlot &&
                      (q.status === 'waiting' || q.status === 'checked-in' || q.status === 'serving')
                  );

                  console.log(`Found ${timeSlotQueues.length} active queues for time slot ${queue.timeSlot}`);

                  // For debugging
                  console.log("Active queues in this time slot:", timeSlotQueues.map((q: any) => ({
                    id: q.id,
                    isVIP: q.isVIP,
                    status: q.status,
                    position: q.position,
                    createdAt: q.createdAt
                  })));

                  // Sort queues by position first
                  const sortedQueues = [...timeSlotQueues].sort((a: any, b: any) => {
                    // First sort by position
                    const positionDiff = (a.position || 0) - (b.position || 0);

                    // If positions are the same, then consider VIP status
                    if (positionDiff === 0) {
                      if (a.isVIP && !b.isVIP) return -1;
                      if (!a.isVIP && b.isVIP) return 1;

                      // If VIP status is the same, sort by creation time
                      try {
                        const aTime = a.createdAt ? new Date(a.createdAt).getTime() : 0;
                        const bTime = b.createdAt ? new Date(b.createdAt).getTime() : 0;
                        return aTime - bTime;
                      } catch (error) {
                        return 0;
                      }
                    }

                    return positionDiff;
                  });

                  // Find user's position in the ordered queue (1-indexed)
                  const userPosition = sortedQueues.findIndex((q: any) => {
                    if (!q) return false;

                    // Make sure both IDs exist
                    const queueId = q.id || q._id;
                    const userQueueId = queue.id;

                    if (!queueId || !userQueueId) {
                      console.log(`Missing ID - userQueueId: ${userQueueId}, queueId: ${queueId}`);
                      return false;
                    }

                    // Compare stringified IDs
                    try {
                      const match = queueId.toString() === userQueueId.toString();
                      if (match) {
                        console.log(`Found queue position match at index ${userPosition}`);
                      }
                      return match;
                    } catch (error) {
                      console.error(`Error comparing queue IDs: ${error}`);
                      return false;
                    }
                  });

                  // Position is 1-indexed, not 0-indexed
                  position = userPosition !== -1 ? userPosition + 1 : undefined;
                  console.log(`Queue ${queue.id} position from sorted queues: ${position}`);

                  // If we couldn't determine position accurately, use a fallback
                  if (position === undefined) {
                    // Use the fallback approach with time slots API
                    const timeSlotResponse = await fetch(
                      `${API_BASE_URL}/partner/services/${queue.serviceId}/time-slots/${queue.date.split("T")[0]}`
                    );

                    if (timeSlotResponse.ok) {
                      const slotData = await timeSlotResponse.json();

                      if (slotData.status === "success" && slotData.timeSlots) {
                        const userSlot = slotData.timeSlots.find(
                          (slot: any) => slot.timeSlot === queue.timeSlot
                        );

                        if (userSlot) {
                          // This calculates total count, not position, but it's the best fallback
                          position = queue.isVIP
                            ? userSlot.vipQueueCount
                            : userSlot.vipQueueCount + userSlot.normalQueueCount;
                        }
                      }
                    }
                  }
                }
              } else {
                // Fallback to using time slots API if the active queues endpoint failed
                const timeSlotResponse = await fetch(
                  `${API_BASE_URL}/partner/services/${queue.serviceId}/time-slots/${queue.date.split("T")[0]}`
                );

                if (timeSlotResponse.ok) {
                  const slotData = await timeSlotResponse.json();

                  if (slotData.status === "success" && slotData.timeSlots) {
                    const userSlot = slotData.timeSlots.find(
                      (slot: any) => slot.timeSlot === queue.timeSlot
                    );

                    if (userSlot) {
                      position = queue.isVIP
                        ? userSlot.vipQueueCount
                        : userSlot.vipQueueCount + userSlot.normalQueueCount;
                    }
                  }
                }
              }
            } catch (error) {
              console.error("Error calculating position:", error);
              // If there's an error, return undefined position
              position = undefined;
            }

            // Make sure we preserve the subunit information
            return {
              ...queue,
              ...serviceDetails,
              position,
              hasSubUnits: queue.hasSubUnits || false,
              subUnitId: queue.subUnitId || '',
              subUnitName: queue.subUnitName || '',
            };
          } catch (error) {
            console.error("Error enhancing queue data:", error);
            return queue;
          }
        })
      );
    }

    // Process completed queues with images
    if (queues.completed && queues.completed.length > 0) {
      enhanced.completed = await Promise.all(
        queues.completed.map(async (queue) => {
          try {
            // Ensure uniqueSlotId exists
            if (!queue.uniqueSlotId) {
              queue.uniqueSlotId = `Q-${queue.id}-${Math.floor(1000 + Math.random() * 9000)}`;
            }

            const serviceResponse = await fetch(
              `${API_BASE_URL}/partner/services/${queue.serviceId}`
            );
            let serviceDetails = {};

            if (serviceResponse.ok) {
              const serviceData = await serviceResponse.json();

              // Process images like in summary.tsx
              let processedImages = [];
              if (serviceData.images) {
                // Filter out any invalid URLs
                processedImages = Array.isArray(serviceData.images)
                  ? serviceData.images.filter(
                      (url: string) =>
                        typeof url === "string" && url.trim() !== ""
                    )
                  : [];
              }

              serviceDetails = {
                address: serviceData.address,
                contactPhone: serviceData.businessPhone || "",
                images: processedImages, // Store the processed images array
              };
            }

            // Make sure we preserve the subunit information for completed queues
            return {
              ...queue,
              ...serviceDetails,
              hasSubUnits: queue.hasSubUnits || false,
              subUnitId: queue.subUnitId || '',
              subUnitName: queue.subUnitName || '',
            };
          } catch (error) {
            console.error("Error enhancing queue data:", error);
            return queue;
          }
        })
      );
    }

    // Process cancelled queues with images
    if (queues.cancelled && queues.cancelled.length > 0) {
      enhanced.cancelled = await Promise.all(
        queues.cancelled.map(async (queue) => {
          try {
            // Ensure uniqueSlotId exists
            if (!queue.uniqueSlotId) {
              queue.uniqueSlotId = `Q-${queue.id}-${Math.floor(1000 + Math.random() * 9000)}`;
            }

            const serviceResponse = await fetch(
              `${API_BASE_URL}/partner/services/${queue.serviceId}`
            );
            let serviceDetails = {};

            if (serviceResponse.ok) {
              const serviceData = await serviceResponse.json();

              // Process images like in summary.tsx
              let processedImages = [];
              if (serviceData.images) {
                // Filter out any invalid URLs
                processedImages = Array.isArray(serviceData.images)
                  ? serviceData.images.filter(
                      (url: string) =>
                        typeof url === "string" && url.trim() !== ""
                    )
                  : [];
              }

              serviceDetails = {
                address: serviceData.address,
                contactPhone: serviceData.businessPhone || "",
                images: processedImages, // Store the processed images array
              };
            }

            // Make sure we preserve the subunit information for cancelled queues
            return {
              ...queue,
              ...serviceDetails,
              hasSubUnits: queue.hasSubUnits || false,
              subUnitId: queue.subUnitId || '',
              subUnitName: queue.subUnitName || '',
            };
          } catch (error) {
            console.error("Error enhancing queue data:", error);
            return queue;
          }
        })
      );
    }

    return enhanced;
  };

  // Refresh queue data
  const onRefresh = () => {
    setRefreshing(true);
    fetchQueues();
  };

  // Format date string to readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  // Get directions to the service
  const getDirections = (queue: QueueItem) => {
    if (!queue.address?.coordinates) {
      Alert.alert("Error", "Location coordinates not available");
      return;
    }

    const { latitude, longitude } = queue.address.coordinates;
    const destination = queue.address.googleMapsLink
      ? queue.address.googleMapsLink
      : `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`;

    Linking.openURL(destination).catch((err) =>
      Alert.alert("Error", "Could not open map application")
    );
  };

  // Make a call to the service
  const makeCall = (queue: QueueItem) => {
    if (!queue.contactPhone) {
      Alert.alert("Error", "Phone number not available");
      return;
    }

    Linking.openURL(`tel:${queue.contactPhone}`).catch((err) =>
      Alert.alert("Error", "Could not open phone application")
    );
  };

  // Handle reschedule
  const handleReschedule = (queue: QueueItem) => {
    // Note: Since this route doesn't exist yet, we'll just log for now and use service details
    console.log("Reschedule queue:", queue.id);
    router.push({
      pathname: "/(root)/service-details",
      params: {
        id: queue.serviceId.toString(),
        action: "reschedule",
        queueId: queue.id.toString(),
      },
    });
  };

  // Handle view queue
  const handleViewQueue = (queue: QueueItem) => {
    // Note: Since this route doesn't exist yet, we'll just log for now and use service details
    console.log("View queue:", queue.id);
    router.push({
      pathname: "/(root)/service-details",
      params: {
        id: queue.serviceId.toString()
      }
    });
  };

  // Handle queue item press
  const handleQueuePress = (queueId: number, serviceId: number, statusDisplay?: string) => {
    // For completed or cancelled queues, redirect to queue-completed screen
    if (activeTab === "completed" || activeTab === "cancelled") {
      // Determine the actual status (no-show, completed, cancelled)
      let status: "completed" | "cancelled" | "no-show" = activeTab as "completed" | "cancelled";
      if (activeTab === "completed" && statusDisplay === "no-show") {
        status = "no-show";
      }

      router.push({
        pathname: "/(root)/queue-completed",
        params: {
          queueId: queueId.toString(),
          status: status
        }
      });
    } else {
      // For upcoming queues, continue to use queue-status screen
      router.push({
        pathname: "/(root)/queue-status",
        params: {
          queueId: queueId.toString(),
          serviceId: serviceId.toString()
        }
      });
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchQueues();
  }, [user]);

  // Render queue item
  const renderQueueItem = ({ item }: { item: QueueItem }) => {
    // Log queue information for debugging
    console.log(`Rendering queue item ${item.id}: position=${item.position}, status=${item.status}`);

    // Log subunit information for debugging
    if (item.hasSubUnits || item.subUnitId || item.subUnitName) {
      console.log(`Queue ${item.id} has subunit: ${item.subUnitName} (ID: ${item.subUnitId}, hasSubUnits: ${item.hasSubUnits})`);
    }

    // Handle image source safely
    const getImageSource = () => {
      // Check if images array exists and has at least one valid image
      if (
        item.images &&
        Array.isArray(item.images) &&
        item.images.length > 0 &&
        item.images[0]
      ) {

        return { uri: item.images[0] };
      }
      // Fallback to placeholder image
      return images.image;
    };

    return (
      <TouchableOpacity
        className="bg-white rounded-3xl border border-primary-200 overflow-hidden mb-8 drop-shadow-lg shadow-lg shadow-primary-300"
        onPress={() => handleQueuePress(item.id, item.serviceId, item.statusDisplay)}
        activeOpacity={0.7}
      >
        <View className="p-4 w-full">
          <View className="mb-6 flex-row flex-wrap justify-between mx-4 border-b border-secondary-600/20 py-2">
            <View className="flex-row items-center mr-2 mb-2">
              <Text className="text-secondary-500 text-sm">
                {formatDate(item.date)},{" "}
              </Text>
              <Text className="text-secondary-500 text-sm">
                {item.timeSlot}
              </Text>
              {/* Display subunit name if available (for all tabs) */}
              {item.hasSubUnits && item.subUnitName && (
                  <View className="bg-primary-50 px-2 py-1 rounded-lg">
                    <Text className="text-secondary-500 text-sm font-poppins-regular">
                     |  {item.subUnitName}
                    </Text>
                  </View>
                )}
            </View>
            <View className="flex-row items-center mb-2">
              <Image
                source={images.queueblue}
                className="w-5 h-4 mr-2"
                tintColor="#159AFF"
              />
              <Text className="text-primary-500 font-poppins-medium text-sm">
                ID: {item.uniqueSlotId}
              </Text>
            </View>
          </View>
          {/* Header Section with Image */}
          <View className="flex-row items-center mx-4 mb-4">
            {/* Service Image */}
            <Image
              source={getImageSource()}
              className="w-16 h-16 rounded-xl mr-3"
              style={{ backgroundColor: "#f5f5f5" }}
            />

            <View className="flex-1">
              <View className="flex-row justify-between items-start">
                <Text className="font-poppins-medium text-lg flex-1">
                  {item.serviceName}
                </Text>
              </View>
              <View className="flex-row items-center flex-wrap">


                {activeTab === "upcoming" ? (
                <>
                <Image
                  source={images.run}
                  className="w-3 h-4 mr-2"
                  tintColor="#159AFF"
                />
                <View >
                  <Text className="text-primary-500 text-sm">Current Spot: </Text>
                </View>

                <Text className="text-primary-500 font-poppins-medium text-base">
                  {item.position !== undefined ? item.position : "Loading..."}
                </Text>
                
                {/* Show a refresh button if position is not available */}
                {item.position === undefined && (
                  <TouchableOpacity
                    onPress={onRefresh}
                    className="ml-2 px-2 py-1 rounded-full bg-primary-100"
                  >
                    <Text className="text-xs text-primary-600">Refresh</Text>
                  </TouchableOpacity>
                )}
                {item.statusDisplay && item.statusDisplay !== 'waiting' && (
                  <View className={`ml-2 px-2 py-1 rounded-full ${
                    item.statusDisplay === 'checked-in'
                      ? 'bg-primary-100'
                      : item.statusDisplay === 'serving'
                        ? 'bg-green-100'
                        : 'bg-primary-100'
                  }`}>
                    <Text className={`text-xs font-poppins-medium ${
                      item.statusDisplay === 'checked-in'
                        ? 'text-primary-600'
                        : item.statusDisplay === 'serving'
                          ? 'text-green-600'
                          : 'text-primary-600'
                    }`}>
                      {item.statusDisplay === 'checked-in'
                        ? 'Checked In'
                        : item.statusDisplay === 'serving'
                          ? 'Now Serving'
                          : item.statusDisplay}
                    </Text>
                  </View>
                )}
                </>):(
                  <>
                  <Text className="text-secondary-600 text-sm">{item.serviceType}</Text>
                  {activeTab === "completed" && item.statusDisplay && (
                    <View className={`ml-2 px-2 py-1 rounded-full ${item.statusDisplay === 'no-show' ? 'bg-red-100' : 'bg-green-100'}`}>
                      <Text className={`text-xs font-poppins-medium ${item.statusDisplay === 'no-show' ? 'text-red-600' : 'text-green-600'}`}>
                        {item.statusDisplay === 'no-show' ? 'No Show' : 'Completed'}
                      </Text>
                    </View>
                  )}
                  {activeTab === "cancelled" && (
                    <View className="ml-2 px-2 py-1 rounded-full bg-warning-100">
                      <Text className="text-warning-500 text-xs font-poppins-medium">
                        Cancelled
                      </Text>
                    </View>
                  )}
                  </>
                )}

              </View>
            </View>
            <View className="flex-row">
              {activeTab === "upcoming" ? (
                <>
                  <TouchableOpacity
                    className="mr-4 h-12 w-12 rounded-full flex-row justify-center items-center border border-primary-500"
                    onPress={() => makeCall(item)}
                  >
                    <Image
                      source={images.call}
                      className="w-5 h-5"
                      tintColor="#159AFF"
                    />
                  </TouchableOpacity>

                  <TouchableOpacity
                    className=" h-12 w-12 rounded-full flex-row justify-center items-center border border-primary-500"
                    onPress={() => getDirections(item)}
                  >
                    <Image
                      source={images.direction}
                      className="w-6 h-6"
                      tintColor="#159AFF"
                    />
                  </TouchableOpacity>
                </>
              ) : (
                <>
                  <TouchableOpacity
                    className="mr-4 h-12 w-12 rounded-full flex-row justify-center items-center border border-primary-500"
                    onPress={() => makeCall(item)}
                  >
                    <Image
                      source={images.call}
                      className="w-5 h-5"
                      tintColor="#159AFF"
                    />
                  </TouchableOpacity>

                  {activeTab === "completed" && (
                    <TouchableOpacity
                      className=" h-12 w-12 rounded-full flex-row justify-center items-center border border-primary-500"
                      onPress={() =>
                        router.push({
                          pathname: "/(root)/service-details",
                          params: {
                            id: item.serviceId.toString(),
                            action: "review",
                          },
                        })
                      }
                    >
                      <Image
                        source={images.star}
                        className="w-5 h-5 "
                        tintColor="#159AFF"
                      />
                    </TouchableOpacity>
                  )}

                  {activeTab === "cancelled" && (
                    <TouchableOpacity
                      className=" h-12 w-12 rounded-full flex-row justify-center items-center border border-primary-500"
                      onPress={() =>
                        router.push({
                          pathname: "/(root)/join-queue",
                          params: { serviceId: item.serviceId.toString() },
                        })
                      }
                    >
                      <Image
                        source={images.history}
                        className="w-5 h-5 "
                        tintColor="#159AFF"
                      />
                    </TouchableOpacity>
                  )}
                </>
              )}
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Render empty state
  const renderEmptyState = () => {
    return (
      <View className="flex-1 items-center justify-center p-8">
        <Text className="text-sm text-gray-500 text-center font-poppins-regular">
          {activeTab === "upcoming"
            ? "You don't have any upcoming queues"
            : activeTab === "completed"
              ? "You don't have any completed queues"
              : "You don't have any cancelled queues"}
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      {/* Header */}
      <View className="bg-white p-4 flex justify-center w-full items-center pt-10 ">
        <Text className="text-2xl font-poppins-medium">My Queues</Text>
      </View>

      {/* Tab Navigation */}
      <View className="flex-row bg-white border-b mt-6 border-gray-200">
        <TouchableOpacity
          className={`flex-1  justify-center items-center`}
          onPress={() => setActiveTab("upcoming")}
        >
          <Text
            className={`text-center  ${activeTab === "upcoming" ? "text-primary-500 font-poppins-medium" : "text-secondary-600 font-poppins-regular"}`}
          >
            Upcoming
          </Text>
          <View
            className={`mt-4 rounded-t-full ${activeTab === "upcoming" ? "bg-primary-500 h-1 w-32 round" : ""}`}
          />
        </TouchableOpacity>

        <TouchableOpacity
          className={`flex-1 justify-center items-center`}
          onPress={() => setActiveTab("completed")}
        >
          <Text
            className={`text-center  ${activeTab === "completed" ? "text-primary-500 font-poppins-medium" : "text-secondary-600 font-poppins-regular"}`}
          >
            Completed
          </Text>
          <View
            className={`mt-4 rounded-t-full ${activeTab === "completed" ? "bg-primary-500 h-1 w-32 round" : ""}`}
          />
        </TouchableOpacity>

        <TouchableOpacity
          className={`flex-1 justify-center items-center`}
          onPress={() => setActiveTab("cancelled")}
        >
          <Text
            className={`text-center  ${activeTab === "cancelled" ? "text-primary-500 font-poppins-medium" : "text-secondary-600 font-poppins-regular"}`}
          >
            Cancelled
          </Text>
          <View
            className={`mt-4 rounded-t-full ${activeTab === "cancelled" ? "bg-primary-500 h-1 w-32 round" : ""}`}
          />
        </TouchableOpacity>
      </View>

      {/* Content Section */}
      {loading && !refreshing ? (
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#159AFF" />
        </View>
      ) : (
        <FlatList
          data={queueData[activeTab]}
          renderItem={renderQueueItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={{ padding: 16, paddingBottom: 120 }} // Added extra padding at bottom
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={["#159AFF"]}
            />
          }
        />
      )}
    </SafeAreaView>
  );
};

export default QueuesScreen;
