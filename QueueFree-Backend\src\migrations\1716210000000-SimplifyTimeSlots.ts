import { MigrationInterface, QueryRunner } from 'typeorm';

export class SimplifyTimeSlots1716210000000 implements MigrationInterface {
  name = 'SimplifyTimeSlots1716210000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('Starting migration to simplify time slots...');
    
    // 1. Get all service setups
    const serviceSetups = await queryRunner.query(`SELECT id, "setupData" FROM service_setups`);
    
    for (const setup of serviceSetups) {
      try {
        const setupData = setup.setupData;
        let modified = false;
        
        // Check if it's a regular service (no subunits)
        if (!setupData.hasSubUnits) {
          // Initialize availableHours if it doesn't exist
          if (!setupData.availableHours) {
            setupData.availableHours = {};
            modified = true;
          }
          
          // Convert from timeSlotsByDay if it exists
          if (setupData.timeSlotsByDay && Object.keys(setupData.timeSlotsByDay).length > 0) {
            for (const day in setupData.timeSlotsByDay) {
              if (Array.isArray(setupData.timeSlotsByDay[day])) {
                setupData.availableHours[day] = setupData.timeSlotsByDay[day];
                modified = true;
              }
            }
          }
          
          // Convert from timeSlots if it exists but no day-specific slots
          if (Array.isArray(setupData.timeSlots) && setupData.timeSlots.length > 0 && 
              (!setupData.availableHours || Object.keys(setupData.availableHours).length === 0)) {
            // Assign the same time slots to all selected days
            if (Array.isArray(setupData.selectedDays)) {
              setupData.selectedDays.forEach(day => {
                setupData.availableHours[day] = setupData.timeSlots;
              });
              modified = true;
            }
          }
        }
        
        // Check for subunits
        if (setupData.hasSubUnits && Array.isArray(setupData.subUnits)) {
          for (let i = 0; i < setupData.subUnits.length; i++) {
            const unit = setupData.subUnits[i];
            
            // Initialize availableHours if it doesn't exist
            if (!unit.availableHours) {
              unit.availableHours = {};
              modified = true;
            }
            
            // Convert from dayWiseAvailableHours if it exists
            if (unit.dayWiseAvailableHours && Object.keys(unit.dayWiseAvailableHours).length > 0) {
              for (const day in unit.dayWiseAvailableHours) {
                if (Array.isArray(unit.dayWiseAvailableHours[day])) {
                  unit.availableHours[day] = unit.dayWiseAvailableHours[day];
                  modified = true;
                }
              }
            }
            
            // Convert from timeSlots if it exists but no day-specific slots
            if (Array.isArray(unit.timeSlots) && unit.timeSlots.length > 0 && 
                (!unit.availableHours || Object.keys(unit.availableHours).length === 0)) {
              // Assign the same time slots to all selected days
              if (Array.isArray(unit.selectedDays)) {
                unit.selectedDays.forEach(day => {
                  unit.availableHours[day] = unit.timeSlots;
                });
                modified = true;
              }
            }
          }
        }
        
        // Save the modified setup data if changes were made
        if (modified) {
          await queryRunner.query(
            `UPDATE service_setups SET "setupData" = $1 WHERE id = $2`,
            [JSON.stringify(setupData), setup.id]
          );
          console.log(`Updated service setup with ID ${setup.id}`);
        }
      } catch (error) {
        console.error(`Error processing service setup with ID ${setup.id}:`, error);
      }
    }
    
    console.log('Completed migration to simplify time slots');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('No down migration implemented for time slots simplification');
    // This migration is not reversible because it consolidates data
  }
} 