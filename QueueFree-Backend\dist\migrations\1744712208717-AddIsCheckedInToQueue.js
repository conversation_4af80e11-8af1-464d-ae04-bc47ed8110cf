"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddIsCheckedInToQueue1744712208717 = void 0;
class AddIsCheckedInToQueue1744712208717 {
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE queues ADD COLUMN is_checked_in BOOLEAN NOT NULL DEFAULT false`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE queues DROP COLUMN is_checked_in`);
    }
}
exports.AddIsCheckedInToQueue1744712208717 = AddIsCheckedInToQueue1744712208717;
//# sourceMappingURL=1744712208717-AddIsCheckedInToQueue.js.map