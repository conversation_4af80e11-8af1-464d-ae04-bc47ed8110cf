import { Controller, Post, Body, Get, Param, Put, BadRequestException, NotFoundException, InternalServerErrorException, Query, HttpException, HttpStatus } from '@nestjs/common';
import { PartnerService } from './partner.service';
import { SchedulerService } from '../services/scheduler/scheduler.service';
import { Logger } from '@nestjs/common';
import { QueueFlowService } from '../services/queue-flow/queue-flow.service';
import { In } from 'typeorm';

@Controller('partner')
export class PartnerController {
  private readonly logger = new Logger(PartnerController.name);

  constructor(
    private readonly partnerService: PartnerService,
    private readonly schedulerService: SchedulerService,
    private readonly queueFlowService: QueueFlowService
  ) {}

  @Post('register')
  async registerPartner(@Body() body: { email: string }) {
    return this.partnerService.createPartner(body.email);
  }

  @Post('register-service')
  async registerService(@Body() serviceData: any) {
    return this.partnerService.registerService(serviceData);
  }

  @Get('service-status/:serviceId')
  async getServiceStatus(@Param('serviceId') serviceId: string) {
    try {
      // Sanitize and validate serviceId
      const cleanId = serviceId.replace(/[^0-9]/g, '');
      const id = parseInt(cleanId, 10);

      if (!cleanId || isNaN(id) || id <= 0) {
        throw new BadRequestException('Invalid service ID format');
      }

      const result = await this.partnerService.getServiceStatus(id);
      return result;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException(error.message);
      }
      if (error instanceof BadRequestException) {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException(
        error?.message || 'Failed to fetch service status'
      );
    }
  }

  @Post('service-setup/:serviceId')
  async saveServiceSetup(@Param('serviceId') serviceId: string, @Body() setupData: any) {
    return this.partnerService.saveServiceSetup(parseInt(serviceId), setupData);
  }

  @Get('service-setup/:serviceId')
  async getServiceSetup(@Param('serviceId') serviceId: string) {
    try {
      const id = parseInt(serviceId, 10);
      if (isNaN(id)) {
        throw new BadRequestException('Invalid service ID format');
      }
      const result = await this.partnerService.getServiceSetup(id);
      return result;
    } catch (error) {
      console.error('Error in getServiceSetup:', error);
      if (error instanceof NotFoundException) {
        throw new NotFoundException(error.message);
      }
      throw new BadRequestException(error.message || 'Failed to get service setup');
    }
  }

  @Get('service-details/:email')
  async getServiceDetails(@Param('email') email: string) {
    return this.partnerService.getServiceDetailsByEmail(email);
  }

  @Put('update-service')
  async updateService(@Body() serviceData: any) {
    return this.partnerService.updateService(serviceData);
  }

  @Get('service-completion/:email')
  async checkServiceCompletion(@Param('email') email: string) {
    return this.partnerService.checkServiceCompletion(email);
  }

  @Put('accept-terms/:email')
  async acceptTerms(@Param('email') email: string) {
    return this.partnerService.acceptTerms(email);
  }

  @Get('verification-status/:email')
  async getVerificationStatusByEmail(@Param('email') email: string) {
    try {
      const service = await this.partnerService.getVerificationStatusByEmail(email);
      return {
        success: true,
        status: service.verificationStatus || 'pending'
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException(error.message);
      }
      throw new BadRequestException('Failed to get verification status');
    }
  }

  @Put('toggle-service/:email')
  async toggleServiceStatus(
    @Param('email') email: string,
    @Body() data: { isOpen: boolean }
  ) {
    return this.partnerService.toggleServiceStatus(email, data.isOpen);
  }

  @Get('service-status/:email')
  async getServiceOpenStatus(@Param('email') email: string) {
    try {
      const service = await this.partnerService.getServiceOpenStatus(email);
      return {
        success: true,
        isOpen: service.isOpen
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException(error.message);
      }
      throw new BadRequestException('Failed to get service status');
    }
  }

  @Post('bank-details/:serviceId')
  async saveBankDetails(
    @Param('serviceId') serviceId: string,
    @Body() bankDetails: any
  ) {
    return this.partnerService.saveBankDetails(parseInt(serviceId), bankDetails);
  }

  @Get('bank-details/:serviceId')
  async getBankDetails(@Param('serviceId') serviceId: string) {
    return this.partnerService.getBankDetails(parseInt(serviceId));
  }

  @Post('leave-days/:serviceId')
  async saveLeaveDays(
    @Param('serviceId') serviceId: string,
    @Body() leaveDays: any
  ) {
    return this.partnerService.saveLeaveDays(parseInt(serviceId), leaveDays);
  }

  @Get('leave-days/:serviceId')
  async getLeaveDays(@Param('serviceId') serviceId: string) {
    return this.partnerService.getLeaveDays(parseInt(serviceId));
  }

  @Get('services')
  async getAllServices() {
    return this.partnerService.getAllServices();
  }

  @Get('services/:id')
  async getServiceById(@Param('id') id: string) {
    try {
      // Sanitize and validate id
      const cleanId = id.replace(/[^0-9]/g, '');
      const serviceId = parseInt(cleanId, 10);

      if (!cleanId || isNaN(serviceId) || serviceId <= 0) {
        throw new BadRequestException('Invalid service ID format');
      }

      const result = await this.partnerService.getServiceById(serviceId);
      return result;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException(error.message);
      }
      if (error instanceof BadRequestException) {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException(
        error?.message || 'Failed to fetch service details'
      );
    }
  }

  @Get('services/:id/reviews')
  async getServiceReviews(
    @Param('id') id: string,
    @Query('limit') limit: string = '10',
    @Query('offset') offset: string = '0'
  ) {
    try {
      // Sanitize and validate id
      const cleanId = id.replace(/[^0-9]/g, '');
      const serviceId = parseInt(cleanId, 10);

      if (!cleanId || isNaN(serviceId) || serviceId <= 0) {
        throw new BadRequestException('Invalid service ID format');
      }

      const limitNum = parseInt(limit, 10) || 10;
      const offsetNum = parseInt(offset, 10) || 0;

      const result = await this.partnerService.getServiceReviews(serviceId, limitNum, offsetNum);
      return result;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException(error.message);
      }
      if (error instanceof BadRequestException) {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException(
        error?.message || 'Failed to fetch service reviews'
      );
    }
  }

  @Post('services/:id/reviews')
  async addReview(
    @Param('id') id: string,
    @Body() reviewData: {
      userId: string;
      userName: string;
      userProfilePic?: string;
      rating: number;
      comment: string;
    }
  ) {
    try {
      // Sanitize and validate id
      const cleanId = id.replace(/[^0-9]/g, '');
      const serviceId = parseInt(cleanId, 10);

      if (!cleanId || isNaN(serviceId) || serviceId <= 0) {
        throw new BadRequestException('Invalid service ID format');
      }

      // Validate review data
      if (!reviewData.userId || !reviewData.userName || !reviewData.rating || !reviewData.comment) {
        throw new BadRequestException('Missing required review data');
      }

      const result = await this.partnerService.addReview(serviceId, reviewData);
      return result;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException(error.message);
      }
      if (error instanceof BadRequestException) {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException(
        error?.message || 'Failed to add review'
      );
    }
  }

  @Get('services/:id/time-slots/:date')
  async getServiceTimeSlots(
    @Param('id') id: string,
    @Param('date') date: string,
    @Query('subUnitId') subUnitId?: string
  ) {
    try {
      // Sanitize and validate id
      const cleanId = id.replace(/[^0-9]/g, '');
      const serviceId = parseInt(cleanId, 10);

      if (!cleanId || isNaN(serviceId) || serviceId <= 0) {
        throw new BadRequestException('Invalid service ID format');
      }

      // Validate date format (YYYY-MM-DD)
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(date)) {
        throw new BadRequestException('Invalid date format. Use YYYY-MM-DD');
      }

      // Parse subUnitId if provided
      let subUnitIdNum: number | undefined = undefined;
      if (subUnitId) {
        subUnitIdNum = parseInt(subUnitId, 10);
        if (isNaN(subUnitIdNum)) {
          throw new BadRequestException('Invalid subUnitId format');
        }
        console.log(`Using subUnitId: ${subUnitIdNum} for time slots request`);
      }

      const result = await this.partnerService.getServiceTimeSlots(serviceId, date, subUnitIdNum);
      return result;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException(error.message);
      }
      if (error instanceof BadRequestException) {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException(
        error?.message || 'Failed to fetch time slots'
      );
    }
  }

  @Get('test-service-setup/:serviceId')
  async testServiceSetup(@Param('serviceId') serviceId: string) {
    try {
      const id = parseInt(serviceId, 10);
      if (isNaN(id)) {
        throw new BadRequestException('Invalid service ID format');
      }

      const result = await this.partnerService.getServiceSetup(id);

      // Add leave days to the response
      const leaveDaysResult = await this.partnerService.getLeaveDays(id);

      return {
        status: 'success',
        serviceId: id,
        setupData: result.data,
        leaveDays: leaveDaysResult.data
      };
    } catch (error) {
      console.error('Error in testServiceSetup:', error);
      throw new BadRequestException(error.message || 'Failed to test service setup');
    }
  }

  @Put('queues/:queueId/complete')
  async completeQueue(@Param('queueId') queueId: string) {
    try {
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum) || queueIdNum <= 0) {
        throw new BadRequestException('Invalid queue ID format');
      }

      // First get the queue from database to ensure we have all data
      const queue = await this.partnerService['queueRepository'].findOne({
        where: { id: queueIdNum },
        relations: ['service']
      });

      if (!queue) {
        throw new NotFoundException(`Queue with ID ${queueIdNum} not found`);
      }

      // Get all queues for this service, date, and time slot (both waiting and serving)
      const allQueues = await this.partnerService['queueRepository'].find({
        where: {
          serviceId: queue.serviceId,
          date: queue.date,
          timeSlot: queue.timeSlot,
          status: In(['waiting', 'serving']),
        },
        order: {
          isVIP: 'DESC' as const, // VIP first
          position: 'ASC' as const, // Then by position
          createdAt: 'ASC' as const // Then by creation time
        }
      });

      // Separate waiting and serving queues
      const servingQueues = allQueues.filter(q => q.status === 'serving');
      const waitingQueues = allQueues.filter(q => q.status === 'waiting');

      this.logger.log(`Found ${servingQueues.length} serving queues and ${waitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);

      // Update the queue status in the database
      queue.status = 'completed';
      queue.statusUpdatedAt = new Date();
      // Set position to -1 to indicate it's no longer in the queue
      queue.position = -1;
      const updatedQueue = await this.partnerService['queueRepository'].save(queue);

      // Update Redis for the completed queue
      const redisService = this.partnerService['redisService'];

      // Get the current queue data from Redis
      const queueData = await redisService.getQueue(queueIdNum.toString()) || {};

      // Update the status and position
      queueData.status = 'completed';
      queueData.position = -1;

      // Save the updated queue data to Redis
      await redisService.saveQueue(queueIdNum.toString(), queueData);

      // Also update the status using the dedicated method
      await this.partnerService.updateQueueStatus(queueIdNum, 'completed', true);

      // Reorder positions for all remaining waiting queues, regardless of the completed queue's status
      // This ensures positions are always reordered when a queue is completed

      // Filter out the completed queue and any queues that are not in waiting status
      const remainingWaitingQueues = waitingQueues.filter(q => q.id !== queueIdNum);

      // Reassign positions to all remaining waiting queues
      const positions: Record<string, number> = {};
      let position = 0;

      // Process VIP queues first, then non-VIP queues
      const vipQueues = remainingWaitingQueues.filter(q => q.isVIP);
      const normalQueues = remainingWaitingQueues.filter(q => !q.isVIP);

      this.logger.log(`Reordering positions: ${vipQueues.length} VIP queues and ${normalQueues.length} normal queues`);

      // Process VIP queues first
      for (const q of vipQueues) {
        position++;
        q.position = position;
        positions[q.id] = position;

        // Update Redis for this queue
        const qData = await redisService.getQueue(q.id.toString()) || {};
        qData.position = position;
        await redisService.saveQueue(q.id.toString(), qData);
      }

      // Then process normal queues
      for (const q of normalQueues) {
        position++;
        q.position = position;
        positions[q.id] = position;

        // Update Redis for this queue
        const qData = await redisService.getQueue(q.id.toString()) || {};
        qData.position = position;
        await redisService.saveQueue(q.id.toString(), qData);
      }

      // Combine VIP and normal queues for saving
      const allRemainingQueues = [...vipQueues, ...normalQueues];
      this.logger.log(`Reordered positions for ${allRemainingQueues.length} queues after completing queue ${queueIdNum}`);

      // Save all queues to database
      if (allRemainingQueues.length > 0) {
        await this.partnerService['queueRepository'].save(allRemainingQueues);
      }

      // Save all positions to Redis and invalidate cache
      const dateStr = new Date(queue.date).toISOString().split('T')[0];

      // Create positions object using the positions we just assigned
      const positionsToSave: Record<string, number> = positions;

      await redisService.saveQueuePosition(
        queue.serviceId.toString(),
        dateStr,
        queue.timeSlot,
        positionsToSave
      );

      // Invalidate any cached queue positions
      await redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);

      // Invalidate service queues cache
      await redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
      await redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');

      // Recalculate estimated serve times for all remaining queues
      await this.queueFlowService.recalculateEstimatedServeTimes(
        queue.serviceId,
        queue.date,
        queue.timeSlot
      );

      this.logger.log(`Recalculated estimated serve times for all queues after completing queue ${queueIdNum}`);

      return {
        status: 'success',
        message: 'Queue marked as completed and positions reordered',
        data: updatedQueue
      };
    } catch (error) {
      this.logger.error(`Error completing queue: ${error.message}`, error.stack);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to mark queue as completed',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Put('queues/:queueId/confirm-presence')
  async confirmPresence(
    @Param('queueId') queueId: string,
    @Body() body: { isPresent: boolean }
  ) {
    try {
      // Sanitize and validate queueId
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum) || queueIdNum <= 0) {
        throw new BadRequestException('Invalid queue ID format');
      }

      // Use the QueueFlowService directly to handle presence confirmation
      const updatedQueue = await this.queueFlowService.handlePresenceConfirmation(
        queueIdNum,
        body.isPresent
      );

      return {
        status: 'success',
        message: body.isPresent ? 'Presence confirmed' : 'Marked as not present',
        data: updatedQueue
      };
    } catch (error) {
      this.logger.error(`Error confirming presence: ${error.message}`, error.stack);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to confirm presence',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Put('queues/:queueId/cancel')
  async cancelQueue(@Param('queueId') queueId: string) {
    try {
      // Sanitize and validate queueId
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum) || queueIdNum <= 0) {
        throw new BadRequestException('Invalid queue ID format');
      }

      // First get the queue from database to ensure we have all data
      const queue = await this.partnerService['queueRepository'].findOne({
        where: { id: queueIdNum },
        relations: ['service']
      });

      if (!queue) {
        throw new NotFoundException(`Queue with ID ${queueIdNum} not found`);
      }

      // Get all queues for this service, date, and time slot (both waiting and serving)
      const allQueues = await this.partnerService['queueRepository'].find({
        where: {
          serviceId: queue.serviceId,
          date: queue.date,
          timeSlot: queue.timeSlot,
          status: In(['waiting', 'serving']),
        },
        order: {
          isVIP: 'DESC' as const, // VIP first
          position: 'ASC' as const, // Then by position
          createdAt: 'ASC' as const // Then by creation time
        }
      });

      // Separate waiting and serving queues
      const servingQueues = allQueues.filter(q => q.status === 'serving');
      const waitingQueues = allQueues.filter(q => q.status === 'waiting');

      this.logger.log(`Found ${servingQueues.length} serving queues and ${waitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);

      // Update the queue status in the database
      queue.status = 'cancelled';
      queue.statusUpdatedAt = new Date();
      // Set position to -1 to indicate it's no longer in the queue
      queue.position = -1;
      const updatedQueue = await this.partnerService['queueRepository'].save(queue);

      // Update Redis for the cancelled queue
      const redisService = this.partnerService['redisService'];

      // Get the current queue data from Redis
      const queueData = await redisService.getQueue(queueIdNum.toString()) || {};

      // Update the status and position
      queueData.status = 'cancelled';
      queueData.position = -1;

      // Save the updated queue data to Redis
      await redisService.saveQueue(queueIdNum.toString(), queueData);

      // Also update the status using the dedicated method
      await this.partnerService.updateQueueStatus(queueIdNum, 'cancelled', true);

      // Only reorder positions if the cancelled queue was in waiting status before cancellation
      if (queue.status === 'waiting' || queue.status === 'checked-in') {
        // Reorder positions for all remaining waiting queues
        // Filter out the cancelled queue
        const remainingWaitingQueues = waitingQueues.filter(q => q.id !== queueIdNum);

        // Reassign positions to all remaining waiting queues
        const positions: Record<string, number> = {};

        // Start position counter at 0 and increment for each queue
        let position = 0;

        // Process VIP queues first, then non-VIP queues
        const vipQueues = remainingWaitingQueues.filter(q => q.isVIP);
        const normalQueues = remainingWaitingQueues.filter(q => !q.isVIP);

        this.logger.log(`Reordering positions: ${vipQueues.length} VIP queues and ${normalQueues.length} normal queues`);

        // Process VIP queues first
        for (const q of vipQueues) {
          position++;
          q.position = position;
          positions[q.id] = position;

          // Update Redis for this queue
          const qData = await redisService.getQueue(q.id) || {};
          qData.position = position;
          await redisService.saveQueue(q.id.toString(), qData);
        }

        // Then process normal queues
        for (const q of normalQueues) {
          position++;
          q.position = position;
          positions[q.id] = position;

          // Update Redis for this queue
          const qData = await redisService.getQueue(q.id) || {};
          qData.position = position;
          await redisService.saveQueue(q.id.toString(), qData);
        }

        // Combine VIP and normal queues for saving
        const allRemainingQueues = [...vipQueues, ...normalQueues];
        this.logger.log(`Reordered positions for ${allRemainingQueues.length} queues after cancelling queue ${queueIdNum}`);

        // Save all queues to database
        if (allRemainingQueues.length > 0) {
          await this.partnerService['queueRepository'].save(allRemainingQueues);
        }
      } else {
        this.logger.log(`Queue ${queueIdNum} was not in waiting status, no need to reorder positions`);
      }

      // Save all positions to Redis and invalidate cache
      const dateStr = new Date(queue.date).toISOString().split('T')[0];

      // Create positions object if it doesn't exist (in case we didn't reorder)
      const positionsToSave: Record<string, number> = {};

      // If we reordered positions, use those positions
      if (queue.status === 'waiting' || queue.status === 'checked-in') {
        // Get all remaining queues with their positions
        // We need to declare these variables here to avoid scope issues
        let vipQueuesLocal: any[] = [];
        let normalQueuesLocal: any[] = [];

        // Get the remaining waiting queues
        const remainingWaitingQueues = waitingQueues.filter(q => q.id !== queueIdNum);

        // Process VIP queues first, then non-VIP queues
        vipQueuesLocal = remainingWaitingQueues.filter(q => q.isVIP);
        normalQueuesLocal = remainingWaitingQueues.filter(q => !q.isVIP);

        const allRemainingQueues = [...vipQueuesLocal, ...normalQueuesLocal];

        // Fill positions object
        for (const q of allRemainingQueues) {
          positionsToSave[q.id] = q.position;
        }
      }

      await redisService.saveQueuePosition(
        queue.serviceId.toString(),
        dateStr,
        queue.timeSlot,
        positionsToSave
      );

      // Invalidate any cached queue positions
      await redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);

      // Invalidate service queues cache
      await redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
      await redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');

      // Recalculate estimated serve times for all remaining queues
      await this.queueFlowService.recalculateEstimatedServeTimes(
        queue.serviceId,
        queue.date,
        queue.timeSlot
      );

      this.logger.log(`Recalculated estimated serve times for all queues after cancelling queue ${queueIdNum}`);

      return {
        status: 'success',
        message: 'Queue cancelled and positions reordered',
        data: updatedQueue
      };
    } catch (error) {
      this.logger.error(`Error cancelling queue: ${error.message}`, error.stack);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to cancel queue',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Put('queues/:queueId/no-show')
  async markQueueAsNoShow(@Param('queueId') queueId: string) {
    try {
      // Sanitize and validate queueId
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum) || queueIdNum <= 0) {
        throw new BadRequestException('Invalid queue ID format');
      }

      // First get the queue from database to ensure we have all data
      const queue = await this.partnerService['queueRepository'].findOne({
        where: { id: queueIdNum },
        relations: ['service']
      });

      if (!queue) {
        throw new NotFoundException(`Queue with ID ${queueIdNum} not found`);
      }

      // Get all queues for this service, date, and time slot (both waiting and serving)
      const allQueues = await this.partnerService['queueRepository'].find({
        where: {
          serviceId: queue.serviceId,
          date: queue.date,
          timeSlot: queue.timeSlot,
          status: In(['waiting', 'serving']),
        },
        order: {
          isVIP: 'DESC' as const, // VIP first
          position: 'ASC' as const, // Then by position
          createdAt: 'ASC' as const // Then by creation time
        }
      });

      // Separate waiting and serving queues
      const servingQueues = allQueues.filter(q => q.status === 'serving');
      const waitingQueues = allQueues.filter(q => q.status === 'waiting');

      this.logger.log(`Found ${servingQueues.length} serving queues and ${waitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);

      // Update the queue status in the database
      queue.status = 'no-show';
      queue.statusUpdatedAt = new Date();
      // Set position to -1 to indicate it's no longer in the queue
      queue.position = -1;
      const updatedQueue = await this.partnerService['queueRepository'].save(queue);

      // Update Redis for the no-show queue
      const redisService = this.partnerService['redisService'];

      // Get the current queue data from Redis
      const queueData = await redisService.getQueue(queueIdNum.toString()) || {};

      // Update the status and position
      queueData.status = 'no-show';
      queueData.position = -1;

      // Save the updated queue data to Redis
      await redisService.saveQueue(queueIdNum.toString(), queueData);

      console.log(`Updating queue ${queueIdNum} to no-show status using RedisService`);
      await this.partnerService.updateQueueStatus(queueIdNum, 'no-show', true); // Force update with true

      // Verify the update was successful
      const verifiedData = await redisService.getQueue(queueIdNum);
      console.log(`Verified queue ${queueIdNum} Redis status: ${verifiedData?.status}`);

      // Only reorder positions if the no-show queue was in waiting status before marking as no-show
      if (queue.status === 'waiting' || queue.status === 'checked-in') {
        // Reorder positions for all remaining waiting queues
        // Filter out the no-show queue
        const remainingWaitingQueues = waitingQueues.filter(q => q.id !== queueIdNum);

        // Reassign positions to all remaining waiting queues
        const positions: Record<string, number> = {};
        let position = 0;

        // Process VIP queues first, then non-VIP queues
        const vipQueues = remainingWaitingQueues.filter(q => q.isVIP);
        const normalQueues = remainingWaitingQueues.filter(q => !q.isVIP);

        this.logger.log(`Reordering positions: ${vipQueues.length} VIP queues and ${normalQueues.length} normal queues`);

        // Process VIP queues first
        for (const q of vipQueues) {
          position++;
          q.position = position;
          positions[q.id] = position;

          // Update Redis for this queue
          const qData = await redisService.getQueue(q.id) || {};
          qData.position = position;
          await redisService.saveQueue(q.id.toString(), qData);
        }

        // Then process normal queues
        for (const q of normalQueues) {
          position++;
          q.position = position;
          positions[q.id] = position;

          // Update Redis for this queue
          const qData = await redisService.getQueue(q.id) || {};
          qData.position = position;
          await redisService.saveQueue(q.id.toString(), qData);
        }

        // Combine VIP and normal queues for saving
        const allRemainingQueues = [...vipQueues, ...normalQueues];
        this.logger.log(`Reordered positions for ${allRemainingQueues.length} queues after marking queue ${queueIdNum} as no-show`);

        // Save all queues to database
        if (allRemainingQueues.length > 0) {
          await this.partnerService['queueRepository'].save(allRemainingQueues);
        }
      } else {
        this.logger.log(`Queue ${queueIdNum} was not in waiting status, no need to reorder positions`);
      }

      // Save all positions to Redis and invalidate cache
      const dateStr = new Date(queue.date).toISOString().split('T')[0];

      // Create positions object if it doesn't exist (in case we didn't reorder)
      const positionsToSave: Record<string, number> = {};

      // If we reordered positions, use those positions
      if (queue.status === 'waiting' || queue.status === 'checked-in') {
        // Get all remaining queues with their positions
        // We need to declare these variables here to avoid scope issues
        let vipQueuesLocal: any[] = [];
        let normalQueuesLocal: any[] = [];

        // Get the remaining waiting queues
        const remainingWaitingQueues = waitingQueues.filter(q => q.id !== queueIdNum);

        // Process VIP queues first, then non-VIP queues
        vipQueuesLocal = remainingWaitingQueues.filter(q => q.isVIP);
        normalQueuesLocal = remainingWaitingQueues.filter(q => !q.isVIP);

        const allRemainingQueues = [...vipQueuesLocal, ...normalQueuesLocal];

        // Fill positions object
        for (const q of allRemainingQueues) {
          positionsToSave[q.id] = q.position;
        }
      }

      await redisService.saveQueuePosition(
        queue.serviceId.toString(),
        dateStr,
        queue.timeSlot,
        positionsToSave
      );

      // Invalidate any cached queue positions
      await redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);

      // Invalidate service queues cache
      await redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
      await redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');

      // Recalculate estimated serve times for all remaining queues
      await this.queueFlowService.recalculateEstimatedServeTimes(
        queue.serviceId,
        queue.date,
        queue.timeSlot
      );

      this.logger.log(`Recalculated estimated serve times for all queues after marking queue ${queueIdNum} as no-show`);

      return {
        status: 'success',
        message: 'Queue marked as no-show and positions reordered',
        data: updatedQueue
      };
    } catch (error) {
      this.logger.error(`Error marking queue as no-show: ${error.message}`, error.stack);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to mark queue as no-show',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Put('queues/:queueId/move-to-end')
  async moveQueueToEnd(
    @Param('queueId') queueId: string
  ) {
    try {
      // Sanitize and validate queueId
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum) || queueIdNum <= 0) {
        throw new BadRequestException('Invalid queue ID format');
      }

      // First get the queue from database to ensure we have all data
      const queue = await this.partnerService['queueRepository'].findOne({
        where: { id: queueIdNum },
        relations: ['service']
      });

      if (!queue) {
        throw new NotFoundException(`Queue with ID ${queueIdNum} not found`);
      }

      // Get all queues for this service, date, and time slot
      // IMPORTANT: When getting all queues, we don't sort by VIP status
      // We want to maintain the current order of all queues except the one being moved
      const allQueuesForSlot = await this.partnerService['queueRepository'].find({
        where: {
          serviceId: queue.serviceId,
          date: queue.date,
          timeSlot: queue.timeSlot,
          status: 'waiting',
        },
        order: {
          position: 'ASC' as const, // Sort only by position
          createdAt: 'ASC' as const // Then by creation time
        }
      });

      this.logger.log(`Found ${allQueuesForSlot.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);

      // Remove the queue we're moving to the end
      const filteredQueues = allQueuesForSlot.filter(q => q.id !== queueIdNum);

      // Make sure the status is 'waiting'
      queue.status = 'waiting';
      // Reset any serving-related flags
      queue.currentlyServing = false;
      queue.servingStartedAt = null;
      queue.inGracePeriod = false;
      queue.graceStartedAt = null;
      queue.confirmedPresence = false;

      // Assign positions to all queues
      const redisService = this.partnerService['redisService'];
      const positionsToSave: Record<string, number> = {};
      let position = 0;

      // First, assign positions to all other queues
      // We maintain their current order, regardless of VIP status
      for (const q of filteredQueues) {
        position++;
        q.position = position;
        positionsToSave[q.id] = position;

        // Update Redis for this queue
        const qData = await redisService.getQueue(q.id) || {};
        qData.position = position;
        await redisService.saveQueue(q.id.toString(), qData);
      }

      // Then, assign the last position to the queue we're moving
      // This ensures it goes to the absolute end, regardless of VIP status
      position++;
      queue.position = position;
      positionsToSave[queue.id] = position;

      this.logger.log(`Moving queue ${queueIdNum} to end of line with new position ${position}`);

      // Save all queues to database
      await this.partnerService['queueRepository'].save(filteredQueues);
      const updatedQueue = await this.partnerService['queueRepository'].save(queue);

      // Update Redis cache for the moved queue
      let queueData = await redisService.getQueue(queueIdNum) || {};

      // Update with new position and status
      queueData = {
        ...queueData,
        position: position,
        status: 'waiting',
        currentlyServing: false,
        servingStartedAt: null,
        inGracePeriod: false,
        graceStartedAt: null,
        confirmedPresence: false,
        updatedAt: new Date().toISOString()
      };

      // Save to Redis
      await redisService.saveQueue(queueIdNum.toString(), queueData);

      // Save all positions to Redis and invalidate cache
      const dateStr = new Date(queue.date).toISOString().split('T')[0];
      await redisService.saveQueuePosition(
        queue.serviceId.toString(),
        dateStr,
        queue.timeSlot,
        positionsToSave
      );

      // Invalidate any cached queue positions
      await redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);

      // Invalidate service queues cache
      await redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
      await redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');

      // Recalculate estimated serve times for all queues in this service
      await this.queueFlowService.recalculateEstimatedServeTimes(
        queue.serviceId,
        queue.date,
        queue.timeSlot
      );

      this.logger.log(`Recalculated estimated serve times for all queues after moving queue ${queueIdNum} to end of line`);

      return {
        status: 'success',
        message: 'Queue moved to end of line',
        data: {
          ...updatedQueue,
          newPosition: position
        }
      };
    } catch (error) {
      console.error('Error moving queue to end of line:', error);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to move queue to end of line',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Put('queues/:queueId/start-serving')
  async startServingQueue(@Param('queueId') queueId: string) {
    try {
      // Sanitize and validate queueId
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum)) {
        throw new BadRequestException('Invalid queue ID');
      }

      // Use the QueueFlowService's dedicated startServing method for proper handling
      const result = await this.queueFlowService.startServing(queueIdNum);

      console.log(`Started serving queue ${queueIdNum}, servingStartedAt: ${result.servingStartedAt?.toISOString() || 'not set'}`);

      return {
        status: 'success',
        message: 'Started serving the queue member',
        data: result
      };
    } catch (error) {
      console.error(`Error starting service for queue ${queueId}:`, error);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to start serving queue',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Put('queues/:queueId/start-grace-period')
  async startGracePeriod(@Param('queueId') queueId: string) {
    try {
      // Sanitize and validate queueId
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum) || queueIdNum <= 0) {
        throw new BadRequestException('Invalid queue ID format');
      }

      // First get the queue to determine its service ID
      const queue = await this.partnerService['queueRepository'].findOne({
        where: { id: queueIdNum }
      });

      if (!queue) {
        throw new NotFoundException(`Queue with ID ${queueIdNum} not found`);
      }

      // Now use the scheduler service to start the grace period
      const result = await this.schedulerService.startGracePeriod(queueIdNum, queue.serviceId);

      return {
        status: 'success',
        message: 'Started grace period for queue member',
        data: result
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to start grace period',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queues/:queueId/grace-period-status')
  async getGracePeriodStatus(@Param('queueId') queueId: string) {
    try {
      // Sanitize and validate queueId
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum) || queueIdNum <= 0) {
        throw new BadRequestException('Invalid queue ID format');
      }

      const result = await this.partnerService.getGracePeriodStatus(queueIdNum);

      return {
        status: 'success',
        data: result
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to get grace period status',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queues/:queueId/estimated-wait-time')
  async getEstimatedWaitTime(@Param('queueId') queueId: string) {
    try {
      // Sanitize and validate queueId
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum)) {
        throw new BadRequestException('Invalid queue ID');
      }

      this.logger.log(`Retrieving estimated wait time for queue: ${queueId}`);

      // Use the QueueFlowService to get the estimated wait time
      const waitTimeData = await this.queueFlowService.getEstimatedWaitTime(queueIdNum);

      return {
        status: 'success',
        data: {
          queueId: queueIdNum,
          waitTimeMinutes: waitTimeData.waitTimeMinutes,
          waitTimeStatus: waitTimeData.waitTimeStatus,
          estimatedServeTime: waitTimeData.estimatedServeTime.toISOString()
        }
      };
    } catch (error) {
      this.logger.error(`Error getting estimated wait time for queue ${queueId}:`, error);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to get estimated wait time',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queues/:queueId/actual-service-time')
  async getActualServiceTime(@Param('queueId') queueId: string) {
    try {
      // Sanitize and validate queueId
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum)) {
        throw new BadRequestException('Invalid queue ID');
      }

      this.logger.log(`Retrieving actual service time for queue: ${queueId}`);

      // Use the QueueFlowService to get the actual service time
      const serviceTimeData = await this.queueFlowService.getActualServiceTime(queueIdNum);

      return {
        status: 'success',
        data: {
          queueId: queueIdNum,
          serviceTimeMinutes: serviceTimeData.serviceTimeMinutes,
          servingStartedAt: serviceTimeData.servingStartedAt?.toISOString() || null,
          statusUpdatedAt: serviceTimeData.statusUpdatedAt?.toISOString() || null,
          status: serviceTimeData.status
        }
      };
    } catch (error) {
      this.logger.error(`Error getting actual service time for queue ${queueId}:`, error);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to get actual service time',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Put('queues/:queueId/move-to-end')
  async moveQueueToEndOfLine(@Param('queueId') queueId: string) {
    try {
      // Sanitize and validate queueId
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum)) {
        throw new BadRequestException('Invalid queue ID');
      }

      this.logger.log(`Moving queue ${queueId} to the end of the line`);

      // Use the QueueFlowService to move the queue to the end of the line
      const updatedQueue = await this.queueFlowService.moveToEndOfLine(queueIdNum);

      return {
        status: 'success',
        message: 'Queue moved to the end of the line',
        data: {
          queueId: updatedQueue.id,
          position: updatedQueue.position,
          status: updatedQueue.status,
          estimatedServeTime: updatedQueue.estimatedServeTime?.toISOString() || null
        }
      };
    } catch (error) {
      this.logger.error(`Error moving queue ${queueId} to the end of the line:`, error);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to move queue to the end of the line',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queues/:queueId/serving-status')
  async getServingStatus(
    @Param('queueId') queueId: string,
    @Query('hasSubUnits') hasSubUnits?: string,
    @Query('subUnitId') subUnitId?: string
  ) {
    try {
      // Sanitize and validate queueId
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum)) {
        throw new BadRequestException('Invalid queue ID');
      }

      console.log(`Retrieving serving status for queue: ${queueId}, hasSubUnits: ${hasSubUnits}, subUnitId: ${subUnitId}`);

      // First check if we have the data cached in Redis for faster response
      try {
        const redisData = await this.partnerService['redisService'].getQueue(queueId);

        if (redisData &&
            redisData.status === 'serving' &&
            redisData.currentlyServing === true &&
            redisData.servingStartedAt) {

          // We have Redis data with serving info already
          console.log(`Found cached serving data in Redis for queue ${queueId}`);

          // Get the service setup to ensure correct serving time
          const serviceSetup = await this.partnerService['setupRepository'].findOne({
            where: { service: { id: redisData.serviceId } }
          });

          // Check if this is a service with subunits
          let servingTimeMinutes = 15; // Default

          const hasSubUnitsValue = hasSubUnits === 'true' || redisData.hasSubUnits === true;
          const subUnitIdValue = subUnitId || redisData.subUnitId || '0';

          console.log(`Processing queue with hasSubUnits: ${hasSubUnitsValue}, subUnitId: ${subUnitIdValue}`);

          if (hasSubUnitsValue &&
              serviceSetup?.setupData?.hasSubUnits &&
              Array.isArray(serviceSetup.setupData.subUnits) &&
              serviceSetup.setupData.subUnits.length > 0) {

            // Get the subunit ID from the queue
            const subUnitIndex = parseInt(subUnitIdValue, 10);

            // Make sure the subunit ID is valid
            const validIndex = isNaN(subUnitIndex) ? 0 : Math.min(subUnitIndex, serviceSetup.setupData.subUnits.length - 1);

            // Get the subunit
            const subUnit = serviceSetup.setupData.subUnits[validIndex];

            // Get serve time from subunit
            if (subUnit?.avgServeTime) {
              servingTimeMinutes = parseInt(subUnit.avgServeTime, 10);
              console.log(`Using subunit ${validIndex} serve time: ${servingTimeMinutes} minutes`);
            }
          }
          // For regular services, use the service setup serve time
          else if (serviceSetup?.setupData?.servingTime) {
            servingTimeMinutes = parseInt(serviceSetup.setupData.servingTime, 10);
            console.log(`Using service setup serve time: ${servingTimeMinutes} minutes`);
          }

          // Use Redis value if available (most accurate)
          if (redisData.serveTime) {
            servingTimeMinutes = redisData.serveTime;
            console.log(`Using queue's stored serve time from Redis: ${servingTimeMinutes} minutes`);
          } else if (redisData.servingTimeMinutes) {
            servingTimeMinutes = redisData.servingTimeMinutes;
            console.log(`Using servingTimeMinutes from Redis: ${servingTimeMinutes} minutes`);
          }

          // Calculate remaining time based on start time and serving duration
          const servingStartedAt = new Date(redisData.servingStartedAt);
          const estimatedEndTime = new Date(servingStartedAt);
          estimatedEndTime.setMinutes(estimatedEndTime.getMinutes() + servingTimeMinutes);

          const now = new Date();
          const remainingMs = Math.max(0, estimatedEndTime.getTime() - now.getTime());
          const remainingSeconds = Math.floor(remainingMs / 1000);
          const remainingMinutes = Math.floor(remainingSeconds / 60);

          console.log(`Queue ${queueId} serving time calculation from Redis:`);
          console.log(`Started: ${servingStartedAt.toISOString()}`);
          console.log(`End time: ${estimatedEndTime.toISOString()}`);
          console.log(`Serving time: ${servingTimeMinutes} minutes`);
          console.log(`Remaining: ${remainingMinutes} minutes ${remainingSeconds % 60} seconds`);

          return {
            status: 'success',
            data: {
              queueId: redisData.id,
              serviceId: redisData.serviceId,
              isServing: true,
              servingStartedAt: redisData.servingStartedAt,
              estimatedEndTime: estimatedEndTime.toISOString(),
              servingTimeMinutes: servingTimeMinutes,
              remainingMinutes: remainingMinutes,
              remainingSeconds: remainingSeconds,
              hasSubUnits: hasSubUnitsValue,
              subUnitId: subUnitIdValue
            }
          };
        }
      } catch (error) {
        console.error(`Error getting Redis serving data for queue ${queueId}:`, error);
        // Continue to database query if Redis fails
      }

      // Get the queue from database
      const queue = await this.partnerService['queueRepository'].findOne({
        where: { id: queueIdNum },
        relations: ['service']
      });

      if (!queue) {
        throw new NotFoundException(`Queue with ID ${queueIdNum} not found`);
      }

      // Get the service setup to get serving time
      const serviceSetup = await this.partnerService['setupRepository'].findOne({
        where: { service: { id: queue.serviceId } }
      });

      // Check if this is a service with subunits
      let servingTimeMinutes = 15; // Default

      const hasSubUnitsValue = hasSubUnits === 'true' || queue.hasSubUnits === true;
      const subUnitIdValue = subUnitId || queue.subUnitId || '0';

      console.log(`Processing queue from DB with hasSubUnits: ${hasSubUnitsValue}, subUnitId: ${subUnitIdValue}`);

      if (hasSubUnitsValue &&
          serviceSetup?.setupData?.hasSubUnits &&
          Array.isArray(serviceSetup.setupData.subUnits) &&
          serviceSetup.setupData.subUnits.length > 0) {

        // Get the subunit ID from the queue
        const subUnitIndex = parseInt(subUnitIdValue, 10);

        // Make sure the subunit ID is valid
        const validIndex = isNaN(subUnitIndex) ? 0 : Math.min(subUnitIndex, serviceSetup.setupData.subUnits.length - 1);

        // Get the subunit
        const subUnit = serviceSetup.setupData.subUnits[validIndex];

        // Get serve time from subunit
        if (subUnit?.avgServeTime) {
          servingTimeMinutes = parseInt(subUnit.avgServeTime, 10);
          console.log(`Using subunit ${validIndex} serve time from DB: ${servingTimeMinutes} minutes`);
        }
      }
      // For regular services, use the service setup serve time
      else if (serviceSetup?.setupData?.servingTime) {
        servingTimeMinutes = parseInt(serviceSetup.setupData.servingTime, 10);
        console.log(`Using service setup serve time from DB: ${servingTimeMinutes} minutes`);
      }

      // Check if the queue is currently being served
      const isServing = queue.status === 'serving' && queue.currentlyServing;

      // If not serving, return basic status
      if (!isServing || !queue.servingStartedAt) {
        return {
          status: 'success',
          data: {
            queueId: queue.id,
            serviceId: queue.serviceId,
            isServing: false,
            servingStartedAt: null,
            estimatedEndTime: null,
            servingTimeMinutes: servingTimeMinutes,
            remainingMinutes: 0,
            remainingSeconds: 0,
            hasSubUnits: hasSubUnitsValue,
            subUnitId: subUnitIdValue
          }
        };
      }

      // Calculate estimated end time and remaining time
      const servingStartedAt = new Date(queue.servingStartedAt);
      const estimatedEndTime = new Date(servingStartedAt);
      estimatedEndTime.setMinutes(estimatedEndTime.getMinutes() + servingTimeMinutes);

      const now = new Date();
      const remainingMs = Math.max(0, estimatedEndTime.getTime() - now.getTime());
      const remainingSeconds = Math.floor(remainingMs / 1000);
      const remainingMinutes = Math.floor(remainingSeconds / 60);

      console.log(`Queue ${queueId} serving time calculation from DB:`);
      console.log(`Started: ${servingStartedAt.toISOString()}`);
      console.log(`End time: ${estimatedEndTime.toISOString()}`);
      console.log(`Serving time: ${servingTimeMinutes} minutes`);
      console.log(`Remaining: ${remainingMinutes} minutes ${remainingSeconds % 60} seconds`);

      // Update Redis with current status for future requests
      try {
        const redisData = {
          id: queue.id,
          serviceId: queue.serviceId,
          status: 'serving',
          currentlyServing: true,
          servingStartedAt: queue.servingStartedAt.toISOString(),
          estimatedEndTime: estimatedEndTime.toISOString(),
          servingTime: servingTimeMinutes,
          servingTimeMinutes: servingTimeMinutes,
          remainingMinutes: remainingMinutes,
          remainingSeconds: remainingSeconds,
          statusUpdatedAt: new Date().toISOString(),
          calculatedAt: new Date().toISOString(),
          hasSubUnits: hasSubUnitsValue,
          subUnitId: subUnitIdValue
        };

        await this.partnerService['redisService'].set(`queue:${queueId}`, redisData, { ex: 86400 }); // 24 hours TTL
      } catch (error) {
        console.error(`Error updating Redis serving data for queue ${queueId}:`, error);
        // Continue without Redis update
      }

      return {
        status: 'success',
        data: {
          queueId: queue.id,
          serviceId: queue.serviceId,
          isServing: true,
          servingStartedAt: queue.servingStartedAt,
          estimatedEndTime: estimatedEndTime.toISOString(),
          servingTimeMinutes: servingTimeMinutes,
          remainingMinutes: remainingMinutes,
          remainingSeconds: remainingSeconds,
          hasSubUnits: hasSubUnitsValue,
          subUnitId: subUnitIdValue
        }
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to get serving status',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * This endpoint has been deprecated. Grace periods can only be started manually
   * from the partner app, not automatically.
   */
  @Post('trigger-auto-grace-periods')
  async triggerAutoGracePeriods() {
    try {
      return {
        status: 'success',
        message: 'Auto grace periods have been disabled. Please use the manual start-grace-period endpoint instead.'
      };
    } catch (error) {
      throw new HttpException(
        { status: 'error', message: error.message || 'Auto grace periods disabled' },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * Manually refresh TTL for serving queues in Redis
   * @returns Success status and count of refreshed queues
   */
  @Get('refresh-serving-queues')
  async refreshServingQueues() {
    try {
      this.logger.log('Manual refresh of serving queues TTL triggered');

      // Call the scheduler service to refresh serving queue TTLs
      await this.schedulerService.refreshServingQueuesTTL();

      return {
        status: 'success',
        message: 'Serving queues TTL refresh has been triggered'
      };
    } catch (error) {
      this.logger.error(`Error in manual refresh of serving queues TTL: ${error.message}`, error.stack);

      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to refresh serving queues TTL'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
