import React, { useEffect, useState, useRef } from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StatusBar,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
  Alert,
  Dimensions,
  FlatList,
  ViewToken,
  RefreshControl,
  Linking,
  TextInput,
  Share,
} from "react-native";
import { useLocalSearchParams, useRouter, Stack } from "expo-router";
import { images } from "@/constants";
import * as Location from "expo-location";
import * as SecureStore from "expo-secure-store";
import MapView, { Marker, PROVIDER_GOOGLE } from "@/lib/MapView";
import ReviewSheet from "@/components/ReviewSheet";
import BottomSheetModal from "@/components/BottomSheetModal";
import { useUser } from "@clerk/clerk-expo";

// Get screen width for carousel
const { width: screenWidth } = Dimensions.get("window");

interface ServiceDetails {
  _id: string;
  serviceName: string;
  serviceType: string;
  address: {
    details: {
      buildingNo: string;
      locality: string;
      city: string;
      state: string;
      pincode: string;
    };
    coordinates: {
      latitude: number;
      longitude: number;
    };
    googleMapsLink?: string;
  };
  images: string[];
  isOpen: boolean;
  queueInfo: {
    waitingTime: number;
    membersInQueue: number;
    vipCount: number;
    normalCount: number;
    cost: number;
    servingTime: number;
    currentTimeSlot?: string;
  };
  rating?: number;
  reviewCount?: number;
  serviceDescription?: string;
  businessPhone?: string;
  email?: string;
  workingHours?: {
    startTime: string;
    endTime: string;
  };
  selectedDays?: string[];
  setupData?: {
    hasSubUnits?: boolean;
    selectedDays?: string[];
    servingTime?: string;
    basePrice?: string;
    availableHours?: {
      [day: string]: Array<{
        start: string;
        end: string;
      }>;
    };
    subUnits?: Array<{
      name: string;
      availableHours: {
        [day: string]: Array<{
          start: string;
          end: string;
        }>;
      };
      avgServeTime: string;
      pricePerHead: string;
      selectedDays: string[];
    }>;
  };
}

interface Review {
  id: number;
  userId: string;
  userName: string;
  userProfilePic?: string;
  rating: number;
  comment: string;
  createdAt: string;
}

interface ReviewsResponse {
  reviews: Review[];
  totalCount: number;
  starDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

// Add these interfaces for type safety
interface QueueItem {
  id: number;
  serviceId: number;
  serviceName: string;
  serviceType: string;
  date: string;
  timeSlot: string;
  status: string;
  isVIP: boolean;
  createdAt: string;
}

interface TimeSlotData {
  timeSlot: string;
  normalQueueCount: number;
  vipQueueCount: number;
}

interface SubUnit {
  name: string;
  availableHours: {
    [day: string]: Array<{
      start: string;
      end: string;
    }>;
  };
  avgServeTime: string;
  pricePerHead: string;
  selectedDays: string[];
}

export default function ServiceDetailsScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const [service, setService] = useState<ServiceDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [imageLoadErrors, setImageLoadErrors] = useState<
    Record<number, boolean>
  >({});
  const flatListRef = useRef<FlatList>(null);
  const [userCoordinates, setUserCoordinates] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);
  const [distance, setDistance] = useState<string>("Calculating...");

  // Reviews state
  const [reviews, setReviews] = useState<Review[]>([]);
  const [reviewsLoading, setReviewsLoading] = useState(true);
  const [totalReviews, setTotalReviews] = useState(0);
  const [starDistribution, setStarDistribution] = useState({
    1: 0,
    2: 0,
    3: 0,
    4: 0,
    5: 0,
  });
  const [showAllReviews, setShowAllReviews] = useState(false);
  const [reviewModalVisible, setReviewModalVisible] = useState(false);
  const [userRating, setUserRating] = useState(0);
  const [userReview, setUserReview] = useState("");
  const [reviewSubmitting, setReviewSubmitting] = useState(false);

  const user = useUser();

  // Wishlist state
  const [isInWishlist, setIsInWishlist] = useState(false);
  const [wishlistLoading, setWishlistLoading] = useState(false);

  // Queue state
  const [userInQueue, setUserInQueue] = useState(false);
  const [queuePosition, setQueuePosition] = useState<number | null>(null);
  const [isCheckingQueue, setIsCheckingQueue] = useState(false);

  // Add a new state for queue count refresh
  const [queueCountRefreshing, setQueueCountRefreshing] = useState(false);

  // SubUnit state
  const [hasSubUnits, setHasSubUnits] = useState(false);
  const [subUnits, setSubUnits] = useState<SubUnit[]>([]);
  const [selectedSubUnit, setSelectedSubUnit] = useState<SubUnit | null>(null);
  const [showSubUnitsModal, setShowSubUnitsModal] = useState(false);
  const [serviceSetupData, setServiceSetupData] = useState<any>(null);

  useEffect(() => {
    fetchServiceDetails();
    getUserLocation();
    checkWishlistStatus();
    checkQueueStatus();
    fetchReviews();
  }, []);

  // Store the last selected subunit to prevent redundant updates
  const lastSelectedSubUnitRef = useRef<string | null>(null);

  useEffect(() => {
    if (service && service._id) {
      // Initial fetch of queue counts
      const today = new Date().toISOString().split("T")[0];
      fetchQueueCounts(service._id, today);

      // Set up interval for queue count refresh
      const intervalId = setInterval(() => {
        // Only refresh if we're not in the middle of a subunit change
        if (selectedSubUnit) {
          const subunitName = selectedSubUnit.name;

          // Only update if the selected subunit hasn't changed since the last update
          if (lastSelectedSubUnitRef.current === subunitName) {
            setQueueCountRefreshing(true);
            const today = new Date().toISOString().split("T")[0];
            console.log(`Auto-refreshing queue counts for subunit: ${subunitName}`);
            fetchQueueCounts(service._id, today).finally(() => {
              setQueueCountRefreshing(false);
            });
          } else {
            console.log(`Skipping auto-refresh because subunit changed from ${lastSelectedSubUnitRef.current} to ${subunitName}`);
            // Update the reference for next time
            lastSelectedSubUnitRef.current = subunitName;
          }
        }
      }, 30000); // Refresh every 30 seconds

      return () => clearInterval(intervalId);
    }
  }, [service?._id, selectedSubUnit]);

  useEffect(() => {
    if (service) {
      console.log(
        `Service updated, queue counts: Normal=${service.queueInfo.normalCount}, VIP=${service.queueInfo.vipCount}`
      );
    }
  }, [service]);

  // Add effect to update queue counts when selected subunit changes
  useEffect(() => {
    if (service && service._id && hasSubUnits && selectedSubUnit) {
      console.log(`Selected subunit changed to: ${selectedSubUnit.name}, updating queue counts`);

      // Update the last selected subunit reference
      lastSelectedSubUnitRef.current = selectedSubUnit.name;

      const today = new Date().toISOString().split("T")[0];
      fetchQueueCounts(service._id, today);
    }
  }, [selectedSubUnit]);

  useEffect(() => {
    if (service && service._id) {
      fetchReviews();
    }
  }, [service?._id]);

  const getUserLocation = async () => {
    try {
      // First try to get saved location from storage
      const savedLocation = await SecureStore.getItemAsync("userLocation");

      if (savedLocation) {
        const parsedLocation = JSON.parse(savedLocation);
        if (
          parsedLocation?.coordinates?.latitude &&
          parsedLocation?.coordinates?.longitude
        ) {
          setUserCoordinates({
            latitude: parsedLocation.coordinates.latitude,
            longitude: parsedLocation.coordinates.longitude,
          });
          return;
        }
      }

      // If no saved location, try to get current location
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== "granted") {
        setDistance("Location access denied");
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      setUserCoordinates({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });
    } catch (error) {
      console.error("Error getting user location:", error);
      setDistance("Unknown distance");
    }
  };

  useEffect(() => {
    if (service && userCoordinates) {
      calculateDistance();
    }
  }, [service, userCoordinates]);

  const calculateDistance = async () => {
    try {
      if (!service?.address?.coordinates || !userCoordinates) {
        setDistance("Unknown");
        return;
      }

      const serviceCoords = service.address.coordinates;

      // Validate coordinates
      if (
        !serviceCoords.latitude ||
        !serviceCoords.longitude ||
        !userCoordinates.latitude ||
        !userCoordinates.longitude
      ) {
        setDistance("N/A");
        return;
      }

      const origins = `${userCoordinates.latitude},${userCoordinates.longitude}`;
      const destinations = `${serviceCoords.latitude},${serviceCoords.longitude}`;

      console.log("Calculating distance between:", { origins, destinations });

      const response = await fetch(
        `https://maps.googleapis.com/maps/api/distancematrix/json?origins=${origins}&destinations=${destinations}&key=AIzaSyAUIQg4PbIdD4MuC2PWJbo1W7njP902gpI`
      );

      const data = await response.json();
      console.log("Distance matrix response:", data);

      if (data.rows?.[0]?.elements?.[0]?.distance?.text) {
        setDistance(data.rows[0].elements[0].distance.text);
      } else {
        setDistance("N/A");
      }
    } catch (error) {
      console.error("Error calculating distance:", error);
      setDistance("Error");
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    setImageLoadErrors({});
    await Promise.all([fetchServiceDetails(), getUserLocation()]);
    setRefreshing(false);
  };

  const fetchServiceDetails = async () => {
    try {
      console.log(`Fetching service details for ID: ${id}`);
      const response = await fetch(
        `http://**************:3000/api/partner/services/${id}`
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch service details: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      console.log("Service data received:", JSON.stringify(data, null, 2));

      // Validate images data
      if (data.images) {
        console.log("Images array:", data.images);
        // Filter out any invalid URLs
        data.images = Array.isArray(data.images)
          ? data.images.filter(
              (url: string) => typeof url === "string" && url.trim() !== ""
            )
          : [];
      } else {
        data.images = [];
      }

      // First set the basic service data so the UI can render something
      // Initialize queue counts to 0 to prevent showing incorrect values
      if (data.queueInfo) {
        data.queueInfo.normalCount = 0;
        data.queueInfo.vipCount = 0;
        data.queueInfo.membersInQueue = 0;
        data.queueInfo.currentTimeSlot = "";
      }

      setService(data);

      // Fetch service setup data
      try {
        const setupResponse = await fetch(
          `http://**************:3000/api/partner/service-setup/${id}`
        );

        if (setupResponse.ok) {
          const setupData = await setupResponse.json();
          console.log(
            "Setup data received:",
            JSON.stringify(setupData, null, 2)
          );

          if (
            setupData.status === "success" &&
            setupData.hasSetup &&
            setupData.data
          ) {
            setServiceSetupData(setupData.data);

            // Check if service has subunits
            if (
              setupData.data.hasSubUnits === true &&
              Array.isArray(setupData.data.subUnits) &&
              setupData.data.subUnits.length > 0
            ) {
              setHasSubUnits(true);
              setSubUnits(setupData.data.subUnits);

              // Set first subunit as default
              const firstSubUnit = setupData.data.subUnits[0];
              setSelectedSubUnit(firstSubUnit);

              // Initialize the last selected subunit reference
              lastSelectedSubUnitRef.current = firstSubUnit.name;
            } else {
              // Regular service without subunits
              setHasSubUnits(false);

              // Update service with working hours from setup data
              if (
                setupData.data.selectedDays &&
                setupData.data.availableHours
              ) {
                const workingHours = getWorkingHoursFromSetupData(
                  setupData.data
                );

                setService((prev) => {
                  if (!prev) return prev;
                  return {
                    ...prev,
                    selectedDays: setupData.data.selectedDays,
                    workingHours: workingHours,
                    queueInfo: {
                      ...prev.queueInfo,
                      servingTime:
                        parseInt(setupData.data.servingTime) ||
                        prev.queueInfo.servingTime,
                      cost:
                        parseInt(setupData.data.basePrice) ||
                        prev.queueInfo.cost,
                    },
                  };
                });
              }
            }
          }
        }
      } catch (error) {
        console.error("Error fetching service setup data:", error);
      }

      // Then fetch accurate queue counts and update the service data
      try {
        const today = new Date().toISOString().split("T")[0];
        await fetchQueueCounts(id.toString(), today);
      } catch (error) {
        console.error("Error fetching queue counts:", error);
        // Leave the original queue counts if this fails
      }
    } catch (error) {
      console.error("Error fetching service details:", error);
      Alert.alert("Error", "Failed to load service details");
    } finally {
      if (!refreshing) {
        setIsLoading(false);
      }
    }
  };

  // Helper function to get working hours from setup data
  const getWorkingHoursFromSetupData = (setupData: any) => {
    if (!setupData.availableHours) return undefined;

    // Find the first selected day with available hours
    const selectedDay = setupData.selectedDays.find(
      (day: string) =>
        setupData.availableHours[day] &&
        setupData.availableHours[day].length > 0
    );

    if (!selectedDay || !setupData.availableHours[selectedDay])
      return undefined;

    // Get the first and last time slots for the day
    const timeSlots = setupData.availableHours[selectedDay];
    if (!timeSlots || timeSlots.length === 0) return undefined;

    // Return working hours with start time from first slot and end time from last slot
    return {
      startTime: timeSlots[0].start,
      endTime: timeSlots[timeSlots.length - 1].end,
    };
  };

  // Get working hours for a specific subunit and specific day
  const getSubUnitWorkingHours = (subUnit: SubUnit, day: string) => {
    if (!subUnit.availableHours || !subUnit.availableHours[day])
      return undefined;

    // Get the time slots for the specific day
    const timeSlots = subUnit.availableHours[day];
    if (!timeSlots || timeSlots.length === 0) return undefined;

    // Return working hours with start time from first slot and end time from last slot
    return {
      startTime: timeSlots[0].start,
      endTime: timeSlots[timeSlots.length - 1].end,
    };
  };

  // Get working hours for a specific day from setup data
  const getWorkingHoursForDay = (setupData: any, day: string) => {
    if (!setupData.availableHours || !setupData.availableHours[day])
      return undefined;

    const timeSlots = setupData.availableHours[day];
    if (!timeSlots || timeSlots.length === 0) return undefined;

    return {
      startTime: timeSlots[0].start,
      endTime: timeSlots[timeSlots.length - 1].end,
    };
  };

  const fetchReviews = async () => {
    try {
      setReviewsLoading(true);
      const response = await fetch(
        `http://**************:3000/api/partner/services/${id}/reviews`
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch reviews: ${response.status} ${response.statusText}`
        );
      }

      const data: ReviewsResponse = await response.json();
      console.log("Reviews data received:", JSON.stringify(data, null, 2));

      setReviews(data.reviews);
      setTotalReviews(data.totalCount);
      setStarDistribution(data.starDistribution);
    } catch (error) {
      console.error("Error fetching reviews:", error);
      Alert.alert("Error", "Failed to load reviews");
    } finally {
      setReviewsLoading(false);
    }
  };

  const handleSubmitReview = async () => {
    if (userRating === 0) {
      Alert.alert("Error", "Please select a rating");
      return;
    }

    if (!userReview.trim()) {
      Alert.alert("Error", "Please write a review");
      return;
    }

    try {
      setReviewSubmitting(true);

      if (!user.isLoaded || !user.user) {
        Alert.alert(
          "Error",
          "Please wait while we load your profile information"
        );
        setReviewSubmitting(false);
        return;
      }

      const reviewData = {
        userId: user.user.id || "anonymous",
        userName: user.user.fullName || user.user.username || "Anonymous User",
        userProfilePic: user.user.imageUrl || null,
        rating: userRating,
        comment: userReview,
      };

      const response = await fetch(
        `http://**************:3000/api/partner/services/${id}/reviews`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(reviewData),
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to submit review: ${response.status} ${response.statusText}`
        );
      }

      // Refresh reviews and service details
      await Promise.all([fetchReviews(), fetchServiceDetails()]);

      // Reset form and close modal
      setUserRating(0);
      setUserReview("");
      setReviewModalVisible(false);

      Alert.alert("Success", "Your review has been submitted");
    } catch (error) {
      console.error("Error submitting review:", error);
      Alert.alert("Error", "Failed to submit review");
    } finally {
      setReviewSubmitting(false);
    }
  };

  const handleJoinQueue = () => {
    // Remove restriction based on service.isOpen
    // Users can join the queue regardless of service status

    if (!service) return;

    // Prepare params for join-queue screen
    const params: any = { serviceId: service._id };

    // If service has subunits and a subunit is selected, pass the subunit index
    if (hasSubUnits && selectedSubUnit) {
      // Find the index of the selected subunit
      const subUnitIndex = subUnits.findIndex(unit => unit.name === selectedSubUnit.name);
      if (subUnitIndex !== -1) {
        params.selectedSubUnitId = subUnitIndex.toString();
        console.log(`Passing selected subunit to join-queue: ${selectedSubUnit.name} (index: ${subUnitIndex})`);
      }
    }

    // Navigate to join-queue screen with params
    router.push({
      pathname: "/(root)/join-queue",
      params
    });
  };

  // Handle carousel item change
  const onViewableItemsChanged = useRef(
    ({
      viewableItems,
    }: {
      viewableItems: Array<ViewToken>;
      changed: Array<ViewToken>;
    }) => {
      if (viewableItems.length > 0 && viewableItems[0].index !== null) {
        setActiveIndex(viewableItems[0].index);
      }
    }
  ).current;

  // Handle image load errors
  const handleImageError = (index: number) => {
    console.log(`Error loading image at index ${index}`);
    setImageLoadErrors((prev) => ({ ...prev, [index]: true }));
  };

  // Render carousel indicator dots
  const renderDots = () => {
    if (!service?.images?.length) return null;

    return (
      <View
        style={{
          flexDirection: "row",
          justifyContent: "center",
          position: "absolute",
          bottom: 20,
          width: "100%",
          alignItems: "center",
        }}
      >
        {service.images.map((_, index) => (
          <View
            key={index}
            style={{
              width: 8,
              height: 8,
              borderRadius: 4,
              marginHorizontal: 4,
              backgroundColor:
                index === activeIndex ? "#159AFF" : "rgba(0, 0, 0, 0.3)",
            }}
          />
        ))}
      </View>
    );
  };

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Text
          key={i}
          className={`text-lg ${i <= rating ? "text-yellow-500" : "text-gray-300"}`}
        >
          ★
        </Text>
      );
    }
    return stars;
  };

  const renderRatingBar = (rating: number, count: number) => {
    const percentage = totalReviews > 0 ? (count / totalReviews) * 100 : 0;

    return (
      <View className="flex-row items-center mb-2">
        <Text className="text-secondary-600 w-6">{rating}</Text>
        <View className="flex-1 h-2 bg-gray-200 rounded-full mx-2">
          <View
            className="h-2 bg-yellow-500 rounded-full"
            style={{ width: `${percentage}%` }}
          />
        </View>
        <Text className="text-secondary-600 w-8 text-right">{count}</Text>
      </View>
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 30) {
      return `${diffDays} days ago`;
    } else {
      const diffMonths = Math.floor(diffDays / 30);
      return `${diffMonths} ${diffMonths === 1 ? "month" : "months"} ago`;
    }
  };

  const checkWishlistStatus = async () => {
    if (!user.isLoaded || !user.user || !user.user.primaryEmailAddress) return;

    try {
      const email = user.user.primaryEmailAddress.emailAddress;
      const response = await fetch(
        `http://**************:3000/api/customer/wishlist/${email}`
      );

      if (!response.ok) {
        return;
      }

      const data = await response.json();

      if (data.success && data.wishlist && Array.isArray(data.wishlist)) {
        setIsInWishlist(data.wishlist.includes(id));
      }
    } catch (error) {
      console.error("Error checking wishlist status:", error);
    }
  };

  const toggleWishlist = async () => {
    if (!user.isLoaded || !user.user || !user.user.primaryEmailAddress) {
      Alert.alert("Error", "Please sign in to add to wishlist");
      return;
    }

    if (!service || !service._id) {
      Alert.alert("Error", "Service information not available");
      return;
    }

    setWishlistLoading(true);

    try {
      const email = user.user.primaryEmailAddress.emailAddress;

      if (isInWishlist) {
        // Remove from wishlist
        const response = await fetch(
          `http://**************:3000/api/customer/wishlist/remove`,
          {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ email, serviceId: service._id }),
          }
        );

        if (response.ok) {
          setIsInWishlist(false);
          Alert.alert("Success", "Removed from wishlist");
        }
      } else {
        // Add to wishlist
        const response = await fetch(
          `http://**************:3000/api/customer/wishlist/add`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ email, serviceId: service._id }),
          }
        );

        if (response.ok) {
          setIsInWishlist(true);
          Alert.alert("Success", "Added to wishlist");
        }
      }
    } catch (error) {
      console.error("Error toggling wishlist:", error);
      Alert.alert("Error", "Failed to update wishlist");
    } finally {
      setWishlistLoading(false);
    }
  };

  const shareService = async () => {
    if (!service) return;

    try {
      const result = await Share.share({
        message: `Check out ${service.serviceName} on QueueFree! Current wait time: ${service.queueInfo.waitingTime} minutes. Download the app to join the queue.`,
        url: `queuefree://service/${service._id}`,
        title: service.serviceName,
      });

      if (result.action === Share.sharedAction) {
        console.log("Service shared successfully");
      }
    } catch (error) {
      console.error("Error sharing service:", error);
    }
  };

  const checkQueueStatus = async () => {
    if (!user.isLoaded || !user.user || !id) return;

    setIsCheckingQueue(true);
    try {
      // Get user's queues
      const response = await fetch(
        `http://**************:3000/api/customer/queues/${user.user.id}/upcoming`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch queue status");
      }

      const data = await response.json();

      if (data.status === "success" && data.queues) {
        // Find if any queue is for this service and still waiting
        const currentQueue = data.queues.find(
          (q: QueueItem) =>
            q.serviceId.toString() === id.toString() && q.status === "waiting"
        );

        if (currentQueue) {
          setUserInQueue(true);

          // Calculate position (assuming VIP queues go first)
          let position = 1;
          const isUserVIP = currentQueue.isVIP;

          // Determine the API endpoint based on whether we're using subunits
          let apiUrl = `http://**************:3000/api/partner/services/${id}/time-slots/${currentQueue.date.split("T")[0]}`;

          // If using subunits, add the subunit parameter for the selected subunit
          if (hasSubUnits && subUnits.length > 0) {
            let subUnitIndex = 0; // Default to first subunit

            if (selectedSubUnit) {
              // Find the index of the selected subunit
              const foundIndex = subUnits.findIndex(unit => unit.name === selectedSubUnit.name);
              if (foundIndex !== -1) {
                subUnitIndex = foundIndex;
              }
            }

            apiUrl += `?subUnitId=${subUnitIndex}`;
            console.log(`Using ${selectedSubUnit ? 'selected' : 'first'} subunit (index: ${subUnitIndex}) for queue position check in service details`);
          }

          // For each person in the same time slot, increment position if they are ahead of the user
          const queueCountResponse = await fetch(apiUrl);

          if (queueCountResponse.ok) {
            const slotData = await queueCountResponse.json();
            if (slotData.status === "success" && slotData.timeSlots) {
              const userSlot = slotData.timeSlots.find(
                (slot: TimeSlotData) => slot.timeSlot === currentQueue.timeSlot
              );

              if (userSlot) {
                if (isUserVIP) {
                  // For VIP users, position is based on vipQueueCount
                  position = userSlot.vipQueueCount;
                } else {
                  // For regular users, VIP users go first
                  position = userSlot.vipQueueCount + userSlot.normalQueueCount;
                }
              }
            }
          }

          setQueuePosition(position);
        }
      }
    } catch (error) {
      console.error("Error checking queue status:", error);
    } finally {
      setIsCheckingQueue(false);
    }
  };

  const fetchQueueCounts = async (serviceId: string, date: string) => {
    try {
      console.log(`Fetching queue counts for service: ${serviceId}, date: ${date}`);

      // Determine which subunit to use
      let subUnitIndex = 0; // Default to first subunit

      // Step 1: First check if the service has subunits
      if (!hasSubUnits) {
        console.log("Service does not have subunits, using regular queue counts");
      } else if (selectedSubUnit) {
        // Find the index of the selected subunit
        const foundIndex = subUnits.findIndex(unit => unit.name === selectedSubUnit.name);
        if (foundIndex !== -1) {
          subUnitIndex = foundIndex;
          console.log(`Service has subunits, using selected subunit: ${selectedSubUnit.name} (index: ${subUnitIndex})`);
        } else {
          console.log(`Selected subunit not found, defaulting to first subunit (index: ${subUnitIndex})`);
        }
      } else {
        console.log(`No subunit selected, using first subunit (index: ${subUnitIndex})`);
      }

      // Step 2: Construct the API URL with subunit parameter if needed
      let apiUrl = `http://**************:3000/api/customer/queues/redis/${serviceId}/upcoming`;
      if (hasSubUnits && subUnits.length > 0) {
        apiUrl += `?subUnitId=${subUnitIndex}`;
      }

      // Step 3: Fetch queue data from Redis
      console.log(`Fetching from API: ${apiUrl}`);
      const response = await fetch(apiUrl);

      if (!response.ok) {
        console.error(`Failed to fetch queues from Redis: ${response.status}`);
        throw new Error(`Failed to fetch queues: ${response.status}`);
      }

      const data = await response.json();

      if (data.status !== "success" || !data.queues || !Array.isArray(data.queues)) {
        console.log("No valid queue data available from Redis");
        throw new Error("No valid queue data available");
      }

      console.log(`Successfully retrieved ${data.queues.length} queues from Redis`);

      // Step 4: Get today's date and find the earliest available date
      const today = new Date().toISOString().split("T")[0];

      // Group queues by date
      const dateGroups: Record<string, any[]> = {};
      data.queues.forEach((queue: any) => {
        if (!queue || !queue.date) return;

        // Extract date part in YYYY-MM-DD format
        let dateStr;
        try {
          dateStr = typeof queue.date === "string"
            ? queue.date.split("T")[0]
            : new Date(queue.date).toISOString().split("T")[0];
        } catch (err) {
          console.error("Error parsing queue date:", err);
          return;
        }

        if (!dateGroups[dateStr]) {
          dateGroups[dateStr] = [];
        }
        dateGroups[dateStr].push(queue);
      });

      // Find earliest future date
      const availableDates = Object.keys(dateGroups)
        .filter(date => date >= today)
        .sort();

      if (availableDates.length === 0) {
        console.log("No future dates with queues found");
        updateServiceWithQueueCounts(0, 0, "");
        return;
      }

      const earliestDate = availableDates[0];
      console.log(`Using queues for date: ${earliestDate}`);

      // Step 5: Group queues by time slot for the earliest date
      const relevantQueues = dateGroups[earliestDate];
      const timeSlotGroups: Record<string, any[]> = {};

      relevantQueues.forEach((queue: any) => {
        if (queue && queue.timeSlot) {
          if (!timeSlotGroups[queue.timeSlot]) {
            timeSlotGroups[queue.timeSlot] = [];
          }
          timeSlotGroups[queue.timeSlot].push(queue);
        }
      });

      // Step 6: Find the current or next time slot
      const timeSlots = Object.keys(timeSlotGroups);
      if (timeSlots.length === 0) {
        console.log("No time slots found for the date");
        updateServiceWithQueueCounts(0, 0, "");
        return;
      }

      // Convert time slots to comparable format
      const now = new Date();
      const currentHours = now.getHours();
      const currentMinutes = now.getMinutes();
      const currentTime = currentHours * 60 + currentMinutes; // in minutes

      const timeSlotTimes = timeSlots.map(slot => {
        const parts = slot.split(" - ");
        const startTimePart = parts[0].trim();
        const [timeValue, period] = startTimePart.split(" ");
        const [hourStr, minuteStr] = timeValue.split(":");
        let hour = parseInt(hourStr, 10);
        const minute = parseInt(minuteStr, 10);

        if (period.toUpperCase() === "PM" && hour < 12) {
          hour += 12;
        } else if (period.toUpperCase() === "AM" && hour === 12) {
          hour = 0;
        }

        return {
          slot,
          time: hour * 60 + minute
        };
      });

      // Sort by time
      timeSlotTimes.sort((a, b) => a.time - b.time);

      // Find appropriate time slot
      let selectedTimeSlot: string;
      let selectedTimeSlotQueues: any[] = [];

      if (earliestDate === today) {
        // For today, find the current time slot
        let selectedSlotInfo = timeSlotTimes[0]; // Default to first

        // First, try to find a time slot that contains the current time
        for (let i = 0; i < timeSlotTimes.length; i++) {
          const currentSlot = timeSlotTimes[i];
          const nextSlot = i < timeSlotTimes.length - 1 ? timeSlotTimes[i + 1] : null;

          // Check if current time is within this slot's range
          if (!nextSlot || currentTime < nextSlot.time) {
            if (currentTime >= currentSlot.time) {
              selectedSlotInfo = currentSlot;
              console.log(`Found current time slot: ${currentSlot.slot} (current time: ${currentHours}:${currentMinutes})`);
              break;
            }
          }
        }

        // If no current slot found, use the next upcoming slot
        if (selectedSlotInfo.time > currentTime) {
          console.log(`No current time slot active, using next slot: ${selectedSlotInfo.slot}`);
        }

        selectedTimeSlot = selectedSlotInfo.slot;
      } else {
        // For future date, use the first time slot
        selectedTimeSlot = timeSlotTimes[0].slot;
        console.log(`Using first time slot for future date: ${selectedTimeSlot}`);
      }

      selectedTimeSlotQueues = timeSlotGroups[selectedTimeSlot] || [];

      // Step 7: Count normal and VIP queues for the selected time slot
      const normalCount = selectedTimeSlotQueues.filter(
        (q: any) => q && q.isVIP !== true && q.status !== "cancelled" && q.status !== "completed"
      ).length;

      const vipCount = selectedTimeSlotQueues.filter(
        (q: any) => q && q.isVIP === true && q.status !== "cancelled" && q.status !== "completed"
      ).length;

      console.log(`Current/next time slot for ${earliestDate}: ${selectedTimeSlot}, Normal: ${normalCount}, VIP: ${vipCount}`);

      // Step 8: Update the service state with the queue counts
      updateServiceWithQueueCounts(normalCount, vipCount, selectedTimeSlot);

    } catch (error) {
      console.error("Error fetching queue counts:", error);
      // Set default values in case of error
      updateServiceWithQueueCounts(0, 0, "");
    }
  };

  // Helper function to update service state with queue counts
  const updateServiceWithQueueCounts = (normalCount: number, vipCount: number, timeSlot: string) => {
    setService((prev) => {
      if (!prev) return prev;

      const updatedQueueInfo = {
        ...prev.queueInfo,
        normalCount,
        vipCount,
        membersInQueue: normalCount + vipCount,
        waitingTime: prev.queueInfo.servingTime * (normalCount + vipCount),
        currentTimeSlot: timeSlot,
      };

      console.log("Updating service queue counts with:", updatedQueueInfo);

      return {
        ...prev,
        queueInfo: updatedQueueInfo,
      };
    });
  };

  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#159AFF" />
        </View>
      </SafeAreaView>
    );
  }

  if (!service) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />
        <View className="flex-1 justify-center items-center">
          <Text className="text-secondary-600">Service not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <View className="bg-white pb-8">
        <View className="px-8 pt-12">
          <View className=" flex-row justify-start mt-4">
            <TouchableOpacity
              className="flex-row items-center absolute left-0 -top-2 border border-secondary-600 w-12 h-12 rounded-full justify-center"
              onPress={() => router.back()}
            >
              <Image source={images.back} className="w-4 h-4" />
            </TouchableOpacity>

            <View className="flex-row absolute right-0 -top-2 space-x-3">
              <TouchableOpacity
                className="border border-secondary-600 w-12 h-12 mr-4 rounded-full justify-center items-center"
                onPress={shareService}
              >
                <Image source={images.share} className="w-5 h-5" />
              </TouchableOpacity>

              <TouchableOpacity
                className="border border-secondary-600 w-12 h-12 rounded-full justify-center items-center"
                onPress={toggleWishlist}
                disabled={wishlistLoading}
              >
                {wishlistLoading ? (
                  <ActivityIndicator size="small" color="#159AFF" />
                ) : isInWishlist ? (
                  <Image
                    source={images.wish}
                    className="w-5 h-5"
                    style={{ tintColor: "#FF3B30" }}
                  />
                ) : (
                  <Image source={images.wish} className="w-5 h-5" />
                )}
              </TouchableOpacity>
            </View>

            <View className="flex ml-20 items-center">
              <Text className="font-poppins-medium text-xl mb-2">
                Service Details
              </Text>
            </View>
          </View>
        </View>
      </View>

      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl
            refreshing={refreshing || queueCountRefreshing}
            onRefresh={onRefresh}
            colors={["#159AFF"]}
            tintColor="#159AFF"
          />
        }
      >
        {/* Image Carousel */}
        <View className="relative w-full h-90 px-4 py-2">
          {service.images && service.images.length > 0 ? (
            <>
              <FlatList
                ref={flatListRef}
                data={service.images}
                horizontal
                pagingEnabled={false}
                decelerationRate="fast"
                snapToInterval={screenWidth - 40}
                snapToAlignment="center"
                showsHorizontalScrollIndicator={false}
                keyExtractor={(_, index) => index.toString()}
                onViewableItemsChanged={onViewableItemsChanged}
                viewabilityConfig={{
                  itemVisiblePercentThreshold: 80,
                  minimumViewTime: 100,
                }}
                contentContainerStyle={{ paddingHorizontal: 10 }}
                renderItem={({ item, index }) => (
                  <View
                    style={{
                      width: screenWidth - 66,
                      height: 240,
                      marginHorizontal: 10,
                      borderRadius: 25,
                      overflow: "hidden",
                      backgroundColor: "#f5f5f5",
                    }}
                  >
                    {imageLoadErrors[index] ? (
                      <View
                        style={{
                          width: "100%",
                          height: "100%",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <Text className="text-secondary-500">
                          Image not available
                        </Text>
                      </View>
                    ) : (
                      <Image
                        source={{ uri: item }}
                        style={{ width: "100%", height: "100%" }}
                        resizeMode="cover"
                        onError={() => handleImageError(index)}
                      />
                    )}
                  </View>
                )}
              />
              {renderDots()}
            </>
          ) : (
            <View
              style={{
                width: screenWidth - 40,
                height: 240,
                marginHorizontal: 10,
                borderRadius: 16,
                backgroundColor: "#f5f5f5",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text className="text-secondary-500">No images available</Text>
            </View>
          )}
        </View>

        {/* Content */}
        <View className="p-6 mx-3">
          {/* Service Header */}
          <View className="flex-row justify-between items-start mb-4">
            <View className="flex-1">
              <Text className="font-poppins-medium text-2xl mb-1">
                {service.serviceName}
              </Text>
              <View className="flex-row items-center">
                <Text className="text-secondary-600 mr-2">
                  {service.address.details.locality},{" "}
                  {service.address.details.city}
                </Text>
              </View>
            </View>
            <View
              className={`flex-row items-center px-3 py-1 h-10 rounded-full ${service.isOpen ? "bg-success-100" : "bg-danger-100"}`}
            >
              <Image
                source={images.serviceclock}
                className="w-5 h-5 mr-2"
                style={{ tintColor: service.isOpen ? "#22C55E" : "#EF4444" }}
              />
              <View className={``}>
                <Text
                  className={`${service.isOpen ? "text-success-700" : "text-danger-700"}`}
                >
                  {service.isOpen ? "Open" : "Closed"}
                </Text>
              </View>
            </View>
          </View>



          {/* Overview section */}
          <View className="mt-2">
            <View className="flex-row justify-between px-6">
              <View className="items-center px-4 py-3 rounded-lg">
                <View className="bg-primary-500/10 w-12 h-12 rounded-full items-center justify-center mb-2">
                  <Image
                    source={images.waiting}
                    className="w-6 h-6"
                    style={{ tintColor: "#159AFF" }}
                  />
                </View>
                <Text className="font-poppins-medium text-base">
                  {hasSubUnits && selectedSubUnit
                    ? `${selectedSubUnit.avgServeTime}m`
                    : `${service.queueInfo.servingTime}m`}
                </Text>
                <Text className="text-secondary-600 text-sm mb-1">
                  Serve Time
                </Text>
              </View>

              <View className="items-center bg-white px-4 py-3 rounded-lg">
                <View className="bg-primary-500/10 w-12 h-12 rounded-full items-center justify-center mb-2">
                  <Image
                    source={images.away}
                    className="w-6 h-6"
                    style={{ tintColor: "#159AFF" }}
                  />
                </View>
                <Text className="font-poppins-medium text-base">
                  {distance}
                </Text>
                <Text className="text-secondary-600 text-sm mb-1">
                  Distance
                </Text>
              </View>

              <View className="items-center bg-white px-4 py-3 rounded-lg">
                <View className="bg-primary-500/10 w-12 h-12 rounded-full items-center justify-center mb-2">
                  <Image
                    source={images.rating}
                    className="w-6 h-6"
                    style={{ tintColor: "#159AFF" }}
                  />
                </View>
                <View className="flex-row items-center">
                  <Text className="font-poppins-medium text-base">
                    {service.rating ? service.rating.toFixed(1) : "0.0"}
                  </Text>
                  <Text className="text-yellow-500 ml-1 pb-2">★</Text>
                  <Text className="text-secondary-600 text-xs ml-1">
                    ({service.reviewCount || 0})
                  </Text>
                </View>
                <Text className="text-secondary-600 text-sm mb-1">Rating</Text>
              </View>
            </View>
          </View>

          {/* Queue Info */}
          <View className="border border-secondary-600 flex justify-center items-center rounded-3xl mx-10 p-6 mt-8">
            <Text className="font-poppins-medium text-secondary-500 absolute bottom-[55px] bg-white p-3 text-sm mb-3">
              Current/Next Queue Count
            </Text>
            <View className="flex-row justify-between">
              <View className="flex-row">
                <View className="items-center px-4 py-3 rounded-lg mr-2">
                  <View className="flex-row items-center">
                    <Image
                      source={images.queueyellow}
                      className="w-[22.37px] h-[19px] mr-2"
                      style={{ tintColor: "#FFB800" }}
                    />
                    <Text className="text-secondary-500 text-base font-poppins-medium">
                      VIP: {service.queueInfo.vipCount}
                    </Text>
                  </View>
                </View>

                <View className="items-center  px-4 py-3 rounded-lg">
                  <View className="flex-row items-center">
                    <Image
                      source={images.queueblue}
                      className="w-[22.37px] h-[19px] mr-2"
                      style={{ tintColor: "#159AFF" }}
                    />
                    <Text className="text-secondary-500 text-base font-poppins-medium">
                      Normal: {service.queueInfo.normalCount}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </View>

          {/* Service Description */}
          <View className="mt-8">
            <Text className="font-poppins-medium text-lg mb-2">
              Description
            </Text>
            <Text className="text-secondary-600 text-base leading-6">
              {service.serviceDescription ||
                "No description available for this service."}
            </Text>
          </View>

          {/* Contact Details */}
          <View className="mt-8">
            <Text className="font-poppins-medium text-lg mb-2">
              Contact Details
            </Text>
            <View className="space-y-4">
              {service.businessPhone && (
                <View className="flex-row items-center mb-2">
                  <View className=" w-8 h-8 rounded-full items-center justify-center">
                    <Image source={images.call} className="w-5 h-5" />
                  </View>
                  <View className="flex-row items-center">
                    <Text className="text-secondary-600 font-poppins-medium text-base">
                      Phone:{" "}
                    </Text>
                    <TouchableOpacity
                      onPress={() =>
                        Linking.openURL(`tel:${service.businessPhone}`)
                      }
                    >
                      <Text className="text-base font-poppins-medium text-primary-500">
                        {service.businessPhone}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
              {service.email && (
                <View className="flex-row items-center mb-2">
                  <View className=" w-8 h-8 rounded-full items-center justify-center">
                    <Image source={images.mail} className="w-5 h-5" />
                  </View>
                  <View className="flex-row items-center">
                    <Text className="text-secondary-600 font-poppins-medium text-base">
                      Mail:{" "}
                    </Text>
                    <TouchableOpacity
                      onPress={() => Linking.openURL(`mailto:${service.email}`)}
                    >
                      <Text className="text-base font-poppins-medium text-primary-500">
                        {service.email}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </View>
          </View>

          {/* Working Hours & Days Combined */}
          <View className="mt-8 ">
            <Text className="font-poppins-medium text-lg mb-2">
              Working Hours
            </Text>
            {hasSubUnits && selectedSubUnit ? (
              // Display working hours for selected subunit
              <View className="space-y-3">
                {[
                  "Monday",
                  "Tuesday",
                  "Wednesday",
                  "Thursday",
                  "Friday",
                  "Saturday",
                  "Sunday",
                ].map((day) => {
                  const isOpen = selectedSubUnit.selectedDays?.includes(day);
                  const workingHours = isOpen
                    ? getSubUnitWorkingHours(selectedSubUnit, day)
                    : undefined;

                  return (
                    <View
                      key={day}
                      className="flex-row items-center justify-between mb-2"
                    >
                      <View className="flex-row items-center">
                        <Text
                          className={`ml-2 font-poppins-medium text-base ${
                            isOpen ? "text-secondary-600" : "text-secondary-400"
                          }`}
                        >
                          {day}
                        </Text>
                      </View>
                      <View>
                        {isOpen ? (
                          <Text className="text-secondary-600 font-poppins-medium">
                            {workingHours
                              ? `${workingHours.startTime} - ${workingHours.endTime}`
                              : "Hours not specified"}
                          </Text>
                        ) : (
                          <Text className="text-secondary-400 font-poppins-medium">
                            Closed
                          </Text>
                        )}
                      </View>
                    </View>
                  );
                })}
              </View>
            ) : serviceSetupData &&
              serviceSetupData.selectedDays &&
              serviceSetupData.selectedDays.length > 0 ? (
              // Display working hours for regular service
              <View className="space-y-3">
                {[
                  "Monday",
                  "Tuesday",
                  "Wednesday",
                  "Thursday",
                  "Friday",
                  "Saturday",
                  "Sunday",
                ].map((day) => {
                  const isOpen = serviceSetupData.selectedDays.includes(day);
                  const workingHours = isOpen
                    ? getWorkingHoursForDay(serviceSetupData, day)
                    : undefined;

                  return (
                    <View
                      key={day}
                      className="flex-row items-center justify-between mb-2"
                    >
                      <View className="flex-row items-center">
                        <Text
                          className={`ml-2 font-poppins-medium text-base ${
                            isOpen ? "text-secondary-600" : "text-secondary-400"
                          }`}
                        >
                          {day}
                        </Text>
                      </View>
                      <View>
                        {isOpen ? (
                          <Text className="text-secondary-600 font-poppins-medium">
                            {workingHours
                              ? `${workingHours.startTime} - ${workingHours.endTime}`
                              : "Hours not specified"}
                          </Text>
                        ) : (
                          <Text className="text-secondary-400 font-poppins-medium">
                            Closed
                          </Text>
                        )}
                      </View>
                    </View>
                  );
                })}
              </View>
            ) : (
              <Text className="text-secondary-600">
                Working hours not specified
              </Text>
            )}
          </View>

          {/* Address and Location Map */}
          <View className="mt-8">
            <Text className="font-poppins-medium text-lg mb-2">Address</Text>

            {service.address && (
              <View className="space-y-4">
                {/* Full address display */}
                <View className="flex-row items-start">
                  <Image
                    source={images.mapPin}
                    className="w-5 h-5 mr-1 mt-1"
                    style={{ tintColor: "#263238" }}
                  />
                  <Text className="font-poppins-regular text-secondary-600 text-base flex-1">
                    {service.address.details?.buildingNo || ""}
                    {service.address.details?.buildingNo ? ", " : ""}
                    {service.address.details?.locality || ""}
                    {service.address.details?.locality ? ", " : ""}
                    {service.address.details?.city || ""}
                    {service.address.details?.city ? ", " : ""}
                    {service.address.details?.state || ""}
                    {service.address.details?.state ? " - " : ""}
                    {service.address.details?.pincode || ""}
                  </Text>
                </View>

                {/* Mini Map with Get Directions button */}
                <View
                  className="mt-4 rounded-2xl overflow-hidden border border-gray-200"
                  style={{ height: 250 }}
                >
                  {service.address.coordinates?.latitude &&
                  service.address.coordinates?.longitude ? (
                    <>
                      <MapView
                        provider={PROVIDER_GOOGLE}
                        style={{ width: "100%", height: "100%" }}
                        initialRegion={{
                          latitude: service.address.coordinates.latitude,
                          longitude: service.address.coordinates.longitude,
                          latitudeDelta: 0.005,
                          longitudeDelta: 0.005,
                        }}
                        scrollEnabled={true}
                        zoomEnabled={true}
                        rotateEnabled={true}
                        pitchEnabled={true}
                        onPress={() => {
                          // Open in Google Maps when the map itself is pressed
                          const destination = service.address.googleMapsLink
                            ? service.address.googleMapsLink
                            : `https://www.google.com/maps/dir/?api=1&destination=${service.address.coordinates.latitude},${service.address.coordinates.longitude}`;

                          Linking.openURL(destination);
                        }}
                      >
                        <Marker
                          coordinate={{
                            latitude: service.address.coordinates.latitude,
                            longitude: service.address.coordinates.longitude,
                          }}
                          title={service.serviceName}
                          description={service.address.details?.locality || ""}
                        >
                          <View style={{ alignItems: "center" }}>
                            <View className="">
                              <Image
                                source={images.currentLocation}
                                style={{ width: 40, height: 45 }}
                              />
                            </View>
                          </View>
                        </Marker>
                      </MapView>
                      <View className="absolute bottom-3 w-full flex items-center justify-center">
                        <TouchableOpacity
                          className="bg-white py-4 px-6 rounded-2xl flex-row items-center border border-primary-400"
                          onPress={() => {
                            // Use Google Maps link if available, otherwise use coordinates
                            const destination = service.address.googleMapsLink
                              ? service.address.googleMapsLink
                              : `https://www.google.com/maps/dir/?api=1&destination=${service.address.coordinates.latitude},${service.address.coordinates.longitude}`;

                            Linking.openURL(destination);
                          }}
                        >
                          <Image
                            source={images.nearme}
                            style={{ width: 18, height: 18, marginRight: 10 }}
                          />
                          <Text className="text-secondary-500 font-poppins-medium">
                            Get Directions
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </>
                  ) : (
                    <View className="w-full h-full bg-gray-200 justify-center items-center">
                      <Text className="text-secondary-500">
                        Map not available
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            )}
          </View>

          {/* Ratings & Reviews Section */}
          <View className="mt-8">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="font-poppins-medium text-lg">
                Ratings & Reviews
              </Text>
              <TouchableOpacity
                className="border flex-row justify-center items-center border-primary-500 px-4 py-2 rounded-xl"
                onPress={() => setReviewModalVisible(true)}
              >
                <Image
                  source={images.write}
                  style={{ width: 14, height: 14, marginRight: 10 }}
                />
                <Text className=" text-primary-500 font-poppins-medium">
                  Write a Review
                </Text>
              </TouchableOpacity>
            </View>

            {/* Rating Summary */}
            <View className="flex-row mb-6 bg-gray-50 p-4 rounded-lg">
              <View className="items-center justify-center mr-6">
                <Text className="font-poppins-medium text-3xl">
                  {service.rating ? service.rating.toFixed(1) : "0.0"}
                </Text>
                <View className="flex-row my-1">
                  {renderStars(Math.round(service.rating || 0))}
                </View>
                <Text className="text-secondary-600 text-sm">
                  {service.reviewCount || 0}{" "}
                  {service.reviewCount === 1 ? "review" : "reviews"}
                </Text>
              </View>

              <View className="flex-1 justify-center">
                {[5, 4, 3, 2, 1].map((rating) => (
                  <View key={`rating-bar-${rating}`}>
                    {renderRatingBar(
                      rating,
                      starDistribution[rating as keyof typeof starDistribution]
                    )}
                  </View>
                ))}
              </View>
            </View>

            {/* Reviews List */}
            {reviewsLoading ? (
              <View className="items-center py-8">
                <ActivityIndicator size="small" color="#159AFF" />
                <Text className="text-secondary-600 mt-2">
                  Loading reviews...
                </Text>
              </View>
            ) : reviews.length === 0 ? (
              <View className="items-center py-8 bg-gray-50 rounded-lg">
                <Text className="text-secondary-600">No reviews yet</Text>
              </View>
            ) : (
              <View>
                {(showAllReviews ? reviews : reviews.slice(0, 3)).map(
                  (review) => (
                    <View
                      key={review.id}
                      className="mb-6 pb-6 border-b border-gray-200"
                    >
                      <View className="flex-row items-center mb-2">
                        <View className="w-10 h-10 rounded-full bg-gray-300 overflow-hidden mr-3">
                          {review.userProfilePic ? (
                            <Image
                              source={{ uri: review.userProfilePic }}
                              className="w-full h-full"
                              resizeMode="cover"
                            />
                          ) : (
                            <View className="w-full h-full bg-primary-500 items-center justify-center">
                              <Text className="text-white font-bold">
                                {review.userName.charAt(0).toUpperCase()}
                              </Text>
                            </View>
                          )}
                        </View>
                        <View className="flex-1">
                          <Text className="font-poppins-medium">
                            {review.userName}
                          </Text>
                          <View className="flex-row items-center">
                            <View className="flex-row mr-2">
                              {renderStars(review.rating)}
                            </View>
                            <Text className="text-secondary-600 text-xs">
                              {formatDate(review.createdAt)}
                            </Text>
                          </View>
                        </View>
                      </View>
                      <Text className="text-secondary-600">
                        {review.comment}
                      </Text>
                    </View>
                  )
                )}

                {reviews.length > 3 && (
                  <TouchableOpacity
                    className="items-center py-3 bg-gray-100 rounded-lg mb-4"
                    onPress={() => setShowAllReviews(!showAllReviews)}
                  >
                    <Text className="text-primary-500 font-poppins-medium">
                      {showAllReviews
                        ? "Show Less"
                        : `View All ${totalReviews} Reviews`}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Join Queue Button */}
      <View className="p-6 border-t flex justify-center items-center border-gray-200">
        {/* SubUnit Selector */}
        {hasSubUnits && subUnits.length > 0 && (
            <View className="mb-6 mx-4 items-center ">
              <TouchableOpacity
                className="border border-secondary-300 w-full rounded-xl p-4 flex-row justify-between items-center"
                onPress={() => setShowSubUnitsModal(true)}
              >
                <View className="flex-row items-center">
                  <Text className="font-poppins-regular text-secondary-600 mr-3">
                    Subunit:
                  </Text>
                  <Image
                    source={images.service}
                    className="w-5 h-5 mr-2 mb-1"
                    tintColor="#159AFF"
                  />
                  <Text className="font-poppins-medium text-secondary-500">
                    {selectedSubUnit?.name || "Select a service unit"}
                  </Text>
                </View>
                <Image
                  source={images.down}
                  className="w-[16px] h-[10px]"
                  tintColor="#455A64"
                />
              </TouchableOpacity>
            </View>
          )}
        <TouchableOpacity
          className="w-[390px] py-6 rounded-2xl items-center bg-primary-500"
          onPress={handleJoinQueue}
        >
          <Text className="text-white font-poppins-medium text-lg">
            {`Join Queue · ₹${hasSubUnits && selectedSubUnit ? selectedSubUnit.pricePerHead : service.queueInfo.cost}`}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Review Bottom Sheet */}
      <ReviewSheet
        visible={reviewModalVisible}
        onClose={() => setReviewModalVisible(false)}
        userRating={userRating}
        setUserRating={setUserRating}
        userReview={userReview}
        setUserReview={setUserReview}
        onSubmit={handleSubmitReview}
        isSubmitting={reviewSubmitting}
      />

      {/* SubUnits Bottom Sheet Modal */}
      <BottomSheetModal
        visible={showSubUnitsModal}
        onClose={() => setShowSubUnitsModal(false)}
        headerText="Select Service Unit"
        items={subUnits.map(unit => ({
          id: unit.name,
          label: unit.name,
          icon: images.service,
          iconClassName: "w-5 h-5 tint-primary-500"
        }))}
        onSelectItem={(unitName) => {
          const unit = subUnits.find(u => u.name === unitName);
          if (unit) {
            setSelectedSubUnit(unit);
          }
        }}
        selectedItem={selectedSubUnit?.name}
        defaultHeight={50}
        expandedHeight={80}
      />
    </SafeAreaView>
  );
}
