import { Repository } from 'typeorm';
import { Queue } from '../../partner/entities/queue.entity';
import { ServiceSetup } from '../../partner/entities/service-setup.entity';
import { RedisService } from '../redis/redis.service';
export declare class QueueFlowService {
    private queueRepository;
    private serviceSetupRepository;
    private redisService;
    private readonly logger;
    constructor(queueRepository: Repository<Queue>, serviceSetupRepository: Repository<ServiceSetup>, redisService: RedisService);
    startGracePeriod(queueId: number, serviceId: number): Promise<Queue>;
    handlePresenceConfirmation(queueId: number, isPresent: boolean): Promise<Queue>;
    startServing(queueId: number): Promise<Queue>;
    completeService(queueId: number): Promise<Queue>;
    markAsNoShow(queueId: number): Promise<Queue>;
    private updateRedisWithGracePeriod;
    checkGracePeriods(): Promise<void>;
    autoStartGracePeriods(): Promise<{
        disabled: boolean;
        message: string;
    }>;
    checkSingleGracePeriod(queueId: number): Promise<void>;
    calculateEstimatedServeTime(queueId: number): Promise<Queue>;
    private updateRedisWithEstimatedServeTime;
    calculateWaitTimeStatus(queueId: number): Promise<string>;
    calculateActualServiceTime(queueId: number): Promise<number>;
    recalculateEstimatedServeTimes(serviceId: number, date: Date, timeSlot: string): Promise<void>;
    getEstimatedWaitTime(queueId: number): Promise<{
        waitTimeMinutes: number;
        waitTimeStatus: string;
        estimatedServeTime: Date;
        position: number;
        initialPositionAtJoin: number;
    }>;
    getActualServiceTime(queueId: number): Promise<{
        serviceTimeMinutes: number;
        servingStartedAt: Date | null;
        statusUpdatedAt: Date | null;
        status: string;
    }>;
    moveToEndOfLine(queueId: number): Promise<Queue>;
    recalculateAllEstimatedServeTimes(): Promise<void>;
}
