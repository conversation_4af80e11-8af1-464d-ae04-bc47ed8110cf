"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const dotenv = require("dotenv");
const express_1 = require("express");
dotenv.config();
async function bootstrap() {
    try {
        const app = await core_1.NestFactory.create(app_module_1.AppModule, {
            logger: ['error', 'warn', 'log', 'debug', 'verbose'],
        });
        app.use((0, express_1.json)({ limit: '10mb' }));
        app.setGlobalPrefix('api');
        app.enableCors({
            origin: true,
            methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
            allowedHeaders: ['Content-Type', 'Accept', 'Authorization'],
            credentials: true,
        });
        app.use((error, req, res, next) => {
            if (error.type === 'entity.too.large') {
                return res.status(413).json({
                    statusCode: 413,
                    message: 'Payload too large. Maximum size is 10MB',
                    error: 'Payload Too Large'
                });
            }
            next(error);
        });
        process.on('unhandledRejection', (reason, promise) => {
            console.error('Unhandled Rejection at:', promise, 'reason:', reason);
        });
        const port = process.env.PORT || 3000;
        await app.listen(port);
        console.log(`Application is running on port ${port}`);
    }
    catch (error) {
        console.error('Failed to start application:', error);
        process.exit(1);
    }
}
bootstrap();
//# sourceMappingURL=main.js.map