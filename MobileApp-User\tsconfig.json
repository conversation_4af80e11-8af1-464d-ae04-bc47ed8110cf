{"compilerOptions": {"strict": true, "jsx": "react-native", "target": "esnext", "lib": ["esnext"], "allowJs": true, "skipLibCheck": true, "noEmit": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "esModuleInterop": true, "moduleResolution": "node", "paths": {"@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "nativewind-env.d.ts", "declarations.d.ts"], "extends": "expo/tsconfig.base"}