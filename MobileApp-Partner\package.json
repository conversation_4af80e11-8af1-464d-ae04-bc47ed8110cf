{"name": "queuefree-user", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "eslint .", "postinstall": "patch-package"}, "dependencies": {"@clerk/clerk-expo": "^2.7.6", "@clerk/types": "^4.45.1", "@expo/metro-runtime": "~4.0.1", "@gorhom/bottom-sheet": "^5.1.1", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/cli-server-api": "^11.4.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-picker/picker": "2.9.0", "@teovilla/react-native-web-maps": "^0.9.5", "axios": "^1.7.9", "expo": "^52.0.46", "expo-auth-session": "~6.0.3", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.20", "expo-font": "~13.0.4", "expo-image-picker": "~16.0.6", "expo-linking": "~7.0.5", "expo-location": "~18.0.10", "expo-router": "~4.0.20", "expo-secure-store": "~14.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-updates": "^0.27.4", "expo-web-browser": "~14.0.2", "nativewind": "^4.1.23", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-calendars": "^1.1310.0", "react-native-confetti": "^0.1.0", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-maps": "1.18.0", "react-native-reanimated": "~3.16.1", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "~0.19.6", "tailwindcss": "^3.0.0", "validator": "^13.12.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/axios": "^0.9.36", "@types/react": "~18.3.12", "@types/react-native": "^0.72.8", "eslint": "^8.57.0", "eslint-config-expo": "~8.0.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "prettier": "^3.5.0", "typescript": "~5.3.3"}, "private": true}