"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Queue = void 0;
const typeorm_1 = require("typeorm");
const service_entity_1 = require("./service.entity");
let Queue = class Queue {
};
exports.Queue = Queue;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], Queue.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => service_entity_1.Service, service => service.queues),
    __metadata("design:type", service_entity_1.Service)
], Queue.prototype, "service", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], Queue.prototype, "serviceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 'waiting' }),
    __metadata("design:type", String)
], Queue.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Queue.prototype, "isVIP", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Queue.prototype, "date", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Queue.prototype, "timeSlot", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Queue.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Queue.prototype, "userName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Queue.prototype, "uniqueSlotId", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Queue.prototype, "isCheckedIn", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'timestamp with time zone' }),
    __metadata("design:type", Object)
], Queue.prototype, "graceStartedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Queue.prototype, "confirmedPresence", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Queue.prototype, "inGracePeriod", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Queue.prototype, "currentlyServing", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'timestamp with time zone' }),
    __metadata("design:type", Object)
], Queue.prototype, "servingStartedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'timestamp with time zone' }),
    __metadata("design:type", Object)
], Queue.prototype, "statusUpdatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, default: 0 }),
    __metadata("design:type", Number)
], Queue.prototype, "position", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, default: 0 }),
    __metadata("design:type", Number)
], Queue.prototype, "initialPositionAtJoin", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'timestamp with time zone' }),
    __metadata("design:type", Object)
], Queue.prototype, "estimatedServeTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'varchar', default: 'on-time' }),
    __metadata("design:type", String)
], Queue.prototype, "waitTimeStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false, nullable: true }),
    __metadata("design:type", Boolean)
], Queue.prototype, "hasSubUnits", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'varchar' }),
    __metadata("design:type", String)
], Queue.prototype, "subUnitId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'varchar' }),
    __metadata("design:type", String)
], Queue.prototype, "subUnitName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, default: 15 }),
    __metadata("design:type", Number)
], Queue.prototype, "serveTime", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Queue.prototype, "createdAt", void 0);
exports.Queue = Queue = __decorate([
    (0, typeorm_1.Entity)('queues')
], Queue);
//# sourceMappingURL=queue.entity.js.map