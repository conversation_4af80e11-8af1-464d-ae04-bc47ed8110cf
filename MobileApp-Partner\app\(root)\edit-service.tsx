import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  ScrollView,
  TouchableOpacity,
  Image,
  StatusBar,
  SafeAreaView,
  Platform,
  Keyboard,
  Alert,
  Linking,
  ActivityIndicator,
} from "react-native";
import { useRouter } from "expo-router";
import { useUser } from "@clerk/clerk-expo";
import { images } from "@/constants";
import ButtonBlueMain from "@/components/ButtonBlueMain";
import * as SecureStore from "expo-secure-store";
import BottomSheetModal from "@/components/BottomSheetModal";
import * as ImagePicker from 'expo-image-picker';

const serviceTypes = [
  "Select Type",
  "Hospital",
  "Bank",
  "Government",
  "Retail",
  "Restaurant",
  "Theatre",
  "Tourism",
  "Salon",
  "Medical",
  "Other",
];

interface FormData {
  serviceName: string;
  serviceType: string;
  businessPhone: string;
  serviceDescription: string;
  address: {
    details: {
      buildingNo: string;
      locality: string;
      city: string;
      state: string;
      pincode: string;
    };
    coordinates: {
      latitude: number;
      longitude: number;
    };
    area?: string;
    fullAddress?: string;
  };
}

export default function EditServiceScreen() {
  console.log("=========== EDIT SERVICE SCREEN INITIALIZED ===========");

  const router = useRouter();
  const { user } = useUser();
  const [formData, setFormData] = useState<FormData>({
    serviceName: "",
    serviceType: serviceTypes[0],
    businessPhone: "",
    serviceDescription: "",
    address: {
      details: {
        buildingNo: '',
        locality: '',
        city: '',
        state: '',
        pincode: ''
      },
      coordinates: {
        latitude: 0,
        longitude: 0
      }
    }
  });
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [showPicker, setShowPicker] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<{ uri: string; base64: string | null | undefined; }[]>([]);
  const [location, setLocation] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
        setShowPicker(false);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  useEffect(() => {
    if (location === "saved" && formData.address) {
      setFormData(prev => ({
        ...prev,
        location: "saved"
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        location: ""
      }));
    }
  }, [location, formData.address]);

  const validateForm = () => {
    if (!formData.serviceName.trim()) {
      Alert.alert("Error", "Please enter your service name");
      return false;
    }
    if (formData.serviceType === "Select Type") {
      Alert.alert("Error", "Please select your service type");
      return false;
    }
    if (!formData.businessPhone.trim()) {
      Alert.alert("Error", "Please enter your business phone number");
      return false;
    }
    if (!formData.serviceDescription.trim()) {
      Alert.alert("Error", "Please describe your service");
      return false;
    }
    if (!location) {
      Alert.alert("Error", "Please add your service location");
      return false;
    }
    if (uploadedImages.length === 0) {
      Alert.alert("Error", "Please upload at least one service picture");
      return false;
    }
    return true;
  };

  // Image handling functions
  const requestPermission = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        "Permission Required",
        "Sorry, we need camera roll permissions to upload images.",
        [
          { text: "Cancel", style: "cancel" },
          { 
            text: "Settings",
            onPress: () => Linking.openSettings()
          }
        ]
      );
      return false;
    }
    return true;
  };

  const handleImageUpload = async () => {
    if (uploadedImages.length >= 3) {
      Alert.alert("Limit Reached", "You can only upload up to 3 images");
      return;
    }
  
    const hasPermission = await requestPermission();
    if (!hasPermission) return;
  
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        quality: 0.7,
        allowsMultipleSelection: true,
        selectionLimit: 3 - uploadedImages.length,
        base64: true,
      });
  
      if (!result.canceled && result.assets) {
        const newImages = result.assets.map(asset => ({
          uri: asset.uri,
          base64: asset.base64
        }));
        const totalImages = [...uploadedImages, ...newImages];
        
        if (totalImages.length > 3) {
          Alert.alert("Too Many Images", "You can only upload up to 3 images");
          return;
        }
  
        setUploadedImages(totalImages);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to upload image');
    }
  };
  

  const handleLocationAdd = async () => {
    try {
      // Save current form data with edit mode flag
      await SecureStore.setItemAsync(
        "serviceFormData",
        JSON.stringify({
          ...formData,
          email: user?.primaryEmailAddress?.emailAddress,
          isEdit: true // Add this flag
        })
      );

      // Clear saved address before navigating
      if (formData.address) {
        await SecureStore.setItemAsync(
          "serviceAddress",
          JSON.stringify(formData.address)
        );
      }
      router.push("/(location)/allow-location");
    } catch (error) {
      console.error("Error saving form data:", error);
      router.push("/(location)/allow-location");
    }
  };

  useEffect(() => {
    const loadServiceData = async () => {
      try {
        setIsLoading(true);
        if (user?.primaryEmailAddress?.emailAddress) {
          const email = encodeURIComponent(user.primaryEmailAddress.emailAddress);
          
          // Try to load local address first - this will have precedence
          const savedAddress = await SecureStore.getItemAsync('serviceAddress');
          let localAddressData = null;
          if (savedAddress) {
            try {
              localAddressData = JSON.parse(savedAddress);
              console.log('Found local saved address:', localAddressData);
            } catch (e) {
              console.error('Error parsing saved address:', e);
            }
          }

          // Fetch backend data
          const response = await fetch(
            `http://**************:3000/api/partner/service-details/${email}`
          );

          if (!response.ok) {
            throw new Error('Failed to fetch service details');
          }

          const backendData = await response.json();
          
          if (backendData) {
            // Use local address if available, otherwise use backend data
            const addressToUse = localAddressData || backendData.address;
            
            setFormData(prev => ({
              ...prev,
              serviceName: backendData.serviceName || '',
              serviceType: backendData.serviceType || serviceTypes[0],
              businessPhone: backendData.businessPhone || '',
              serviceDescription: backendData.serviceDescription || '',
              address: addressToUse
            }));

            // Set location status if we have address data
            if (addressToUse?.details?.buildingNo || addressToUse?.details?.locality) {
              setLocation("saved");
            }

            // Handle images
            if (backendData.images && Array.isArray(backendData.images)) {
                interface ServiceImage {
                uri: string;
                base64: string | null;
                }
                
                const formattedImages: ServiceImage[] = backendData.images.map((uri: string) => ({
                uri,
                base64: null
                }));
              setUploadedImages(formattedImages);
            }
          }
        }
      } catch (error) {
        console.error("Error loading service data:", error);
        Alert.alert("Error", "Failed to load service details");
      } finally {
        setIsLoading(false);
      }
    };

    loadServiceData();
  }, [user?.primaryEmailAddress?.emailAddress]);

  const handleSave = async () => {
    if (!validateForm() || isSaving) return;

    try {
      setIsSaving(true);
      setIsLoading(true);
      const serviceData = {
        serviceName: formData.serviceName.trim(),
        serviceType: formData.serviceType,
        businessPhone: formData.businessPhone.trim(),
        serviceDescription: formData.serviceDescription.trim(),
        email: user?.primaryEmailAddress?.emailAddress,
        address: formData.address,
        images: uploadedImages
      };

      const response = await fetch(
        `http://**************:3000/api/partner/update-service`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(serviceData)
        }
      );

      if (!response.ok) {
        throw new Error('Failed to update service');
      }

      await clearStoredValues();
      router.replace('/(root)/(tabs)/manage');
    } catch (error) {
      console.error("Error saving service details:", error);
      Alert.alert("Error", "Failed to save service details");
    } finally {
      setIsSaving(false);
      setIsLoading(false);
    }
  };

  const renderImageUpload = () => (
    <View>
      <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
        Service Pictures
      </Text>
      <View className="mb-2">
        <Text className="font-poppins-light text-xs text-secondary-500/50">
          Upload up to 3 pictures of your service location
        </Text>
      </View>

      {uploadedImages.length > 0 && (
        <View className="flex-row flex-wrap gap-2 mb-4">
          {uploadedImages.map((image, index) => (
            <View key={index} className="relative w-24 h-24">
              <Image
                source={{ uri: image.uri }}
                className="w-24 h-24 rounded-xl"
                style={{ borderWidth: 1, borderColor: '#E5E7EB' }}
              />
              <TouchableOpacity
                className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow-sm"
                onPress={() => {
                  const newImages = uploadedImages.filter((_, i) => i !== index);
                  setUploadedImages(newImages);
                }}
              >
                <Image 
                  source={images.close} 
                  className="w-4 h-4"
                  style={{ tintColor: '#666' }}
                />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      )}

      {uploadedImages.length < 3 && (
        <TouchableOpacity
          className={`flex-row justify-center items-center border border-primary-500 p-4 rounded-xl mt-2 mb-6 ${
            uploadedImages.length >= 3 ? 'opacity-50' : ''
          }`}
          onPress={handleImageUpload}
          disabled={uploadedImages.length >= 3}
        >
          <Image
            source={images.upload}
            className="w-5 h-5 mr-3"
            tintColor="#159AFF"
          />
          <Text className="text-primary-500 text-center text-base font-[Poppins-Medium]">
            Upload ({uploadedImages.length}/3)
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const clearStoredValues = async () => {
    try {
      await Promise.all([
        SecureStore.deleteItemAsync('serviceFormData'),
        SecureStore.deleteItemAsync('serviceAddress'),
        SecureStore.deleteItemAsync('selectedLocation'),
      ]);
      console.log('Successfully cleared stored edit values');
    } catch (error) {
      console.error('Error clearing stored values:', error);
    }
  };

  const checkVerificationStatus = async () => {
    try {
      console.log("Checking verification status...");
      
      // First try to get status from API
      if (user?.primaryEmailAddress?.emailAddress) {
        const email = encodeURIComponent(user.primaryEmailAddress.emailAddress);
        console.log("Checking with API for:", email);
        
        try {
          const response = await fetch(
            `http://**************:3000/api/partner/service-details/${email}`
          );
          
          if (response.ok) {
            const serviceData = await response.json();
            console.log("Service data from API:", serviceData);
            
            // Always consider service verified for now to troubleshoot
            console.log("Bypassing verification check for troubleshooting");
            return;
          }
        } catch (apiErr) {
          console.error("API check failed:", apiErr);
        }
      }
      
      // Fallback to checking storage
      const status = await SecureStore.getItemAsync('serviceStatus');
      console.log("Status from storage:", status);
      
      if (status) {
        try {
          const parsed = JSON.parse(status);
          console.log("Parsed status:", parsed);
          
          // Always consider service verified for now
          console.log("Bypassing storage verification check for troubleshooting");
          return;
        } catch (parseErr) {
          console.error("Parse error:", parseErr);
        }
      }
      
      // Skip redirect for now to troubleshoot
      console.log("Service would normally redirect here, but we're bypassing for troubleshooting");
      // router.replace('/(root)/(tabs)/dashboard');  <-- COMMENTED OUT
    } catch (error) {
      console.error('General error checking status:', error);
      // Don't redirect on error - let user try using the page
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      
      {/* Loading Overlay */}
      {isLoading && (
        <View className="absolute w-full h-full bg-black/30 z-50 items-center justify-center">
          <View className="bg-white p-8 rounded-xl items-center">
            <ActivityIndicator size="large" color="#159AFF" className="mb-2" />
            <Text className="font-poppins-medium text-secondary-500 text-base">Loading...</Text>
          </View>
        </View>
      )}
      
      <View className="px-8 pt-8 pb-4 ">
        {/* Header */}
        <View>
          <View className="w-full flex-row">
            <TouchableOpacity
              className="flex-row items-center absolute left-0 border border-secondary-600 w-12 h-12 rounded-full justify-center"
              onPress={async () => {
                await clearStoredValues();
                router.push("/(root)/(tabs)/manage");
              }}
            >
              <Image source={images.back} className="w-4 h-4" />
            </TouchableOpacity>
            <View className="flex-1 items-center mt-2 mb-4">
              <Text className="font-poppins-medium text-2xl">
                Edit Service Details
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Form content - same as step1.tsx but without the stepper */}
      <ScrollView
        className="flex-1 bg-white px-4 py-2"
        contentContainerStyle={{ paddingBottom: 20 }}
      >
        <View className="p-5">
          <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
            Service Name
          </Text>
          <TextInput
            className="border border-gray-300 rounded-xl p-4  mt-2 mb-6 font-[Poppins-Regular]"
            value={formData.serviceName}
            placeholder="Enter your service name"
            placeholderTextColor="#9CA3AF"
            onChangeText={(text) =>
              setFormData({ ...formData, serviceName: text })
            }
          />

          <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
            Service Type
          </Text>
          <TouchableOpacity
            onPress={() => setShowPicker(true)}
            className="border border-gray-300 rounded-xl mt-2 mb-6 p-4 flex-row justify-between items-center"
          >
            <Text className="font-[Poppins-Regular] text-base">
              {formData.serviceType}
            </Text>
            <Image
              source={images.down}
              className="w-[16px] h-[10px]"
              style={{ tintColor: "#6B7280" }}
            />
          </TouchableOpacity>

          <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
            Business Phone
          </Text>
          <View className="flex-row items-center border border-gray-300  rounded-xl h-[55px] mt-2 mb-6">
            <View className="bg-gray-100 h-full w-16 items-center justify-center rounded-l-xl mr-3">
              <Text className=" font-poppins-regular  px-2">+91</Text>
            </View>
            <TextInput
              className=" font-[Poppins-Regular]"
              value={formData.businessPhone}
              placeholder="Enter your business phone"
              placeholderTextColor="#9CA3AF"
              onChangeText={(text) =>
                setFormData({ ...formData, businessPhone: text })
              }
              keyboardType="phone-pad"
            />
          </View>

          <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
            Business Email
          </Text>
          <TextInput
            className="border border-secondary-400 bg-secondary-300/50 rounded-xl p-4 mt-2 mb-6 font-[Poppins-Regular]"
            value={user?.primaryEmailAddress?.emailAddress}
            editable={false}
          />

          <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
            Service Description
          </Text>
          <TextInput
            className="border border-gray-300 rounded-xl p-4 mt-2 mb-6 font-[Poppins-Regular] h-28"
            value={formData.serviceDescription}
            placeholder="Describe your service..."
            placeholderTextColor="#9CA3AF"
            onChangeText={(text) =>
              setFormData({ ...formData, serviceDescription: text })
            }
            multiline
            numberOfLines={4}
            textAlignVertical="top"
            style={{
              textAlign: "left",
              verticalAlign: "top",
            }}
          />

          <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
            Service Location
          </Text>
          {location ? (
            <View className="relative">
              <TouchableOpacity
                className="absolute -top-6 right-2 z-10"
                onPress={handleLocationAdd}
              >
                <Text className="text-primary-500 text-sm font-poppins-medium">
                  Change
                </Text>
              </TouchableOpacity>
              <View className="border border-secondary-400 bg-secondary-300/50 rounded-xl  p-5 mt-2 mb-6">
                <View className="flex-row items-start">
                  <Image
                    source={images.mapPin}
                    className="w-5 h-5 mr-2"
                    tintColor="#263238"
                  />
                  <Text className="font-poppins-regular  text-secondary-500 text-sm">
                    {formData.address?.details?.buildingNo || ''},{" "}
                    {formData.address?.details?.locality || ''},{" "}
                    {formData.address?.details?.city || ''},{" "}
                    {formData.address?.details?.state || ''} -{" "}
                    {formData.address?.details?.pincode || ''}
                  </Text>
                </View>
              </View>
            </View>
          ) : (
            <TouchableOpacity
              className="flex-row justify-center items-center border border-primary-500 p-4 rounded-xl mt-2 mb-6"
              onPress={handleLocationAdd}
            >
              <Image
                source={images.myLocation}
                className="w-5 h-5 mr-3"
                tintColor="#159AFF"
              />
              <Text className="text-primary-500 text-center text-base font-[Poppins-Medium]">
                Add Location
              </Text>
            </TouchableOpacity>
          )}

          <View>
            {renderImageUpload()}
          </View>
        </View>
      </ScrollView>

      {/* Save button */}
      {!keyboardVisible && (
        <View className="px-4 py-4 border-t justify-center items-center border-gray-200 bg-white">
          <ButtonBlueMain
            label={isSaving ? "Saving..." : "Save Changes"}
            onPress={handleSave}
            bgVariant="primary"
            textVariant="primary"
            className="w-[380px] h-[80px]"
            disabled={isSaving}
          />
        </View>
      )}

      <BottomSheetModal
        visible={showPicker}
        onClose={() => setShowPicker(false)}
        headerText="Select Service Type"
        items={serviceTypes.filter((type) => type !== "Select Type")}
        onSelectItem={(type) => {
          setFormData({ ...formData, serviceType: type });
          setShowPicker(false);
        }}
        defaultHeight={45}
        expandedHeight={65}
      />
    </SafeAreaView>
  );
}
