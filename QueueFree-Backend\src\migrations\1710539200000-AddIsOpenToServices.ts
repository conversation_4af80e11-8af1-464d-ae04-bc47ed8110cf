import { MigrationInterface, QueryRunner } from "typeorm";

export class AddIsOpenToServices1710539200000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "services" ADD COLUMN "isOpen" boolean DEFAULT false`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "services" DROP COLUMN "isOpen"`);
    }
}
