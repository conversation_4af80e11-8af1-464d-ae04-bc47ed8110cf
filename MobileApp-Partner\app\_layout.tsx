import { Stack } from "expo-router";
import 'react-native-get-random-values'; 
import { useFonts } from "expo-font";
import * as SplashScreen from "expo-splash-screen";
import { useEffect } from "react";
import { ClerkLoaded, ClerkProvider } from "@clerk/clerk-expo";
import Constants from "expo-constants";
import { tokenCache } from "@/lib/auth";
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import AsyncStorage from '@react-native-async-storage/async-storage';
// Import your global CSS file
import "../global.css";

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

const publishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!;

if (!publishableKey) {
  throw new Error(
    "Missing Publishable Key. Please set EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY in your .env"
  );
}

export default function RootLayout() {
  const [fontsLoaded] = useFonts({
    "Poppins-Light": require("../assets/fonts/poppins.light.ttf"),
    "Poppins-Regular": require("../assets/fonts/poppins.regular.ttf"),
    "Poppins-Medium": require("../assets/fonts/poppins.medium.ttf"),
    "Poppins-SemiBold": require("../assets/fonts/poppins.semibold.ttf"),
    "Poppins-Bold": require("../assets/fonts/poppins.bold.ttf"),
  });

  useEffect(() => {
    if (fontsLoaded) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded]);

  // Check if app was restarted due to subunit changes
  useEffect(() => {
    const checkReloadFlag = async () => {
      try {
        const needsReload = await AsyncStorage.getItem('app_needs_reload');
        if (needsReload === 'true') {
          console.log('App was restarted due to subunit changes');
          // Clear the flag
          await AsyncStorage.removeItem('app_needs_reload');
        }
      } catch (error) {
        console.error('Error checking reload flag:', error);
      }
    };

    checkReloadFlag();
  }, []);

  if (!fontsLoaded) {
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ClerkProvider tokenCache={tokenCache} publishableKey={publishableKey}>
        <ClerkLoaded>
          <Stack
            screenOptions={{
              headerShown: false,
            }}
          />
        </ClerkLoaded>
      </ClerkProvider>
    </GestureHandlerRootView>
  );
}