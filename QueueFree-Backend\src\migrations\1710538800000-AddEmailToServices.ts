import { MigrationInterface, QueryRunner } from "typeorm";

export class AddEmailToServices1710538800000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if email column exists
        const hasEmailColumn = await queryRunner.hasColumn('services', 'email');
        if (!hasEmailColumn) {
            // Add email column as nullable first
            await queryRunner.query(`ALTER TABLE "services" ADD COLUMN "email" character varying`);
        }

        // Clean up existing data if needed
        await queryRunner.query(`DELETE FROM "services" WHERE "email" IS NULL`);
        
        // Make email non-nullable
        await queryRunner.query(`ALTER TABLE "services" ALTER COLUMN "email" SET NOT NULL`);

        // Check if constraint exists before creating it
        const constraintExists = await queryRunner.query(`
            SELECT constraint_name 
            FROM information_schema.table_constraints 
            WHERE table_name = 'services' 
            AND constraint_name = 'UQ_services_email'
        `);

        if (!constraintExists.length) {
            await queryRunner.query(`ALTER TABLE "services" ADD CONSTRAINT "UQ_services_email" UNIQUE ("email")`);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove constraints first (if exists)
        await queryRunner.query(`
            DO $$ 
            BEGIN
                IF EXISTS (
                    SELECT 1 
                    FROM information_schema.table_constraints 
                    WHERE table_name = 'services' 
                    AND constraint_name = 'UQ_services_email'
                ) THEN
                    ALTER TABLE "services" DROP CONSTRAINT "UQ_services_email";
                END IF;
            END $$;
        `);
        
        // Make email nullable
        await queryRunner.query(`ALTER TABLE "services" ALTER COLUMN "email" DROP NOT NULL`);
        
        // Drop email column if it exists
        const hasEmailColumn = await queryRunner.hasColumn('services', 'email');
        if (hasEmailColumn) {
            await queryRunner.query(`ALTER TABLE "services" DROP COLUMN "email"`);
        }
    }
}
