import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddEstimatedServeTimeToQueue1720000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE queues ADD COLUMN "estimatedServeTime" TIMESTAMP WITH TIME ZONE NULL`
    );
    await queryRunner.query(
      `ALTER TABLE queues ADD COLUMN "waitTimeStatus" VARCHAR(255) DEFAULT 'on-time'`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE queues DROP COLUMN "estimatedServeTime"`);
    await queryRunner.query(`ALTER TABLE queues DROP COLUMN "waitTimeStatus"`);
  }
}
