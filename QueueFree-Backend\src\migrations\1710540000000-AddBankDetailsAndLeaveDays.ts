import { MigrationInterface, QueryRunner } from "typeorm";

export class AddBankDetailsAndLeaveDays1710540000000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create bank_details table
        await queryRunner.query(`
            CREATE TABLE "bank_details" (
                "id" SERIAL PRIMARY KEY,
                "serviceId" integer UNIQUE REFERENCES services(id) ON DELETE CASCADE,
                "accountNumber" character varying NOT NULL,
                "accountHolderName" character varying NOT NULL,
                "ifscCode" character varying NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now()
            )
        `);

        // Update leaveDays to be a simple array
        await queryRunner.query(`
            ALTER TABLE "service_setups" 
            ADD COLUMN "leaveDays" jsonb DEFAULT '[]'::jsonb
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "bank_details"`);
        await queryRunner.query(`ALTER TABLE "service_setups" DROP COLUMN "leaveDays"`);
    }
}
