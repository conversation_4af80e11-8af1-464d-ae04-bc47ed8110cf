import React, { useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Image,
  ScrollView,
  Animated,
  Easing,
  Share,
  Alert,
  ActivityIndicator,
} from "react-native";
import { Stack, useRouter, useLocalSearchParams } from "expo-router";
import { images } from "@/constants";
import LottieView from "lottie-react-native";

const PaymentSuccessScreen = () => {
  const router = useRouter();
  const {
    serviceId,
    serviceName,
    serviceType,
    selectedDate,
    selectedTimeSlot,
    fullName,
    mobileNumber,
    amount,
    queueId,
    transactionId,
    position,
    subUnitName,
    subUnitPrice,
    waitTimeMinutes,
  } = useLocalSearchParams();

  // State for real-time queue position
  const [queuePosition, setQueuePosition] = useState<number>(
    Number(position) || 0
  );
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // State for the unique slot ID (4-digit code)
  const [uniqueSlotId, setUniqueSlotId] = useState<string>("");

  // Animation references
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(100)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  // Animation for share button
  const [isSharePressed, setIsSharePressed] = useState(false);
  const shareScaleAnim = useRef(new Animated.Value(1)).current;

  // Fetch the unique slot ID for the queue
  const fetchUniqueSlotId = async () => {
    if (!queueId) return;

    try {
      console.log("Fetching unique slot ID for queue:", queueId);

      // Direct API call to get queue details - this API already uses Redis for fast retrieval
      const queueDetailResponse = await fetch(
        `http://**************:3000/api/customer/queue/${queueId}`
      );

      if (queueDetailResponse.ok) {
        const queueData = await queueDetailResponse.json();
        console.log("Queue data response:", queueData);

        if (
          queueData.status === "success" &&
          queueData.queue &&
          queueData.queue.uniqueSlotId
        ) {
          console.log("Found unique slot ID:", queueData.queue.uniqueSlotId);
          setUniqueSlotId(queueData.queue.uniqueSlotId);
        } else {
          // If the endpoint doesn't return the uniqueSlotId, try a different approach
          try {
            // Fetch using the joins API - this is another way to get slot details
            const joinsResponse = await fetch(
              `http://**************:3000/api/customer/queues/join/${queueId}`
            );

            if (joinsResponse.ok) {
              const joinsData = await joinsResponse.json();
              if (joinsData.uniqueSlotId) {
                console.log(
                  "Found unique slot ID from joins API:",
                  joinsData.uniqueSlotId
                );
                setUniqueSlotId(joinsData.uniqueSlotId);
                return;
              }
            }
          } catch (altError) {
            console.error("Alternative ID fetch failed:", altError);
          }

          // Generate a fallback ID if not found
          const fallbackId = Math.floor(1000 + Math.random() * 9000).toString();
          console.log("Using fallback ID:", fallbackId);
          setUniqueSlotId(fallbackId);
        }
      } else {
        // Try an alternative endpoint using the queue ID directly
        console.log("First endpoint failed, trying alternative...");
        try {
          // Check if it's in any active queues
          const activeQueuesResponse = await fetch(
            `http://**************:3000/api/customer/queues/active/${serviceId}/${selectedDate}`
          );

          if (activeQueuesResponse.ok) {
            const activeData = await activeQueuesResponse.json();
            if (activeData.status === "success" && activeData.queues) {
              // Find this queue by ID
              const matchingQueue = activeData.queues.find(
                (q: any) =>
                  q.id?.toString() === queueId?.toString() ||
                  q._id?.toString() === queueId?.toString()
              );

              if (matchingQueue && matchingQueue.uniqueSlotId) {
                console.log(
                  "Found unique slot ID from active queues:",
                  matchingQueue.uniqueSlotId
                );
                setUniqueSlotId(matchingQueue.uniqueSlotId);
                return;
              }
            }
          }
        } catch (altError) {
          console.error("Active queues endpoint error:", altError);
        }

        // If all else fails, use queueId as the uniqueSlotId
        if (queueId) {
          console.log("Using queueId as uniqueSlotId:", queueId);
          setUniqueSlotId(queueId.toString());
        } else {
          // Generate a fallback ID if nothing works
          const fallbackId = Math.floor(1000 + Math.random() * 9000).toString();
          console.log("Using fallback ID after all attempts:", fallbackId);
          setUniqueSlotId(fallbackId);
        }
      }
    } catch (error) {
      console.error("Error fetching unique slot ID:", error);
      // Use queueId as fallback if available
      if (queueId) {
        console.log(
          "Using queueId as fallback uniqueSlotId after error:",
          queueId
        );
        setUniqueSlotId(queueId.toString());
      } else {
        // Generate a fallback ID if fetch fails and no queueId
        const fallbackId = Math.floor(1000 + Math.random() * 9000).toString();
        console.log("Using generated fallback ID after error:", fallbackId);
        setUniqueSlotId(fallbackId);
      }
    }
  };

  useEffect(() => {
    // Start animations when component mounts
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        easing: Easing.out(Easing.exp),
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 800,
        easing: Easing.elastic(1),
        useNativeDriver: true,
      }),
    ]).start();

    // Fetch the latest queue position from Redis
    fetchLatestQueuePosition();

    // Fetch the unique slot ID
    fetchUniqueSlotId();
  }, []);

  // Function to fetch latest queue position with fast counting approach
  const fetchLatestQueuePosition = async () => {
    try {
      setIsLoading(true);

      // Step 1: Verify the userQueue exists and has necessary data
      if (queueId && serviceId && selectedDate && selectedTimeSlot) {
        console.log("Fetching latest queue position...");
        console.log("Queue ID:", queueId);
        console.log("Service ID:", serviceId);
        console.log("Date:", selectedDate);
        console.log("TimeSlot:", selectedTimeSlot);

        // First try to get the queue directly - this will have the most accurate position
        try {
          console.log("Fetching queue details for position");
          const queueResponse = await fetch(
            `http://**************:3000/api/customer/queue/${queueId}`
          );

          if (queueResponse.ok) {
            const queueData = await queueResponse.json();

            if (queueData.status === "success" && queueData.queue) {
              // Check if position is available directly from the queue data
              if (queueData.queue.position !== undefined && queueData.queue.position > 0) {
                console.log("Found position directly from queue data:", queueData.queue.position);
                setQueuePosition(queueData.queue.position);
                return;
              }
            }
          }
        } catch (error) {
          console.error("Error fetching queue details:", error);
          // Continue to next approach
        }

        // Next, try to get position from the positions endpoint
        try {
          console.log("Fetching position from positions endpoint");
          const positionsResponse = await fetch(
            `http://**************:3000/api/partner/services/${serviceId}/queue-positions/${selectedDate}/${selectedTimeSlot}`
          );

          if (positionsResponse.ok) {
            const positionsData = await positionsResponse.json();

            if (positionsData.status === "success" && positionsData.positions) {
              // Check if our queue ID is in the positions data
              const queueIdStr = queueId?.toString();
              if (queueIdStr && positionsData.positions[queueIdStr] !== undefined) {
                const ourPosition = positionsData.positions[queueIdStr];
                if (ourPosition > 0) {
                  console.log("Found position from positions endpoint:", ourPosition);
                  setQueuePosition(ourPosition);
                  return;
                }
              }
            }
          }
        } catch (error) {
          console.error("Error fetching from positions endpoint:", error);
          // Continue to next approach
        }

        // Fallback to active queues approach
        try {
          console.log("Fetching active queues for position calculation");
          const activeQueuesResponse = await fetch(
            `http://**************:3000/api/customer/queues/active/${serviceId}/${selectedDate}`
          );

          if (activeQueuesResponse.ok) {
            const activeQueuesData = await activeQueuesResponse.json();

            if (
              activeQueuesData.status === "success" &&
              activeQueuesData.queues
            ) {
              // Filter queues for this time slot
              const timeSlotQueues = activeQueuesData.queues.filter(
                (q: any) => q.timeSlot === selectedTimeSlot
              );

              // Sort queues: VIP first, then normal - both in order of creation time
              const vipQueues = timeSlotQueues.filter(
                (q: any) => q.isVIP === true
              );
              const normalQueues = timeSlotQueues.filter(
                (q: any) => q.isVIP !== true
              );

              vipQueues.sort(
                (a: any, b: any) =>
                  new Date(a.createdAt).getTime() -
                  new Date(b.createdAt).getTime()
              );

              normalQueues.sort(
                (a: any, b: any) =>
                  new Date(a.createdAt).getTime() -
                  new Date(b.createdAt).getTime()
              );

              // Combine VIP first, then normal
              const orderedQueues = [...vipQueues, ...normalQueues];

              // Find current queue's position (1-indexed)
              const queueIdToFind = queueId;
              const position =
                orderedQueues.findIndex((q: any) => {
                  try {
                    return (
                      q._id?.toString() === queueIdToFind?.toString() ||
                      q.id?.toString() === queueIdToFind?.toString()
                    );
                  } catch (error) {
                    console.error("Error comparing queue IDs:", error);
                    return false;
                  }
                }) + 1; // Add 1 because findIndex is 0-based

              console.log("Current position from active queues:", position);
              setQueuePosition(position > 0 ? position : 1);
              return;
            }
          }
        } catch (error) {
          console.error("Error fetching active queues:", error);
        }

        // Final fallback to estimated position if all other approaches fail
        console.log("Falling back to estimated position calculation");
        try {
          // Try to get time slots and estimate position
          const slotsResponse = await fetch(
            `http://**************:3000/api/partner/services/${serviceId}/time-slots/${selectedDate}`
          );

          if (slotsResponse.ok) {
            const slotsData = await slotsResponse.json();

            if (slotsData.status === "success" && slotsData.timeSlots) {
              const slotData = slotsData.timeSlots.find(
                (slot: any) => slot.timeSlot === selectedTimeSlot
              );

              if (slotData) {
                // Estimate position based on whether user is VIP or not
                // Since we don't know if the queue is VIP from just the queueId parameter,
                // default to showing the total position (worst case)
                const estimatedPosition = slotData.vipQueueCount + slotData.normalQueueCount;
                console.log("Estimated position from time slots:", estimatedPosition);
                setQueuePosition(estimatedPosition);
              }
            }
          }
        } catch (error) {
          console.error("Error fetching slots:", error);
          setQueuePosition(1); // Default fallback position
        }
      } else {
        console.warn("Missing data required for position calculation");
        setQueuePosition(1); // Default fallback position
      }
    } catch (error) {
      console.error("Error in fetchLatestQueuePosition:", error);
      setQueuePosition(1); // Default fallback position
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Fetch the latest queue position when component mounts
    fetchLatestQueuePosition();

    // Set up refresh interval for queue position (every 10 seconds)
    const intervalId = setInterval(() => {
      fetchLatestQueuePosition();
    }, 10000);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, []);

  useEffect(() => {
    if (isSharePressed) {
      Animated.sequence([
        Animated.timing(shareScaleAnim, {
          toValue: 0.9,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(shareScaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start(() => setIsSharePressed(false));
    }
  }, [isSharePressed]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    };
    return date.toLocaleDateString("en-US", options);
  };

  const handleViewQueue = () => {
    router.push("/(root)/(tabs)/queues");
  };

  const handleShare = async () => {
    setIsSharePressed(true);
    try {
      const formattedDate = formatDate(selectedDate as string);
      const message = `
🎟️ QueueFree - Skip The Line! 🎟️

I've booked a slot at ${serviceName}${subUnitName ? ` (${subUnitName})` : ""}
📅 ${formattedDate}
⏰ ${selectedTimeSlot}
👤 Queue Position: #${queuePosition}
${subUnitPrice ? `💰 Price: ₹${subUnitPrice}` : ""}

✳️ My Slot Code: ${uniqueSlotId || queueId}

Please show this code at the service counter.
      `;

      const result = await Share.share({
        message,
        title: "My QueueFree Slot",
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // Shared with activity type of result.activityType
          console.log(`Shared via ${result.activityType}`);
        } else {
          // Shared
          console.log("Shared successfully");
        }
      } else if (result.action === Share.dismissedAction) {
        // Dismissed
        console.log("Share dismissed");
      }
    } catch (error) {
      Alert.alert("Error", "Could not share the slot information");
      console.error("Share error:", error);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <ScrollView className="flex-1 px-10">
        {/* Animation Container */}
        <Animated.View
          className="items-center justify-center mt-5 mb-8"
          style={{
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          }}
        >
          <LottieView
            source={require("@/assets/animations/payment-success.json")}
            autoPlay
            loop={true}
            style={{ width: 300, height: 300 }}
          />
          <Text className="font-poppins-medium text-2xl -mt-16 text-primary-500">
            Payment Successful!
          </Text>
          <Text className="font-poppins-regular text-secondary-600 text-center mt-2 mb-4 px-6">
            You have Successfully Joined the Queue in {serviceName}
            {subUnitName ? ` (${subUnitName})` : ""} at slot {queuePosition}.
          </Text>
        </Animated.View>

        {/* Slot ID Card */}
        <Animated.View
          className="bg-primary-50 rounded-3xl border border-primary-100 p-6 mb-8"
          style={{
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          }}
        >
          <View className="flex-row flex-wrap items-center justify-between mb-6">
            <View className="flex-1">
              <Text className="font-poppins-medium text-secondary-600 mb-2">
                Unique Slot Code
              </Text>
              <Text className="font-poppins-regular text-xs text-secondary-600">
                Please show this code when you reach the service counter
              </Text>
            </View>

            <Animated.View style={{ transform: [{ scale: shareScaleAnim }] }}>
              <TouchableOpacity
                className="bg-primary-500 rounded-2xl px-4 py-3 ml-4 justify-center"
                onPress={handleShare}
                activeOpacity={0.7}
              >
                <View className="flex-row items-center">
                  <Image
                    source={images.share}
                    className="w-3 h-3 mr-2"
                    style={{ tintColor: "#ffff" }}
                  />
                  <Text className="font-poppins-medium text-white text-xs">
                    Share
                  </Text>
                </View>
              </TouchableOpacity>
            </Animated.View>
          </View>

          <View className="flex-row items-center justify-center">
            <View className="flex-row items-center justify-center bg-white rounded-xl p-4 mb-2 flex-1">
              {/* Display 4-digit slot code with each digit in a separate box */}
              {isLoading ? (
                <ActivityIndicator size="large" color="#5552E4" />
              ) : (
                (uniqueSlotId || queueId?.toString() || "0000")
                  .padStart(4, "0")
                  .slice(0, 4)
                  .split("")
                  .map((digit, index) => (
                    <View
                      key={index}
                      className="w-14 h-16 bg-primary-500/10 rounded-2xl mx-2 flex items-center justify-center border border-primary-500/20"
                    >
                      <Text className="font-poppins-bold text-2xl text-primary-500">
                        {digit}
                      </Text>
                    </View>
                  ))
              )}
            </View>
          </View>
        </Animated.View>

        {/* Transaction Details */}
        <Animated.View
          style={{
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          }}
        >
          <Text className="font-poppins-medium text-base text-secondary-600 mb-3">
            Transaction Details
          </Text>
          <View className="bg-white rounded-3xl border border-primary-500/10 p-6 mb-8">
            <View className="flex-row items-center mb-4">
              <Text className="text-secondary-600 flex-1">Transaction ID</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                {transactionId}
              </Text>
            </View>

            <View className="flex-row items-center mb-2">
              <Text className="text-secondary-600 flex-1">Amount Paid</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                ₹{parseFloat(amount as string).toFixed(2)}
              </Text>
            </View>


          </View>
        </Animated.View>

        {/* Transaction Details */}
        <Animated.View
          style={{
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          }}
        >
          <Text className="font-poppins-medium text-base text-secondary-600 mb-3">
            Schedule Details
          </Text>
          <View className="bg-white rounded-3xl border border-primary-500/10 p-6 mb-8">


            <View className="flex-row items-center mb-4">
              <Text className="text-secondary-600 flex-1">Date</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                {formatDate(selectedDate as string)}
              </Text>
            </View>

            <View className="flex-row items-center mb-4">
              <Text className="text-secondary-600 flex-1">Time</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                {selectedTimeSlot as string}
              </Text>
            </View>

            <View className="flex-row items-center mb-4">
              <Text className="text-secondary-600 flex-1">Service</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                {serviceName as string}
              </Text>
            </View>

            <View className="flex-row items-center mb-4">
              <Text className="text-secondary-600 flex-1">Queue Position</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                {queuePosition}
              </Text>
            </View>

            <View className="flex-row items-center mb-4">
              <Text className="text-secondary-600 flex-1">Estimated Wait</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                {waitTimeMinutes
                  ? parseInt(waitTimeMinutes as string) < 60
                    ? `${waitTimeMinutes} minutes`
                    : `${Math.floor(parseInt(waitTimeMinutes as string) / 60)} hour${Math.floor(parseInt(waitTimeMinutes as string) / 60) > 1 ? 's' : ''} ${parseInt(waitTimeMinutes as string) % 60 > 0 ? `${parseInt(waitTimeMinutes as string) % 60} minutes` : ''}`
                  : "Unknown"}
              </Text>
            </View>

            {subUnitName && (
              <View className="flex-row items-center mb-4">
                <Text className="text-secondary-600 flex-1">Sub-Unit</Text>
                <Text className="font-poppins-regular text-base text-secondary-500">
                  {subUnitName}
                </Text>
              </View>
            )}

            <View className="flex-row items-center mb-2">
              <Text className="text-secondary-600 flex-1">Name</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                {fullName as string}
              </Text>
            </View>
          </View>
        </Animated.View>
      </ScrollView>

      <View className="p-6 border-t flex justify-center items-center border-gray-200">
        <TouchableOpacity
          className="w-[390px] py-6 rounded-2xl items-center bg-primary-500"
          onPress={handleViewQueue}
        >
          <Text className="text-white font-poppins-medium text-lg">
            View My Queues
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default PaymentSuccessScreen;
