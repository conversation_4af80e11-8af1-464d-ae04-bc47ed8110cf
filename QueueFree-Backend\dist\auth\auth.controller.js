"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const customer_service_1 = require("../customer/customer.service");
const partner_service_1 = require("../partner/partner.service");
let AuthController = class AuthController {
    constructor(customerService, partnerService) {
        this.customerService = customerService;
        this.partnerService = partnerService;
        this.registrationInProgress = new Set();
    }
    async registerUser(userData) {
        if (this.registrationInProgress.has(userData.email)) {
            return {
                status: 'success',
                statusCode: common_1.HttpStatus.OK,
                message: 'Registration already in progress',
            };
        }
        try {
            this.registrationInProgress.add(userData.email);
            console.log('Starting user registration process for:', userData.email);
            console.log('User data received:', JSON.stringify(userData));
            const result = await this.customerService.createUser(userData.email, userData.fullName, userData.clerkId);
            console.log('User registration completed:', result);
            return {
                status: 'success',
                statusCode: common_1.HttpStatus.OK,
                message: 'User registered successfully',
                data: result
            };
        }
        catch (error) {
            console.error('User registration failed:', error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'User registration failed'
            }, common_1.HttpStatus.BAD_REQUEST);
        }
        finally {
            this.registrationInProgress.delete(userData.email);
        }
    }
    async registerPartner(partnerData) {
        if (this.registrationInProgress.has(partnerData.email)) {
            return {
                status: 'success',
                statusCode: common_1.HttpStatus.OK,
                message: 'Registration already in progress',
            };
        }
        try {
            this.registrationInProgress.add(partnerData.email);
            console.log('Starting partner registration process for:', partnerData.email);
            const result = await this.partnerService.createPartner(partnerData.email);
            console.log('Partner registration completed:', result);
            return {
                status: 'success',
                statusCode: common_1.HttpStatus.OK,
                message: 'Partner registered successfully',
                data: result
            };
        }
        catch (error) {
            console.error('Partner registration failed:', error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Partner registration failed'
            }, common_1.HttpStatus.BAD_REQUEST);
        }
        finally {
            this.registrationInProgress.delete(partnerData.email);
        }
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)('register-user'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "registerUser", null);
__decorate([
    (0, common_1.Post)('register-partner'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "registerPartner", null);
exports.AuthController = AuthController = __decorate([
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [customer_service_1.CustomerService,
        partner_service_1.PartnerService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map