import { Injectable, BadRequestException } from '@nestjs/common';
import { storage } from '../config/firebase.config';
import { getDownloadURL } from 'firebase-admin/storage';

@Injectable()
export class UploadService {
  async uploadImage(imageData: any, path: string): Promise<string> {
    try {
      console.log('Starting upload for path:', path);
      console.log('Image data type:', typeof imageData);

      let base64Data: string;

      if (typeof imageData === 'object') {
        if (!imageData.base64) {
          throw new Error('No base64 data found in image object');
        }
        base64Data = imageData.base64;
      } else if (typeof imageData === 'string') {
        base64Data = imageData;
      } else {
        throw new Error(`Invalid image data type: ${typeof imageData}`);
      }

      // Ensure base64 data is a string and properly formatted
      if (!base64Data || typeof base64Data !== 'string') {
        throw new Error('Invalid base64 string');
      }

      // Remove data URL prefix if present
      const base64String = base64Data.replace(/^data:image\/\w+;base64,/, '');

      // Create buffer from base64
      const buffer = Buffer.from(base64String, 'base64');

      // Create file in bucket
      const file = storage.bucket().file(path);
      await file.save(buffer, {
        metadata: {
          contentType: 'image/jpeg'
        }
      });

      // Get signed URL
      const [url] = await file.getSignedUrl({
        action: 'read',
        expires: '03-01-2500'
      });

      console.log('Upload successful, URL:', url);
      return url;
    } catch (error) {
      console.error('Error details:', error);
      throw new BadRequestException(`Failed to upload image: ${error.message}`);
    }
  }

  async uploadMultipleImages(images: Array<{ uri: string, base64: string }>): Promise<string[]> {
    try {
      console.log(`Attempting to upload ${images.length} images`);
      
      const uploadPromises = images.map((image, index) => {
        const path = `services/images/${Date.now()}-${index}.jpg`;
        return this.uploadImage(image, path);
      });

      return await Promise.all(uploadPromises);
    } catch (error) {
      console.error('Error uploading multiple images:', error);
      throw new BadRequestException('Failed to upload images');
    }
  }
}
