import { Module, Global, Inject, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Queue } from '../partner/entities/queue.entity';
import { ServiceSetup } from '../partner/entities/service-setup.entity';
import { ScheduleModule } from '@nestjs/schedule';
import { RedisModule } from './redis/redis.module';
import { QueueFlowService } from './queue-flow/queue-flow.service';
import { SchedulerService } from './scheduler/scheduler.service';
import { RedisService } from './redis/redis.service';
import { CustomerService } from '../customer/customer.service';

/**
 * This module provides common services across the application
 * to break circular dependencies between customer and scheduler modules
 */
@Global()
@Module({
  imports: [
    ScheduleModule.forRoot(),
    TypeOrmModule.forFeature([Queue, ServiceSetup]),
    RedisModule,
  ],
  providers: [
    QueueFlowService,
    {
      provide: SchedulerService,
      useFactory: (queueFlowService: QueueFlowService, redisService: RedisService) => {
        // Create a stub CustomerService to break circular dependency
        const stubCustomerService = {
          updateExpiredQueues: async () => {
            return { updated: 0, message: "Stub implementation - real method will be available at runtime" };
          }
        };
        
        // Pass the stub customer service to the scheduler
        return new SchedulerService(
          stubCustomerService as any,
          queueFlowService,
          redisService
        );
      },
      inject: [QueueFlowService, RedisService],
    },
  ],
  exports: [
    QueueFlowService,
    SchedulerService,
  ],
})
export class CommonServicesModule {} 