import React from 'react';
import { View, Text } from 'react-native';

interface StepperProps {
  currentStep: number;
  steps: string[];
}

export default function Stepper({ currentStep, steps }: StepperProps) {
  return (
    <View className="flex-row justify-between items-center py-5 px-4">
      {steps.map((step, index) => (
        <View key={index} className="items-center flex-1 relative">
          {/* Connecting Line */}
          {index < steps.length - 1 && (
            <View 
              className="absolute"
              style={{
                height: 2,
                width: '100%',
                backgroundColor: currentStep > index + 1 ? '#159AFF' : '#E5E7EB',
                top: 15,
                right: '-50%',
                zIndex: 0
              }}
            />
          )}
          
          {/* Circle */}
          <View 
            className={`w-[30px] h-[30px] rounded-full justify-center items-center border-2 border-white mb-1.5 z-0
              ${currentStep >= index + 1 ? 'bg-primary-500' : 'bg-gray-100'}`}
          >
            <Text className={`${currentStep >= index + 1 ? 'text-white' : 'text-gray-600'}`}>
              {index + 1}
            </Text>
          </View>
          
          {/* Label */}
          <Text className="text-sm text-gray-600">{step}</Text>
        </View>
      ))}
    </View>
  );
}
