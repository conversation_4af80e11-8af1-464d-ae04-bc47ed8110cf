import React from "react";
import { Text, SafeAreaView, View, StatusBar } from "react-native";
import { useAuth } from "@clerk/clerk-expo";
import { Redirect } from "expo-router";

const InsightsScreen = () => {
  const { isSignedIn } = useAuth();

  if (!isSignedIn) {
    return <Redirect href="/(auth)/sign-in" />;
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <View className="p-4">
        <Text className="text-2xl font-bold text-primary-500">Insights</Text>
        <Text className="text-base text-gray-600 mt-2">View your business analytics</Text>
      </View>
    </SafeAreaView>
  );
};

export default InsightsScreen;
