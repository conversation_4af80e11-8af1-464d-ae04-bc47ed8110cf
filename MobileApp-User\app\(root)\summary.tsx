import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Alert,
  Image,
  ScrollView,
} from "react-native";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import { useUser } from "@clerk/clerk-expo";
import { images } from "@/constants";
import BottomSheetModal from "@/components/BottomSheetModal";

interface ServiceDetails {
  id: string;
  serviceName: string;
  serviceType: string;
  address: string;
  rating: number;
  reviewCount: number;
  queueInfo: {
    cost: number;
  };
  images: string[];
}

interface TimeSlot {
  timeSlot: string;
  normalQueueCount: number;
  vipQueueCount: number;
}

// Add API base URL constant
const API_BASE_URL = "http://**************:3000/api";

const SummaryScreen = () => {
  const router = useRouter();
  const {
    queueId,
    isVIP,
    serviceDetails: serviceDetailsStr,
    timeSlotDetails: timeSlotDetailsStr,
    fullName,
    mobileNumber,
    selectedDate,
    selectedTimeSlot,
    serviceId,
    subUnitId,
    subUnitName,
    subUnitPrice,
    reschedule,
    oldQueueId,
  } = useLocalSearchParams();
  const { user } = useUser();

  const [isLoading, setIsLoading] = useState(false);
  const [serviceDetails, setServiceDetails] = useState<ServiceDetails | null>(
    serviceDetailsStr ? JSON.parse(serviceDetailsStr as string) : null
  );
  const [timeSlotDetails, setTimeSlotDetails] = useState<TimeSlot | null>(
    timeSlotDetailsStr ? JSON.parse(timeSlotDetailsStr as string) : null
  );
  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState<string>("gpay");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [servingTime, setServingTime] = useState<string>("10");
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);
  const [userPosition, setUserPosition] = useState<number>(1);
  // Add state for reschedule mode
  const [isRescheduleMode, setIsRescheduleMode] = useState(false);

  // Replace getImageSource with exact implementation from queues.tsx
  const getImageSource = () => {
    // Debug the service details we have
    console.log("Service details for image:", serviceDetails?.id);
    console.log("Service images:", serviceDetails?.images);

    // Check if images array exists and has at least one valid image
    if (
      serviceDetails?.images &&
      Array.isArray(serviceDetails.images) &&
      serviceDetails.images.length > 0 &&
      typeof serviceDetails.images[0] === "string" &&
      serviceDetails.images[0].trim() !== ""
    ) {
      console.log("Using image from service:", serviceDetails.images[0]);
      return { uri: serviceDetails.images[0] };
    }
    // Fallback to placeholder image
    console.log("Using placeholder image");
    return images.image;
  };

  useEffect(() => {
    if (serviceId) {
      const loadData = async () => {
        try {
          setIsLoading(true);

          // Check if this is reschedule mode
          if (reschedule === "true") {
            setIsRescheduleMode(true);
            console.log("Reschedule mode detected");
          }

          // Fetch full service details to get correct images
          await fetchFullServiceDetails();

          // Only fetch serving time and calculate position
          await Promise.all([fetchServingTime(), calculateYourPlace()]);
        } catch (error) {
          console.error("Error loading data:", error);
          Alert.alert(
            "Error",
            "Failed to load summary details. Please try again."
          );
        } finally {
          setIsLoading(false);
        }
      };

      loadData();
    }
  }, [serviceId]);

  // Add function to fetch complete service details with images
  const fetchFullServiceDetails = async () => {
    try {
      if (!serviceId) return;

      console.log("Fetching full service details for images");
      const response = await fetch(
        `${API_BASE_URL}/partner/services/${serviceId}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch service details");
      }

      const data = await response.json();
      console.log("Full service data received");

      // Process images
      if (data.images) {
        console.log("Raw images from API:", data.images);
        // Filter out any invalid URLs
        const processedImages = Array.isArray(data.images)
          ? data.images.filter(
              (url: string) => typeof url === "string" && url.trim() !== ""
            )
          : [];

        data.images = processedImages;
        console.log("Processed images:", processedImages);
      } else {
        data.images = [];
        console.log("No images in service data");
      }

      // Update service details while preserving existing data
      setServiceDetails((prevDetails) => ({
        ...prevDetails,
        ...data,
        images: data.images || [],
      }));
    } catch (error) {
      console.error("Error fetching full service details:", error);
    }
  };

  const fetchServingTime = async () => {
    try {
      const setupResponse = await fetch(
        `http://**************:3000/api/partner/service-setup/${serviceId}`
      );

      if (setupResponse.ok) {
        const setupData = await setupResponse.json();
        if (setupData.data?.servingTime) {
          setServingTime(setupData.data.servingTime);
        }
      }
    } catch (error) {
      console.error("Error fetching serving time:", error);
      throw error;
    }
  };

  const calculateYourPlace = async () => {
    try {
      if (!serviceId || !selectedDate || !selectedTimeSlot) {
        throw new Error("Missing required parameters");
      }

      const position = await fetchQueuePosition(
        serviceId as string,
        selectedDate as string,
        selectedTimeSlot as string,
        isVIP === "true"
      );

      setUserPosition(position);
      return position;
    } catch (error) {
      console.error("Error calculating position:", error);
      throw error;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    };
    return date.toLocaleDateString("en-US", options);
  };

  const fetchQueuePosition = async (
    serviceId: string,
    date: string,
    timeSlot: string,
    isVIP: boolean = false
  ) => {
    try {
      const start = performance.now();
      console.log("Fetching queue position using position endpoint");

      // First try to get position directly from the position endpoint
      try {
        // This endpoint uses the same mechanism as the backend position calculation
        const positionResponse = await fetch(
          `http://**************:3000/api/partner/services/${serviceId}/positions/${date}/${timeSlot}`
        );

        if (positionResponse.ok) {
          const positionData = await positionResponse.json();

          if (positionData.status === "success") {
            // Calculate next position based on VIP status with enhanced logic
            const vipCount = positionData.vipCount || 0;
            const normalCount = positionData.normalCount || 0;
            const totalCount = vipCount + normalCount;

            // Calculate position based on VIP status
            let position = 0;
            if (isVIP) {
              // Note: For endpoint-based calculation, we use simplified logic
              // The backend will handle the detailed VIP positioning logic
              // This is just for display estimation
              if (totalCount === 0) {
                position = 1; // First in empty queue
              } else {
                position = vipCount + 1; // After existing VIPs (simplified)
              }
            } else {
              // For normal: position is after all VIPs and existing normals
              position = totalCount + 1;
            }

            console.log(
              `Position calculation from endpoint: Position=${position}, VIP=${vipCount}, Normal=${normalCount}, Time=${Math.round(performance.now() - start)}ms`
            );

            return position;
          }
        }
      } catch (error) {
        console.error("Error fetching from position endpoint:", error);
        // Continue to fallback method
      }

      // Fallback: Use the active queues endpoint to determine position
      console.log("Falling back to active queues approach");
      const activeQueuesResponse = await fetch(
        `http://**************:3000/api/customer/queues/active/${serviceId}/${date}`
      );

      if (!activeQueuesResponse.ok) {
        console.error(
          `Failed to fetch active queues: ${activeQueuesResponse.status}`
        );
        return 1; // Default to position 1 if request fails
      }

      const activeQueuesData = await activeQueuesResponse.json();

      if (activeQueuesData.status === "success" && activeQueuesData.queues) {
        // Filter to get queues for this time slot only
        const timeSlotQueues = activeQueuesData.queues.filter(
          (q: any) => q && q.timeSlot === timeSlot
        );

        // First get all the VIP queues (they go first)
        const vipQueues = timeSlotQueues.filter(
          (q: any) => q && q.isVIP === true
        );

        // Then get all normal queues
        const normalQueues = timeSlotQueues.filter(
          (q: any) => q && q.isVIP !== true
        );

        // Count queues
        const vipCount = vipQueues.length;
        const normalCount = normalQueues.length;

        // Enhanced VIP positioning logic to match backend
        let position = 0;
        if (isVIP) {
          console.log(`🔍 VIP Position Calculation Debug:`);
          console.log(`- All queues in slot:`, JSON.stringify(timeSlotQueues, null, 2));
          console.log(`- Total queues in slot:`, timeSlotQueues.length);

          // Enhanced VIP positioning logic
          const firstPersonInQueue = timeSlotQueues.find((q: any) => q.position === 1);

          console.log(`- First person in queue (position=1):`, JSON.stringify(firstPersonInQueue, null, 2));
          console.log(`- First person status:`, firstPersonInQueue?.status);
          console.log(`- First person currentlyServing:`, firstPersonInQueue?.currentlyServing);

          if (!firstPersonInQueue && timeSlotQueues.length === 0) {
            // Truly empty queue, VIP gets position 1
            position = 1;
            console.log(`✅ VIP gets position ${position} - truly empty queue`);
          } else if (!firstPersonInQueue && timeSlotQueues.length > 0) {
            // Queue exists but no position 1 found - might be different position system
            // Sort by position or use first queue member
            const sortedQueues = timeSlotQueues.sort((a: any, b: any) => (a.position || 0) - (b.position || 0));
            const actualFirstPerson = sortedQueues[0];

            console.log(`- Actual first person (sorted):`, JSON.stringify(actualFirstPerson, null, 2));

            if (actualFirstPerson?.status === 'waiting' || (!actualFirstPerson?.currentlyServing && actualFirstPerson?.status !== 'serving')) {
              // First person is waiting, VIP can jump ahead
              position = 1;
              console.log(`✅ VIP gets position ${position} - first person waiting (status: ${actualFirstPerson?.status}, serving: ${actualFirstPerson?.currentlyServing})`);
            } else if (actualFirstPerson?.status === 'serving' || actualFirstPerson?.currentlyServing === true) {
              // First person is being served, check for existing VIPs
              const existingVips = timeSlotQueues.filter((q: any) => q.isVIP && q.status === 'waiting');
              console.log(`- Existing VIPs waiting:`, existingVips.length);

              if (existingVips.length === 0) {
                // No VIPs waiting, new VIP gets position 2
                position = 2;
                console.log(`✅ VIP gets position ${position} - first person serving, no VIPs waiting`);
              } else {
                // VIPs already waiting, new VIP joins after the last VIP but before any normal members
                // Count: 1 (serving) + existing VIPs + 1 (new VIP)
                position = 1 + existingVips.length + 1;
                console.log(`✅ VIP gets position ${position} - after ${existingVips.length} existing VIPs, before normal members`);
              }
            } else {
              // Fallback: VIP goes after existing VIPs
              position = vipCount + 1;
              console.log(`⚠️ VIP gets position ${position} - fallback after VIPs`);
            }
          } else if (firstPersonInQueue?.status === 'waiting' || (!firstPersonInQueue?.currentlyServing && firstPersonInQueue?.status !== 'serving')) {
            // First person is waiting, VIP can jump ahead
            position = 1;
            console.log(`✅ VIP gets position ${position} - first person waiting (status: ${firstPersonInQueue.status}, serving: ${firstPersonInQueue.currentlyServing})`);
          } else if (firstPersonInQueue?.status === 'serving' || firstPersonInQueue?.currentlyServing === true) {
            // First person is being served, check for VIPs waiting behind them
            const vipQueuesWaiting = timeSlotQueues.filter((q: any) => q.isVIP && q.status === 'waiting');

            console.log(`- VIPs currently waiting:`, vipQueuesWaiting.length);

            if (vipQueuesWaiting.length === 0) {
              // No VIPs waiting, new VIP gets position 2
              position = 2;
              console.log(`✅ VIP gets position ${position} - first person serving, no VIPs waiting`);
            } else {
              // VIPs already waiting, new VIP joins after the last VIP but before normal members
              position = 1 + vipQueuesWaiting.length + 1;
              console.log(`✅ VIP gets position ${position} - after ${vipQueuesWaiting.length} existing VIPs, before normal members`);
            }
          } else {
            // Fallback to original logic if status is unclear
            position = vipCount + 1;
            console.log(`⚠️ VIP gets position ${position} - fallback logic (status: ${firstPersonInQueue?.status})`);
          }
        } else {
          // For normal: position is after all VIPs and existing normals
          position = vipCount + normalCount + 1;
          console.log(`✅ Normal user gets position ${position} - after all VIPs and normals`);
        }

        console.log(
          `Position calculation from active queues: Position=${position}, VIP=${vipCount}, Normal=${normalCount}, Time=${Math.round(performance.now() - start)}ms`
        );

        return position;
      }

      return 1; // Default position if no data available
    } catch (error) {
      console.error("Error calculating queue position:", error);
      return 1; // Default position on error
    }
  };

  // State for estimated wait time
  const [estimatedWaitTime, setEstimatedWaitTime] = useState<number | null>(null);
  const [isLoadingWaitTime, setIsLoadingWaitTime] = useState<boolean>(false);

  // Function to fetch estimated wait time from backend after joining queue
  const fetchEstimatedWaitTime = async (queueId: string) => {
    try {
      setIsLoadingWaitTime(true);
      console.log(`Fetching estimated wait time for queue ${queueId}`);

      const response = await fetch(
        `${API_BASE_URL}/customer/queue/${queueId}/estimated-wait-time`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch estimated wait time");
      }

      const data = await response.json();

      if (data.status === "success" && data.data) {
        console.log(`Received wait time data: ${JSON.stringify(data.data)}`);
        setEstimatedWaitTime(data.data.waitTimeMinutes);
        return data.data.waitTimeMinutes;
      } else {
        throw new Error("Invalid wait time data received");
      }
    } catch (error) {
      console.error("Error fetching estimated wait time:", error);
      return null;
    } finally {
      setIsLoadingWaitTime(false);
    }
  };

  // Format wait time for display
  const formatWaitTime = (waitTimeMinutes: number | null) => {
    if (waitTimeMinutes === null) {
      // Fallback to position-based calculation if API call fails
      if (!userPosition || userPosition <= 0 || !serviceDetails?.queueInfo)
        return "Unknown";

      // Get correct serving time from service setup or use default
      const servingTimeMinutes = parseInt(servingTime, 10) || 10;

      // Calculate wait time based on position (subtract 1 because current position doesn't wait)
      waitTimeMinutes = (userPosition - 1) * servingTimeMinutes;
    }

    if (waitTimeMinutes <= 0) {
      return "Immediate";
    } else if (waitTimeMinutes < 60) {
      return `${waitTimeMinutes} minutes`;
    } else {
      const hours = Math.floor(waitTimeMinutes / 60);
      const minutes = waitTimeMinutes % 60;
      return `${hours} hour${hours > 1 ? "s" : ""} ${minutes > 0 ? `${minutes} minutes` : ""}`;
    }
  };

  const calculateTotalAmount = () => {
    if (!serviceDetails) return { subtotal: 0, gst: 0, reschedule: 0, total: 0 };

    // Use subunit price if available, otherwise use the default service price
    let subtotal = 0;

    if (
      subUnitPrice &&
      typeof subUnitPrice === "string" &&
      !isNaN(parseFloat(subUnitPrice))
    ) {
      // Use the subunit price
      subtotal = parseFloat(subUnitPrice);
      console.log(`Using subunit price for calculation: ₹${subtotal}`);
    } else {
      // Fallback to service default price
      subtotal = serviceDetails.queueInfo.cost || 0;
      console.log(`Using default service price for calculation: ₹${subtotal}`);
    }

    // Add 10% reschedule fee if in reschedule mode
    const rescheduleRate = isRescheduleMode ? 0.10 : 0; // 10% reschedule fee
    const rescheduleFee = subtotal * rescheduleRate;

    const gstRate = 0.18; // 18% GST
    const gst = (subtotal + rescheduleFee) * gstRate; // GST on subtotal + reschedule fee
    const total = subtotal + rescheduleFee + gst;

    return {
      subtotal,
      reschedule: rescheduleFee,
      gst,
      total,
    };
  };

  const handlePayment = async () => {
    try {
      setIsSubmitting(true);

      // Prepare queue join request body
      const joinQueueBody: any = {
        serviceId,
        userId: user?.id,
        userName: fullName || user?.fullName || "Anonymous",
        mobileNumber,
        date: selectedDate,
        timeSlot: selectedTimeSlot,
        isVIP: isVIP === "true",
        amount: total,
      };

      // Add subunit information if available
      if (subUnitId) {
        joinQueueBody.hasSubUnits = true;
        joinQueueBody.subUnitId = subUnitId;
        joinQueueBody.subUnitName = subUnitName;
        console.log(
          `Including subunit in queue join request: ${subUnitName} (ID: ${subUnitId})`
        );
      }

      // Join queue first
      console.log("Creating queue with VIP status:", isVIP);
      const queueResponse = await fetch(
        `${API_BASE_URL}/customer/queues/join`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(joinQueueBody),
        }
      );

      if (!queueResponse.ok) {
        throw new Error("Failed to join queue");
      }

      const queueData = await queueResponse.json();

      if (queueData.status !== "success") {
        throw new Error(queueData.message || "Failed to join queue");
      }

      // Get queue ID from response
      const queueId = queueData.id || queueData.queueId;

      if (!queueId) {
        throw new Error("No queue ID returned from server");
      }

      console.log("Queue joined successfully with ID:", queueId);

      // Fetch estimated wait time from backend
      let waitTimeMinutes = null;
      try {
        waitTimeMinutes = await fetchEstimatedWaitTime(queueId.toString());
        console.log(`Fetched estimated wait time: ${waitTimeMinutes} minutes`);
      } catch (error) {
        console.error("Error fetching wait time, will use fallback calculation:", error);
      }

      // Generate a transaction ID for payment record
      const transactionId = `TXN-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

      // Prepare params for payment success screen
      const successParams: any = {
        serviceId: serviceId as string,
        serviceName: serviceDetails?.serviceName,
        serviceType: serviceDetails?.serviceType,
        selectedDate: selectedDate as string,
        selectedTimeSlot: selectedTimeSlot as string,
        fullName: fullName as string,
        mobileNumber: mobileNumber as string,
        amount: total.toFixed(2),
        queueId: queueId.toString(),
        transactionId,
        // Use the position from the queue response if available, otherwise use calculated position
        position: queueData.position?.toString() || userPosition.toString(),
        // Include estimated wait time if available
        waitTimeMinutes: waitTimeMinutes !== null ? waitTimeMinutes.toString() : null,
      };

      // Add subunit information if available
      if (subUnitName) {
        successParams.subUnitName = subUnitName;
        successParams.subUnitPrice = subUnitPrice;
        console.log(
          `Passing subunit to payment success: ${subUnitName} (Price: ${subUnitPrice})`
        );
      }

      // Add reschedule information if applicable
      if (isRescheduleMode) {
        successParams.reschedule = "true";
        successParams.oldQueueId = oldQueueId;
        successParams.rescheduleFee = rescheduleFee.toFixed(2);
        console.log("Passing reschedule information to payment success");
      }

      // Navigate to payment success screen with all required data
      router.push({
        pathname: "/(root)/payment-success",
        params: successParams,
      });
    } catch (error: any) {
      console.error("Error processing payment:", error);
      Alert.alert("Error", error.message || "Failed to process payment");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditDetails = () => {
    router.push({
      pathname: "/(root)/enter-details",
      params: {
        serviceId,
        selectedDate,
        selectedTimeSlot,
        fullName,
        mobileNumber,
        fromSummary: "true",
      },
    });
  };

  const openPaymentModal = () => {
    setPaymentModalVisible(true);
  };

  const { subtotal, reschedule: rescheduleFee, gst, total } = calculateTotalAmount();

  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#159AFF" />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <View className="bg-white pb-4">
        <View className="px-8 pt-12">
          <View className="flex-row justify-start mt-4">
            <TouchableOpacity
              className="flex-row items-center absolute left-0 -top-2 border border-secondary-600 w-12 h-12 rounded-full justify-center"
              onPress={() => router.back()}
            >
              <Image source={images.back} className="w-4 h-4" />
            </TouchableOpacity>

            <View className="flex ml-20 items-center">
              <Text className="font-poppins-medium text-xl mb-2">
                {isRescheduleMode ? "Reschedule Summary" : "Booking Summary"}
              </Text>
            </View>
          </View>
        </View>
      </View>

      <ScrollView className="flex-1 px-10">
        <Text className="font-poppins-medium text-base mt-6 text-secondary-600 mb-3">
          Service
        </Text>
        {/* Service Card */}
        {serviceDetails && (
          <View className="mb-6  bg-white rounded-3xl border border-primary-500/10 p-6">
            <View className="flex-row items-center justify-start">
              <Image
                source={getImageSource()}
                className="w-16 h-16 rounded-xl mr-5"
                style={{ backgroundColor: "#f5f5f5" }}
              />
              <View className="flex-col items-start">
                <View className="flex-row flex-wrap items-center mr-2 flex-1">
                  <Text
                    className="font-poppins-medium text-lg"
                    numberOfLines={2}
                    style={{ maxWidth: "85%" }}
                  >
                    {serviceDetails.serviceName}
                  </Text>
                  {/* Rating display */}
                  {serviceDetails.rating ? (
                    <View className="flex-row items-center  bg-warning-500/10 rounded-lg px-[5px] py-[3px] ml-2">
                      <Image
                        source={images.star}
                        className="w-[10px] h-[10px] mr-1 mb-1"
                      />
                      <Text className="text-warning-500 font-poppins-medium text-[10px]">
                        {serviceDetails.rating.toFixed(1)}
                      </Text>
                    </View>
                  ) : null}
                </View>
                <Text className="text-secondary-500 text-sm">
                  {serviceDetails.serviceType}
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Reschedule Indicator */}
        {isRescheduleMode && (
          <View className="mb-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
            <View className="flex-row items-center">
              <View className="w-3 h-3 bg-blue-500 rounded-full mr-3" />
              <Text className="font-poppins-medium text-blue-800 text-base">
                Queue Reschedule
              </Text>
            </View>
            <Text className="text-blue-600 text-sm mt-2 ml-6">
              You are rescheduling your existing queue. A 10% reschedule fee will be applied.
            </Text>
          </View>
        )}

        {/* Schedule Details */}
        <View className="mb-6">
          <Text className="font-poppins-medium text-base text-secondary-600 mb-3">
            Schedule
          </Text>
          <View className="bg-white rounded-3xl border border-primary-500/10 p-6">
            {/* Display subunit information if available */}
            {subUnitName && (
              <View className="flex-row items-center mb-4">
                <Text className="text-secondary-600 flex-1">Sub-Unit</Text>
                <Text className="font-poppins-regular text-base text-secondary-500">
                  {subUnitName}
                </Text>
              </View>
            )}

            <View className="flex-row items-center mb-4">
              <Text className="text-secondary-600 flex-1">Date</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                {selectedDate ? formatDate(selectedDate as string) : ""}
              </Text>
            </View>

            <View className="flex-row items-center mb-4">
              <Text className="text-secondary-600 flex-1">Time</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                {selectedTimeSlot as string}
              </Text>
            </View>

            <View className="flex-row items-center mb-4">
              <Text className="text-secondary-600 flex-1">Your Place</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                {userPosition}
              </Text>
            </View>

            <View className="flex-row items-center mb-2">
              <Text className="text-secondary-600 flex-1">Estimated Wait</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                {formatWaitTime(estimatedWaitTime)}
              </Text>
            </View>
          </View>
        </View>

        {/* Your Details */}
        <View className="mb-6">
          <View className="flex-row justify-between items-center mb-3">
            <Text className="font-poppins-medium text-base text-secondary-600">
              Your Details
            </Text>
            <TouchableOpacity onPress={handleEditDetails}>
              <Text className="text-primary-500 font-poppins-medium">Edit</Text>
            </TouchableOpacity>
          </View>

          <View className="bg-white rounded-3xl  border border-primary-500/10 p-6">
            {isVIP === "true" && (
              <View className="flex-row items-center mb-4 bg-warning-100 p-3 rounded-lg">
                <Image
                  source={images.vip}
                  className="w-6 h-6 mr-2"
                  tintColor={"#A16207"}
                />
                <Text className="text-warning-700 font-poppins-medium">
                  VIP
                </Text>
              </View>
            )}
            <View className="flex-row items-center mb-4">
              <Text className="text-secondary-600 flex-1">Name</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                {fullName as string}
              </Text>
            </View>

            <View className="flex-row items-center">
              <Text className="text-secondary-600 flex-1 mb-2">Mobile</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                +91 {mobileNumber as string}
              </Text>
            </View>
          </View>
        </View>

        {/* Payment Method */}
        <View className="mb-6">
          <Text className="font-poppins-medium text-base text-secondary-600 mb-3">
            Payment Method
          </Text>
          <TouchableOpacity
            className="bg-white rounded-3xl border border-primary-500/10 p-6 flex-row items-center justify-between"
            onPress={openPaymentModal}
          >
            <View className="flex-row items-center">
              {selectedPaymentMethod === "gpay" && (
                <Image source={images.gpay} className="w-11 h-9 mr-3" />
              )}
              {selectedPaymentMethod === "paytm" && (
                <Image source={images.paytm} className="w-10 h-3 mr-3" />
              )}
              {selectedPaymentMethod === "phonepe" && (
                <Image source={images.phonepe} className="w-9 h-9 mr-3" />
              )}
              <Text className="font-poppins-medium text-secondary-600">
                {selectedPaymentMethod === "gpay"
                  ? "Google Pay"
                  : selectedPaymentMethod === "paytm"
                    ? "Paytm"
                    : "PhonePe"}
              </Text>
            </View>
            <Text className="text-primary-500 font-poppins-medium">Change</Text>
          </TouchableOpacity>
        </View>

        {/* Payment Summary */}
        <View className="mb-8">
          <Text className="font-poppins-medium text-base text-secondary-600 mb-3">
            Payment Summary
          </Text>
          <View className="bg-white rounded-3xl border border-primary-500/10 p-6">
            <View className="flex-row justify-between mb-4">
              <Text className="text-secondary-600 flex-1">Subtotal</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                ₹{subtotal.toFixed(2)}
              </Text>
            </View>

            {/* Show reschedule fee if in reschedule mode */}
            {isRescheduleMode && rescheduleFee > 0 && (
              <View className="flex-row justify-between mb-4">
                <Text className="text-secondary-600 flex-1">Reschedule Fee (10%)</Text>
                <Text className="font-poppins-regular text-base text-secondary-500">
                  ₹{rescheduleFee.toFixed(2)}
                </Text>
              </View>
            )}

            <View className="flex-row justify-between mb-4">
              <Text className="text-secondary-600 flex-1">GST (18%)</Text>
              <Text className="font-poppins-regular text-base text-secondary-500">
                ₹{gst.toFixed(2)}
              </Text>
            </View>

            <View className="border-t border-gray-200 mb-4" />

            <View className="flex-row justify-between">
              <Text className="font-poppins-medium text-secondary-700 flex-1">
                Total
              </Text>
              <Text className="font-poppins-medium text-primary-500 text-lg">
                ₹{total.toFixed(2)}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>

      <View className="p-6 border-t flex justify-center items-center border-gray-200">
        <TouchableOpacity
          className="w-[390px] py-6 rounded-2xl items-center bg-primary-500"
          onPress={handlePayment}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Text className="text-white font-poppins-medium text-lg">
              Pay ₹{total.toFixed(2)}
            </Text>
          )}
        </TouchableOpacity>
      </View>

      <BottomSheetModal
        visible={paymentModalVisible}
        onClose={() => setPaymentModalVisible(false)}
        headerText="Select Payment Method"
        items={[
          {
            id: "gpay",
            label: "Google Pay",
            icon: images.gpay,
            iconClassName: "w-11 h-9",
          },
          {
            id: "paytm",
            label: "Paytm",
            icon: images.paytm,
            iconClassName: "w-10 h-3",
          },
          {
            id: "phonepe",
            label: "PhonePe",
            icon: images.phonepe,
            iconClassName: "w-9 h-9",
          },
        ]}
        onSelectItem={(item) => {
          setSelectedPaymentMethod(item);
          setPaymentModalVisible(false);
        }}
        selectedItem={selectedPaymentMethod}
        defaultHeight={50}
        expandedHeight={70}
        showBackdrop={true}
        backdropOpacity={0.5}
      />
    </SafeAreaView>
  );
};

export default SummaryScreen;
