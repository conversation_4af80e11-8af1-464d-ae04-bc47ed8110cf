import { View, TouchableOpacity, Text } from "react-native";

type Props = {
  label: string;
  onPress?: () => void;
  bgVariant?: "primary" | "secondary" | "danger" | "outline" | "success";
  textVariant?: "primary" | "default" | "secondary" | "danger" | "success";
  disabled?: boolean;
  className?: string;
};

const getBgVariantStyle = (variant: string) => {
  switch (variant) {
    case "primary":
      return "bg-primary-500";
    case "secondary":
      return "bg-white";
    case "danger":
      return "bg-red-500";
    case "outline":
      return "bg-transparent border border-gray-500";
    case "success":
      return "bg-green-500";
    default:
      return "bg-primary-500";
  }
};

const getTextVariantStyle = (variant: string) => {
  switch (variant) {
    case "primary":
      return "text-white";
    case "secondary":
      return "text-secondary-500";
    case "danger":
      return "text-red-500";
    case "success":
      return "text-green-500";
    default:
      return "text-black";
  }
};

export default function ButtonWhite({
  label,
  onPress,
  bgVariant = "secondary",
  textVariant = "default",
  disabled = false,
  className = "",
}: Props) {
  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={0.7}
      className={`rounded-xl w-full h-[60px] items-center justify-center p-5 ${getBgVariantStyle(bgVariant)} ${className}`}
      disabled={disabled}
    >
      <Text
        className={`text-center font-poppins-medium text-lg font-medium ${getTextVariantStyle(textVariant)}`}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );
}
