{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "queuefree", "version": "1.0.0", "orientation": "portrait", "scheme": "myapp", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "config": {"googleMapsApiKey": "AIzaSyAUIQg4PbIdD4MuC2PWJbo1W7njP902gpI"}, "bundleIdentifier": "com.app.queuefreepartner", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#000000"}, "config": {"googleMaps": {"apiKey": "AIzaSyAUIQg4PbIdD4MuC2PWJbo1W7njP902gpI"}}, "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.INTERNET"], "package": "com.app.queuefreepartner"}, "web": {"bundler": "metro", "output": "server", "favicon": "./assets/images/icon.png"}, "plugins": ["expo-router", "expo-font", "expo-secure-store", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow QueueFree to use your location."}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "1cd949ca-c6af-46af-9cb8-590c7e950d92"}}, "owner": "selfie_jones", "runtimeVersion": "1.0.0", "updates": {"url": "https://u.expo.dev/1cd949ca-c6af-46af-9cb8-590c7e950d92"}}}