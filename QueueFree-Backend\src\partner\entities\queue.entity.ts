import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn } from 'typeorm';
import { Service } from './service.entity';

@Entity('queues')
export class Queue {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Service, service => service.queues)
  service: Service;

  @Column()
  serviceId: number;

  @Column({ default: 'waiting' })
  status: string;

  @Column({ default: false })
  isVIP: boolean;

  @Column({ type: 'date', nullable: true })
  date: Date;

  @Column({ nullable: true })
  timeSlot: string;

  @Column({ nullable: true })
  userId: string;

  @Column({ nullable: true })
  userName: string;

  @Column({ nullable: true })
  uniqueSlotId: string;

  @Column({ default: false })
  isCheckedIn: boolean;

  @Column({ nullable: true, type: 'timestamp with time zone' })
  graceStartedAt: Date | null;

  @Column({ default: false })
  confirmedPresence: boolean;

  @Column({ default: false })
  inGracePeriod: boolean;

  @Column({ default: false })
  currentlyServing: boolean;

  @Column({ nullable: true, type: 'timestamp with time zone' })
  servingStartedAt: Date | null;

  @Column({ nullable: true, type: 'timestamp with time zone' })
  statusUpdatedAt: Date | null;

  @Column({ nullable: true, default: 0 })
  position: number;

  @Column({ nullable: true, default: 0 })
  initialPositionAtJoin: number;

  @Column({ nullable: true, type: 'timestamp with time zone' })
  estimatedServeTime: Date | null;

  @Column({ nullable: true, type: 'varchar', default: 'on-time' })
  waitTimeStatus: string;

  @Column({ default: false, nullable: true })
  hasSubUnits: boolean;

  @Column({ nullable: true, type: 'varchar' })
  subUnitId: string;

  @Column({ nullable: true, type: 'varchar' })
  subUnitName: string;

  @Column({ nullable: true, default: 15 })
  serveTime: number;

  @CreateDateColumn()
  createdAt: Date;
}
