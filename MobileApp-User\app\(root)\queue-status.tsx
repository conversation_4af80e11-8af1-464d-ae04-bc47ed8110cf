import React, {
  useState,
  useEffect,
  useCallback,
  useRef,
  useMemo,
} from "react";

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;

  return function (...args: Parameters<T>) {
    if (timeout) {
      clearTimeout(timeout);
    }

    timeout = setTimeout(() => {
      func(...args);
      timeout = null;
    }, wait);
  };
}
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StatusBar,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
  Alert,
  RefreshControl,
  Linking,
  Platform,
  StyleSheet,
  Animated,
} from "react-native";
import { useLocalSearchParams, useRouter, Stack } from "expo-router";
import { images } from "@/constants";
import LottieView from "lottie-react-native";
import { useUser } from "@clerk/clerk-expo";
import { CountdownCircleTimer } from "react-native-countdown-circle-timer";

/**
 * Queue Status Screen
 *
 * This screen displays detailed information about a user's queue position
 * for a specific service. The queue position calculation now uses Redis data
 * fetched from the backend to ensure accurate position tracking.
 *
 * The position is calculated by:
 * 1. Fetching all active queues for the service on the given date
 * 2. Filtering to only queues for the specific time slot
 * 3. Sorting VIP queues first, then normal queues, both by creation time
 * 4. Finding the user's position in this ordered list (1-indexed)
 *
 * This ensures that:
 * - VIP queues are always prioritized above normal queues
 * - Within each queue type, positions are assigned by first-come-first-served
 * - The position shown is the user's actual position, not just a count of people
 */

// Interface for queue status
interface QueueStatus {
  id: number;
  serviceId: number;
  serviceName: string;
  serviceType: string;
  date: string;
  timeSlot: string;
  status: string; // waiting, completed, cancelled, checked-in
  isVIP: boolean;
  createdAt: string;
  uniqueSlotId: string;
  position: number;
  initialPositionAtJoin?: number;
  isCheckedIn: boolean;
  hasSubUnits?: boolean;
  subUnitId?: string;
  subUnitName?: string;
  address?: {
    details: {
      buildingNo: string;
      locality: string;
      city: string;
    };
    coordinates: {
      latitude: number;
      longitude: number;
    };
    googleMapsLink?: string;
  };
  contactPhone?: string;
  images?: string[];
  queueCount?: {
    normal: number;
    vip: number;
    total: number;
  };
  servingTime?: number; // Time in minutes to serve each customer
  serveTime?: number; // Time in minutes to serve this specific customer
}

// Interface for service details
interface ServiceDetails {
  _id: string;
  serviceName: string;
  serviceType: string;
  businessPhone: string;
  images: string[];
  address: {
    details: {
      buildingNo: string;
      locality: string;
      city: string;
      state: string;
      pincode: string;
    };
    coordinates: {
      latitude: number;
      longitude: number;
    };
    googleMapsLink?: string;
  };
  queueInfo: {
    waitingTime: number;
    servingTime: number;
    vipCount: number;
    normalCount: number;
  };
}

// Interface for grace period status
interface GracePeriodStatus {
  queueId: number;
  inGracePeriod: boolean;
  confirmedPresence: boolean;
  graceStartedAt: string | null;
  graceEndTime?: string;
  graceTimeSeconds: number;
  remainingSeconds: number;
  isExpired: boolean;
  status: string;
}

// Interface for serving status
interface ServingStatus {
  queueId: number;
  serviceId: number;
  isServing: boolean;
  servingStartedAt: string | null;
  estimatedEndTime: string | null;
  servingTimeMinutes: number;
  remainingMinutes: number;
  remainingSeconds: number;
  serveTime?: number; // Time in minutes to serve this specific customer
}

// Interface for estimated wait time
interface EstimatedWaitTime {
  queueId: number;
  waitTimeMinutes: number;
  waitTimeStatus: string; // 'early', 'on-time', 'delayed'
  estimatedServeTime: string;
  position: number;
  initialPositionAtJoin: number;
  remainingSeconds?: number; // Added for precise timer display
  displayWaitTimeMinutes?: number; // Added for display purposes
  originalWaitTimeMinutes?: number; // Added to store the original wait time from the backend
}

export default function QueueStatusScreen() {
  const router = useRouter();
  const { queueId, serviceId } = useLocalSearchParams();
  const { user } = useUser();
  const isMounted = useRef(true);

  // States
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isRefreshingData, setIsRefreshingData] = useState(false); // New state for small loading indicator
  const [queueData, setQueueData] = useState<QueueStatus | null>(null);
  const [serviceData, setServiceData] = useState<ServiceDetails | null>(null);
  const [queueStatus, setQueueStatus] = useState<
    "not-started" | "live" | "ended"
  >("not-started");
  const [checkingIn, setCheckingIn] = useState(false);
  const [cancelling, setCancelling] = useState(false);
  const [rescheduling, setRescheduling] = useState(false);
  const [inGracePeriod, setInGracePeriod] = useState(false);
  const [gracePeriodStatus, setGracePeriodStatus] =
    useState<GracePeriodStatus | null>(null);
  const [gracePeriodTimer, setGracePeriodTimer] =
    useState<NodeJS.Timeout | null>(null);
  const [remainingGraceTime, setRemainingGraceTime] = useState(120); // Default to 2 minutes
  const [confirmingPresence, setConfirmingPresence] = useState(false);
  const [servingStatus, setServingStatus] = useState<ServingStatus | null>(
    null
  );
  const [remainingServingTime, setRemainingServingTime] = useState(0);
  const [initialServingTime, setInitialServingTime] = useState(0);
  const [servingTimer, setServingTimer] = useState<NodeJS.Timeout | null>(null);
  const [estimatedWaitTime, setEstimatedWaitTime] = useState<
    (EstimatedWaitTime & { receivedAt?: number }) | null
  >(null);
  const [waitTimeCountdownInterval, setWaitTimeCountdownInterval] =
    useState<NodeJS.Timeout | null>(null);
  const [firstPersonStatus, setFirstPersonStatus] = useState<{
    status: "waiting" | "serving" | "overtime" | "completed" | "unknown";
    message: string;
  }>({ status: "unknown", message: "Checking queue status..." });

  // API base URL
  const API_BASE_URL = "http://**************:3000/api";

  // Fetch queue data
  const fetchQueueData = useCallback(async () => {
    if (!queueId || !user?.id) return;

    // Add a small delay to prevent multiple rapid fetches
    await new Promise((resolve) => setTimeout(resolve, 100));

    try {
      // Only show full loading screen on initial load, otherwise show small indicator
      if (isLoading) {
        // Keep the full loading screen
      } else {
        // Show a small loading indicator instead of blanking out the screen
        setIsRefreshingData(true);
      }

      // This endpoint already uses Redis data for faster access, as implemented in the backend
      const response = await fetch(`${API_BASE_URL}/customer/queue/${queueId}`);

      if (!response.ok) {
        throw new Error("Failed to fetch queue data");
      }

      const data = await response.json();

      if (data.status === "success" && data.queue) {
        console.log("Queue data from Redis/backend:", data.queue);

        // If position is not in the response, set it to 0 initially
        // Also handle isCheckedIn if it's not provided by the backend
        const queueWithDefaults = {
          ...data.queue,
          position: data.queue.position || 0,
          uniqueSlotId:
            data.queue.uniqueSlotId ||
            `${Math.floor(1000 + Math.random() * 9000)}`,
          isCheckedIn:
            data.queue.isCheckedIn !== undefined
              ? data.queue.isCheckedIn
              : data.queue.status === "checked-in",
        };

        setQueueData(queueWithDefaults);

        // Parse time slots to get start time and end time, handling AM/PM format
        const timeSlotParts = data.queue.timeSlot.split(" - ");

        // Parse start time
        const timeStart = timeSlotParts[0].trim(); // e.g., "06:00 PM"
        let startHour = 0;
        let startMinute = 0;

        // Parse end time
        const timeEnd = timeSlotParts.length > 1 ? timeSlotParts[1].trim() : ""; // e.g., "07:00 PM"
        let endHour = 23;
        let endMinute = 59;

        // Check if we need to handle AM/PM format for start time
        if (timeStart.includes("AM") || timeStart.includes("PM")) {
          const [timeValue, period] = timeStart.split(" ");
          const [hourStr, minuteStr] = timeValue.split(":");
          let hour = parseInt(hourStr, 10);
          const minute = parseInt(minuteStr, 10);

          // Convert to 24-hour format
          if (period.toUpperCase() === "PM" && hour < 12) {
            hour += 12;
          } else if (period.toUpperCase() === "AM" && hour === 12) {
            hour = 0;
          }

          startHour = hour;
          startMinute = minute;
          console.log(`Queue start time parsed as: ${hour}:${minute}`);
        } else {
          // Fallback to original parsing if no AM/PM
          const [hours, minutes] = timeStart
            .split(":")
            .map((part: string) => parseInt(part));
          startHour = hours;
          startMinute = minutes;
        }

        // Parse end time if available
        if (timeEnd && (timeEnd.includes("AM") || timeEnd.includes("PM"))) {
          const [timeValue, period] = timeEnd.split(" ");
          const [hourStr, minuteStr] = timeValue.split(":");
          let hour = parseInt(hourStr, 10);
          const minute = parseInt(minuteStr, 10);

          // Convert to 24-hour format
          if (period.toUpperCase() === "PM" && hour < 12) {
            hour += 12;
          } else if (period.toUpperCase() === "AM" && hour === 12) {
            hour = 0;
          }

          endHour = hour;
          endMinute = minute;
          console.log(`Queue end time parsed as: ${hour}:${minute}`);
        } else if (timeEnd) {
          // Fallback to original parsing if no AM/PM
          const [hours, minutes] = timeEnd
            .split(":")
            .map((part: string) => parseInt(part));
          endHour = hours;
          endMinute = minutes;
        }

        // Set the correct time on the queue date
        const queueStartDate = new Date(data.queue.date);
        queueStartDate.setHours(startHour, startMinute, 0, 0);

        const queueEndDate = new Date(data.queue.date);
        queueEndDate.setHours(endHour, endMinute, 0, 0);

        console.log(
          `Queue time: ${queueStartDate.toLocaleTimeString()} - ${queueEndDate.toLocaleTimeString()}`
        );

        // Get current time
        const now = new Date();
        console.log(`Current time: ${now.toLocaleString()}`);
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();

        // Convert times to minutes for easier comparison
        const startTimeMinutes = startHour * 60 + startMinute;
        const endTimeMinutes = endHour * 60 + endMinute;
        const currentTimeMinutes = currentHour * 60 + currentMinute;

        console.log(
          `Time in minutes - Current: ${currentTimeMinutes}, Start: ${startTimeMinutes}, End: ${endTimeMinutes}`
        );

        let newStatus = data.queue.status;

        // Status logic: If database says canceled, keep it canceled
        if (data.queue.status === "cancelled") {
          setQueueStatus("ended");
          // Redirect to queue-completed page
          router.replace({
            pathname: "/(root)/queue-completed",
            params: {
              queueId: queueId.toString(),
              status: "cancelled",
            },
          });
          return; // Exit early
        }
        // If database status is completed, keep it completed
        else if (data.queue.status === "completed") {
          setQueueStatus("ended");
          // Redirect to queue-completed page
          router.replace({
            pathname: "/(root)/queue-completed",
            params: {
              queueId: queueId.toString(),
              status: "completed",
            },
          });
          return; // Exit early
        }
        // If database status is no-show, keep it no-show
        else if (data.queue.status === "no-show") {
          setQueueStatus("ended");
          // Redirect to queue-completed page
          router.replace({
            pathname: "/(root)/queue-completed",
            params: {
              queueId: queueId.toString(),
              status: "no-show",
            },
          });
          return; // Exit early
        }
        // Queue date logic
        else {
          // Properly compare dates by normalizing them to start of day
          const queueDate = new Date(data.queue.date);
          const today = new Date();

          // Set both dates to start of day for accurate comparison
          const queueDateNormalized = new Date(
            queueDate.getFullYear(),
            queueDate.getMonth(),
            queueDate.getDate()
          );
          const todayNormalized = new Date(
            today.getFullYear(),
            today.getMonth(),
            today.getDate()
          );

          // Calculate difference in days
          const diffTime =
            queueDateNormalized.getTime() - todayNormalized.getTime();
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          console.log(
            `Queue date: ${queueDateNormalized.toDateString()}, Today: ${todayNormalized.toDateString()}, Diff days: ${diffDays}`
          );

          // If queue date is in the future
          if (diffDays > 0) {
            console.log("Queue is in the future - not started");
            setQueueStatus("not-started");
          }
          // If queue date is today
          else if (diffDays === 0) {
            console.log(
              `Current time: ${currentTimeMinutes}, Start: ${startTimeMinutes}, End: ${endTimeMinutes}`
            );

            // Queue has ended
            if (currentTimeMinutes > endTimeMinutes) {
              console.log("Queue has ended today");
              setQueueStatus("ended");

              // Automatically mark as completed or no-show based on check-in status
              if (
                data.queue.status !== "cancelled" &&
                data.queue.status !== "completed" &&
                data.queue.status !== "no-show"
              ) {
                // If checked in, mark as completed; otherwise, mark as no-show
                const newStatusValue = data.queue.isCheckedIn
                  ? "completed"
                  : "no-show";
                console.log(
                  `Queue ended with isCheckedIn=${data.queue.isCheckedIn}, marking as ${newStatusValue}`
                );
                newStatus = newStatusValue;

                // This will trigger the redirection to queue-completed through updateQueueStatus
                updateQueueStatus(queueId.toString(), newStatusValue);
                return; // Exit early to prevent further processing
              }
            }
            // Queue is live/ongoing
            else if (
              currentTimeMinutes >= startTimeMinutes &&
              currentTimeMinutes <= endTimeMinutes
            ) {
              console.log("Queue is live");
              setQueueStatus("live");
            }
            // Queue hasn't started yet today
            else {
              console.log("Queue is today but hasn't started yet");
              setQueueStatus("not-started");
            }
          }
          // Queue date is in the past
          else {
            console.log("Queue date is in the past - ended");
            setQueueStatus("ended");

            // Automatically mark as completed or no-show based on check-in status if it's past the date
            if (
              data.queue.status !== "cancelled" &&
              data.queue.status !== "completed" &&
              data.queue.status !== "no-show"
            ) {
              // If checked in, mark as completed; otherwise, mark as no-show
              const newStatusValue = data.queue.isCheckedIn
                ? "completed"
                : "no-show";
              console.log(
                `Past queue date with isCheckedIn=${data.queue.isCheckedIn}, marking as ${newStatusValue}`
              );
              newStatus = newStatusValue;

              // This will trigger the redirection to queue-completed through updateQueueStatus
              updateQueueStatus(queueId.toString(), newStatusValue);
              return; // Exit early to prevent further processing
            }
          }
        }

        // Update local state if status changed
        if (newStatus !== data.queue.status) {
          setQueueData((prev) =>
            prev ? { ...prev, status: newStatus } : null
          );
        }

        // Fetch service details to get additional info
        fetchServiceDetails(data.queue.serviceId);

        // Always fetch queue counts
        fetchQueueCounts(data.queue);

        // If position is already provided in the queue data, use it directly
        if (data.queue.position !== undefined) {
          console.log(
            `Using position directly from queue data: ${data.queue.position}`
          );
          // No need to fetch position separately
        }
        // Otherwise, fetch position separately using the Redis-backed API
        else if (newStatus === "waiting" || newStatus === "checked-in") {
          fetchQueuePosition(data.queue);
        }

        // Check if this queue is in grace period
        await checkGracePeriodStatus();
        await checkServingStatus();

        // Fetch estimated wait time
        await fetchEstimatedWaitTime();
      }
    } catch (error) {
      console.error("Error fetching queue data:", error);
      Alert.alert("Error", "Failed to load queue information");
    } finally {
      setIsLoading(false);
      setRefreshing(false);
      setIsRefreshingData(false); // Reset the small loading indicator
    }
  }, [queueId, user?.id]);

  // Update queue status on the server
  const updateQueueStatus = async (queueId: string, status: string) => {
    try {
      // Valid statuses are 'completed', 'cancelled', and 'no-show'
      if (
        status !== "completed" &&
        status !== "cancelled" &&
        status !== "no-show"
      ) {
        console.error(
          `Invalid status: ${status}, only 'completed', 'cancelled', and 'no-show' are valid.`
        );
        return;
      }

      // First trigger the backend to update any expired queues
      try {
        console.log(
          "Triggering backend to update expired queues before updating status"
        );
        const updateResponse = await fetch(
          `${API_BASE_URL}/customer/update-expired-queues`
        );
        if (updateResponse.ok) {
          const result = await updateResponse.json();
          console.log(`Queue status update result: ${result.message}`);
        }
      } catch (error) {
        console.error("Error triggering queue status update:", error);
        // Continue with the status update even if this fails
      }

      let endpoint = "";

      // Use the partner endpoints which are more efficient
      if (status === "completed") {
        endpoint = `${API_BASE_URL}/partner/queues/${queueId}/complete`;
      } else if (status === "no-show") {
        endpoint = `${API_BASE_URL}/partner/queues/${queueId}/no-show`;
      } else if (status === "cancelled") {
        endpoint = `${API_BASE_URL}/partner/queues/${queueId}/cancel`;
      }

      // Partner endpoints don't require a request body
      const response = await fetch(endpoint, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to update queue status to ${status}`);
      }

      console.log(`Queue ${queueId} status updated to ${status}`);

      // Redirect to queue-completed screen with the appropriate status
      setTimeout(() => {
        router.replace({
          pathname: "/(root)/queue-completed",
          params: {
            queueId: queueId,
            status: status,
          },
        });
      }, 500);
    } catch (error) {
      console.error(`Error updating queue status: ${error}`);
      // Don't show alert to user as this is a background operation
    }
  };

  // Function to fetch queue counts using the same approach as service-details.tsx
  const fetchQueueCounts = async (queueData: any) => {
    try {
      console.log(
        `Fetching queue counts for service: ${queueData.serviceId}, date: ${queueData.date}`
      );

      // Determine which subunit to use
      let subUnitId = queueData.subUnitId || "0";

      // Step 1: First check if the service has subunits
      if (!queueData.hasSubUnits) {
        console.log(
          "Service does not have subunits, using regular queue counts"
        );
      } else {
        console.log(
          `Service has subunits, using subunit: ${queueData.subUnitName} (ID: ${subUnitId})`
        );
      }

      // Step 2: Construct the API URL with subunit parameter if needed
      let apiUrl = `${API_BASE_URL}/customer/queues/redis/${queueData.serviceId}/upcoming`;
      if (queueData.hasSubUnits) {
        apiUrl += `?subUnitId=${subUnitId}`;
      }

      // Step 3: Fetch queue data from Redis
      console.log(`Fetching from API: ${apiUrl}`);
      const response = await fetch(apiUrl);

      if (!response.ok) {
        console.error(`Failed to fetch queues from Redis: ${response.status}`);
        throw new Error(`Failed to fetch queues: ${response.status}`);
      }

      const data = await response.json();

      if (
        data.status !== "success" ||
        !data.queues ||
        !Array.isArray(data.queues)
      ) {
        console.log("No valid queue data available from Redis");
        throw new Error("No valid queue data available");
      }

      console.log(
        `Successfully retrieved ${data.queues.length} queues from Redis`
      );

      // Step 4: Get today's date and find the earliest available date
      const today = new Date().toISOString().split("T")[0];

      // Group queues by date
      const dateGroups: Record<string, any[]> = {};
      data.queues.forEach((queue: any) => {
        if (!queue || !queue.date) return;

        // Extract date part in YYYY-MM-DD format
        let dateStr;
        try {
          dateStr =
            typeof queue.date === "string"
              ? queue.date.split("T")[0]
              : new Date(queue.date).toISOString().split("T")[0];
        } catch (err) {
          console.error("Error parsing queue date:", err);
          return;
        }

        if (!dateGroups[dateStr]) {
          dateGroups[dateStr] = [];
        }
        dateGroups[dateStr].push(queue);
      });

      // Find earliest future date
      const availableDates = Object.keys(dateGroups)
        .filter((date) => date >= today)
        .sort();

      if (availableDates.length === 0) {
        console.log("No future dates with queues found");
        updateQueueCounts(0, 0, 0);
        return;
      }

      const earliestDate = availableDates[0];
      console.log(`Using queues for date: ${earliestDate}`);

      // Step 5: Group queues by time slot for the earliest date
      const relevantQueues = dateGroups[earliestDate];
      const timeSlotGroups: Record<string, any[]> = {};

      relevantQueues.forEach((queue: any) => {
        if (queue && queue.timeSlot) {
          if (!timeSlotGroups[queue.timeSlot]) {
            timeSlotGroups[queue.timeSlot] = [];
          }
          timeSlotGroups[queue.timeSlot].push(queue);
        }
      });

      // Step 6: Find the current or next time slot
      const timeSlots = Object.keys(timeSlotGroups);
      if (timeSlots.length === 0) {
        console.log("No time slots found for the date");
        updateQueueCounts(0, 0, 0);
        return;
      }

      // Find the time slot that matches the user's queue
      const userTimeSlot = queueData.timeSlot;
      if (!userTimeSlot || !timeSlotGroups[userTimeSlot]) {
        console.log(
          `User's time slot ${userTimeSlot} not found in available time slots`
        );
        updateQueueCounts(0, 0, 0);
        return;
      }

      const selectedTimeSlotQueues = timeSlotGroups[userTimeSlot] || [];

      // Step 7: Count normal and VIP queues for the selected time slot
      const normalCount = selectedTimeSlotQueues.filter(
        (q: any) =>
          q &&
          q.isVIP !== true &&
          q.status !== "cancelled" &&
          q.status !== "completed"
      ).length;

      const vipCount = selectedTimeSlotQueues.filter(
        (q: any) =>
          q &&
          q.isVIP === true &&
          q.status !== "cancelled" &&
          q.status !== "completed"
      ).length;

      console.log(
        `Time slot ${userTimeSlot}: Normal: ${normalCount}, VIP: ${vipCount}, Total: ${normalCount + vipCount}`
      );

      // Step 8: Update the queue data with the queue counts
      updateQueueCounts(normalCount, vipCount, normalCount + vipCount);
    } catch (error) {
      console.error("Error fetching queue counts:", error);
      // Set default values in case of error
      updateQueueCounts(0, 0, 0);
    }
  };

  // Helper function to update queue data with queue counts
  const updateQueueCounts = (
    normalCount: number,
    vipCount: number,
    totalCount: number
  ) => {
    setQueueData((prev) => {
      if (!prev) return prev;

      return {
        ...prev,
        queueCount: {
          normal: normalCount,
          vip: vipCount,
          total: totalCount,
        },
      };
    });

    console.log(
      `Updated queue counts: Normal=${normalCount}, VIP=${vipCount}, Total=${totalCount}`
    );
  };

  // Function to calculate position in queue using Redis position data
  const fetchQueuePosition = async (queueData: any) => {
    try {
      const start = performance.now();
      console.log("Fetching queue position using Redis position data");

      if (!queueData || !queueData.id) {
        console.error("Missing required queue ID for position calculation");
        return;
      }

      // First try to get position directly from the queue position endpoint
      try {
        const positionResponse = await fetch(
          `${API_BASE_URL}/customer/queue/${queueData.id}/position`
        );

        if (positionResponse.ok) {
          const positionData = await positionResponse.json();

          if (
            positionData.status === "success" &&
            positionData.position !== undefined
          ) {
            console.log(`Got position from Redis: ${positionData.position}`);

            // Now fetch queue counts using the same approach as service-details.tsx
            await fetchQueueCounts(queueData);

            // Update state with position from Redis
            setQueueData((prev) => {
              if (!prev) return prev;
              return {
                ...prev,
                position: positionData.position,
              };
            });

            console.log(
              `Position from Redis: Position=${positionData.position}, Time=${Math.round(performance.now() - start)}ms`
            );

            return; // Exit early if we got the position from Redis
          }
        }
      } catch (error) {
        console.error("Error fetching position from Redis:", error);
        // Continue to fallback method if Redis fetch fails
      }

      // Fallback to the old method if Redis position is not available
      console.log(
        "Falling back to active queues approach for position calculation"
      );

      if (!queueData.serviceId || !queueData.date || !queueData.timeSlot) {
        console.error(
          "Missing required queue data for fallback position calculation"
        );
        return;
      }

      const serviceId = queueData.serviceId;
      const date =
        typeof queueData.date === "string"
          ? queueData.date.split("T")[0]
          : new Date(queueData.date).toISOString().split("T")[0];
      const timeSlot = queueData.timeSlot;

      // Use the active queues endpoint to determine position
      const activeQueuesResponse = await fetch(
        `${API_BASE_URL}/customer/queues/active/${serviceId}/${date}`
      );

      if (!activeQueuesResponse.ok) {
        console.error(
          `Failed to fetch active queues: ${activeQueuesResponse.status}`
        );
        return;
      }

      const activeQueuesData = await activeQueuesResponse.json();

      if (activeQueuesData.status === "success" && activeQueuesData.queues) {
        // Filter to get queues for this time slot only
        const timeSlotQueues = activeQueuesData.queues.filter(
          (q: any) => q && q.timeSlot === timeSlot
        );

        // Sort queues by position first
        const sortedQueues = [...timeSlotQueues].sort((a, b) => {
          // First sort by position
          const positionDiff = (a.position || 0) - (b.position || 0);

          // If positions are the same, then consider VIP status
          if (positionDiff === 0) {
            if (a.isVIP && !b.isVIP) return -1;
            if (!a.isVIP && b.isVIP) return 1;

            // If VIP status is the same, sort by creation time
            try {
              const aTime = a.createdAt ? new Date(a.createdAt).getTime() : 0;
              const bTime = b.createdAt ? new Date(b.createdAt).getTime() : 0;
              return aTime - bTime;
            } catch (error) {
              return 0;
            }
          }

          return positionDiff;
        });

        // Find the position of the user's queue
        const userQueueId = queueData.id || queueData._id;
        const position = sortedQueues.findIndex((q: any) => {
          if (!q) return false;

          const queueId = q._id || q.id;

          if (!queueId || !userQueueId) return false;

          try {
            return queueId.toString() === userQueueId.toString();
          } catch (error) {
            console.error("Error comparing queue IDs:", error);
            return false;
          }
        });

        // Position is 1-indexed
        const queuePosition = position !== -1 ? position + 1 : 0;

        // Count all queues by type
        const vipCount = timeSlotQueues.filter(
          (q: any) => q && q.isVIP === true
        ).length;
        const normalCount = timeSlotQueues.filter(
          (q: any) => q && q.isVIP !== true
        ).length;
        const totalCount = vipCount + normalCount;

        // Update state
        setQueueData((prev) => {
          if (!prev) return prev;
          return {
            ...prev,
            position: queuePosition,
            queueCount: {
              normal: normalCount,
              vip: vipCount,
              total: totalCount,
            },
          };
        });

        console.log(
          `Fallback position calculation: Position=${queuePosition}, VIP=${vipCount}, Normal=${normalCount}, Total=${totalCount}, Time=${Math.round(performance.now() - start)}ms`
        );
      }
    } catch (error) {
      console.error("Error fetching queue position:", error);
    }
  };

  // Fetch service details
  const fetchServiceDetails = async (id: number) => {
    try {
      const response = await fetch(`${API_BASE_URL}/partner/services/${id}`);

      if (response.ok) {
        const serviceData = await response.json();
        console.log("Service details fetched:", serviceData);

        // Process images
        let processedImages = [];
        if (serviceData.images) {
          // Filter out any invalid URLs
          processedImages = Array.isArray(serviceData.images)
            ? serviceData.images.filter(
                (url: string) => typeof url === "string" && url.trim() !== ""
              )
            : [];

          serviceData.images = processedImages;
        }

        // Update queue data with service details including phone number and address
        setQueueData((prevData) => {
          if (!prevData) return prevData;

          return {
            ...prevData,
            images: processedImages,
            contactPhone: serviceData.businessPhone || "",
            address: serviceData.address || prevData.address,
          };
        });

        console.log(
          "Updated queue data with service details including phone and address"
        );
        setServiceData(serviceData);
      }
    } catch (error) {
      console.error("Error fetching service details:", error);
    }
  };

  // Add initial data loading back
  useEffect(() => {
    fetchQueueData();

    // Set up unmount cleanup
    return () => {
      isMounted.current = false;
    };
  }, [fetchQueueData]);

  // Add the cleanup of specialized timers
  useEffect(() => {
    // Clean up timers when component unmounts
    return () => {
      if (gracePeriodTimer) {
        clearInterval(gracePeriodTimer);
      }
      if (servingTimer) {
        clearInterval(servingTimer);
      }
      // Reset mounted flag
      isMounted.current = false;
    };
  }, [gracePeriodTimer, servingTimer]);

  // Add polling effect for status updates and auto-refresh
  useEffect(() => {
    if (queueId) {
      console.log("Starting initial polling checks...");
      // Check status immediately on mount
      checkGracePeriodStatus();
      checkServingStatus();

      // Set up polling interval for status checks (every 15 seconds)
      const statusCheckInterval = setInterval(() => {
        if (!isLoading && !refreshing) {
          console.log("Polling for status updates...");
          checkGracePeriodStatus();
          checkServingStatus();
        }
      }, 15000); // Check every 15 seconds

      // Set up auto-refresh timer for queue data (every 60 seconds)
      const dataRefreshInterval = setInterval(() => {
        if (
          !isLoading &&
          !refreshing &&
          !inGracePeriod &&
          !servingStatus?.isServing
        ) {
          console.log("Auto-refreshing queue data...");
          fetchQueueData();
        }
      }, 60000); // Refresh every minute

      // Clean up all intervals on unmount
      return () => {
        clearInterval(statusCheckInterval);
        clearInterval(dataRefreshInterval);
      };
    }
  }, [
    queueId,
    inGracePeriod,
    servingStatus?.isServing,
    isLoading,
    refreshing,
    fetchQueueData,
  ]);

  // Check if this queue is in grace period
  const checkGracePeriodStatus = async () => {
    if (!queueId) return;

    try {
      // Call the API to check grace period status
      const response = await fetch(
        `${API_BASE_URL}/customer/queue/${queueId}/grace-period-status`
      );

      if (!response.ok) {
        console.error(
          `Failed to check grace period status: ${response.status}`
        );
        return;
      }

      const result = await response.json();
      const status = result.data;

      console.log("Grace period status:", status);

      // If the queue is in grace period, update state and start polling
      if (
        status.inGracePeriod &&
        !status.confirmedPresence &&
        !status.isExpired
      ) {
        setInGracePeriod(true);
        setGracePeriodStatus(status);
        setRemainingGraceTime(status.remainingSeconds);

        // Start polling for updates if not already polling
        if (!gracePeriodTimer) {
          startGracePeriodPolling();
        }
      } else if (status.confirmedPresence) {
        // If presence already confirmed, just show the waiting for service screen
        setInGracePeriod(false);

        // If timer was running, stop it
        if (gracePeriodTimer) {
          clearInterval(gracePeriodTimer);
          setGracePeriodTimer(null);
        }
      } else {
        // Not in grace period
        setInGracePeriod(false);
      }
    } catch (error) {
      console.error("Error checking grace period status:", error);
    }
  };

  /**
   * Poll for grace period status updates - modified to match serving timer approach
   */
  const startGracePeriodPolling = () => {
    // Clear any existing timer
    if (gracePeriodTimer) {
      clearInterval(gracePeriodTimer);
      setGracePeriodTimer(null);
    }

    // Create interval that syncs with backend every 5 seconds
    const serverSyncTimer = setInterval(async () => {
      if (!queueId || !isMounted.current) {
        // If component is unmounted, clean up timer
        clearInterval(serverSyncTimer);
        return;
      }

      try {
        // Call the API to check grace period status
        const response = await fetch(
          `${API_BASE_URL}/customer/queue/${queueId}/grace-period-status`
        );

        if (!response.ok) {
          console.error(
            `Failed to poll grace period status: ${response.status}`
          );
          return;
        }

        const result = await response.json();
        const status = result.data;

        // Only update state if component is still mounted
        if (isMounted.current) {
          // Update status object
          setGracePeriodStatus(status);

          // Only update initial time if it differs significantly (>3 seconds)
          // to avoid jumpy UI
          if (Math.abs(status.remainingSeconds - remainingGraceTime) > 3) {
            console.log(
              `Syncing grace period timer: server=${status.remainingSeconds}, local=${remainingGraceTime}`
            );
            setRemainingGraceTime(status.remainingSeconds);
          }

          // If no longer in grace period or response already given, stop polling
          if (
            !status.inGracePeriod ||
            status.confirmedPresence ||
            status.isExpired
          ) {
            clearInterval(serverSyncTimer);
            setGracePeriodTimer(null);
            setInGracePeriod(false);

            // If grace period expired without confirmation, queue might be marked as no-show
            if (status.isExpired && !status.confirmedPresence) {
              console.log(
                "Grace period expired without confirmation - refreshing queue data"
              );
              debouncedFetchQueueData();
            }
          }
        }
      } catch (error) {
        console.error("Error polling grace period status:", error);
      }
    }, 5000); // Sync with backend every 5 seconds

    // Store timer for cleanup
    setGracePeriodTimer(serverSyncTimer);
  };

  /**
   * Calculate progress percentage for grace period timer
   * Returns a value between 0 (time's up) and 1 (full time)
   */
  const calculateGraceProgress = (): number => {
    if (!gracePeriodStatus) return 1;

    const totalTime = gracePeriodStatus.graceTimeSeconds;
    if (totalTime <= 0) return 0;

    const progress = remainingGraceTime / totalTime;
    return Math.max(0, Math.min(1, progress)); // Clamp between 0 and 1
  };

  /**
   * Move the queue to the end of the line
   */
  const moveToEndOfLine = async () => {
    if (!queueId || !user?.id) return;

    try {
      setConfirmingPresence(true);

      // Call the API to move to end of line
      const response = await fetch(
        `${API_BASE_URL}/partner/queues/${queueId}/move-to-end`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to move to end of line: ${response.status}`);
      }

      const result = await response.json();
      console.log("Move to end of line result:", result);

      // Update UI based on confirmation
      setInGracePeriod(false);

      // Stop polling
      if (gracePeriodTimer) {
        clearInterval(gracePeriodTimer);
        setGracePeriodTimer(null);
      }

      // Show success message
      Alert.alert(
        "Success",
        "You have been moved to the end of the line. Please wait for your turn."
      );

      // Refresh queue data to show updated position
      debouncedFetchQueueData();
    } catch (error) {
      console.error("Error moving to end of line:", error);
      Alert.alert(
        "Error",
        "Failed to move you to the end of the line. Please try again."
      );
    } finally {
      setConfirmingPresence(false);
    }
  };

  /**
   * Confirm presence in response to grace period notification
   */
  const confirmPresence = async (isPresent: boolean) => {
    if (!queueId || !user?.id) return;

    try {
      setConfirmingPresence(true);

      // Call the partner API to confirm presence for better performance
      const response = await fetch(
        `${API_BASE_URL}/partner/queues/${queueId}/confirm-presence`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            isPresent,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to confirm presence: ${response.status}`);
      }

      const result = await response.json();
      console.log("Presence confirmation result:", result);

      // Update UI based on confirmation
      setInGracePeriod(false);

      // Stop polling
      if (gracePeriodTimer) {
        clearInterval(gracePeriodTimer);
        setGracePeriodTimer(null);
      }

      // If not present, redirect to queue-completed with no-show status
      if (!isPresent) {
        router.replace({
          pathname: "/(root)/queue-completed",
          params: {
            queueId: queueId.toString(),
            status: "no-show",
          },
        });
      } else {
        // Otherwise refresh queue data to show waiting for service
        debouncedFetchQueueData();
      }
    } catch (error) {
      console.error("Error confirming presence:", error);
      Alert.alert(
        "Error",
        "Failed to confirm your presence. Please try again."
      );
    } finally {
      setConfirmingPresence(false);
    }
  };

  /**
   * Format the remaining time in grace period as MM:SS
   */
  const formatGraceTimeRemaining = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  // Handle pull-to-refresh
  const onRefresh = async () => {
    try {
      // If currently being served with an active timer, don't reset timer state
      if (
        (queueData?.status === "serving" || servingStatus?.isServing) &&
        initialServingTime > 0
      ) {
        setRefreshing(true);
        // Only refresh parts of the data that aren't related to the timer
        await fetchQueueDetails();

        // Also refresh queue counts
        if (queueData) {
          await fetchQueueCounts(queueData);
        }

        return;
      }

      // Regular refresh for non-serving states
      setRefreshing(true);
      debouncedFetchQueueData();

      // Also refresh queue counts and estimated wait time
      if (queueData) {
        await fetchQueueCounts(queueData);
        debouncedFetchEstimatedWaitTime();
      }
    } catch (error) {
      console.error("Error refreshing data:", error);
    } finally {
      setRefreshing(false);
    }
  };

  // Fetch basic queue details without affecting timer state
  const fetchQueueDetails = async () => {
    if (!queueId || !user?.id) return;

    try {
      const response = await fetch(`${API_BASE_URL}/customer/queue/${queueId}`);

      if (!response.ok) {
        throw new Error("Failed to fetch queue data");
      }

      const data = await response.json();

      if (data.status === "success" && data.queue) {
        // Check if position has changed (queue progressed)
        const oldPosition = queueData?.position;
        const newPosition =
          data.queue.position !== undefined
            ? data.queue.position
            : queueData?.position || 0;
        const hasPositionChanged =
          oldPosition !== undefined && newPosition !== oldPosition;

        if (hasPositionChanged) {
          console.log(
            `🚀 POSITION CHANGED: ${oldPosition} → ${newPosition} (queue progressed!)`
          );

          // Clear the current wait time timer since position changed
          if (waitTimeCountdownInterval) {
            console.log(`🔄 Clearing old timer due to position change`);
            clearInterval(waitTimeCountdownInterval);
            setWaitTimeCountdownInterval(null);
          }

          // Reset estimated wait time to trigger a fresh calculation
          setEstimatedWaitTime(null);
        }

        // Update queue data but preserve serving status
        setQueueData({
          ...data.queue,
          // Use the position from the response if available, otherwise keep the current position
          position: newPosition,
          uniqueSlotId:
            data.queue.uniqueSlotId ||
            `${Math.floor(1000 + Math.random() * 9000)}`,
          isCheckedIn:
            data.queue.isCheckedIn !== undefined
              ? data.queue.isCheckedIn
              : data.queue.status === "checked-in",
          // Preserve queue count if not in the response
          queueCount: data.queue.queueCount ||
            queueData?.queueCount || {
              normal: 0,
              vip: 0,
              total: 0,
            },
        });

        console.log(
          `Updated queue data with position: ${newPosition} ${hasPositionChanged ? "(CHANGED!)" : ""}`
        );

        // Fetch service details for display
        fetchServiceDetails(data.queue.serviceId);

        // Fetch queue counts and estimated wait time
        await fetchQueueCounts(data.queue);
        await fetchEstimatedWaitTime();
      }
    } catch (error) {
      console.error("Error fetching queue details:", error);
    } finally {
      setRefreshing(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric",
      year: "numeric",
    });
  };

  // Calculate estimated wait time
  const calculateWaitTime = () => {
    if (!queueData || !serviceData?.queueInfo.servingTime) {
      return (
        <Text className="font-poppins-medium text-primary-500 text-base">
          Unknown
        </Text>
      );
    }

    // For upcoming queues, calculate time until queue starts
    if (queueStatus === "not-started") {
      // Calculate time until queue starts
      const queueDate = new Date(queueData.date);
      const now = new Date();

      // Parse time slots to get start time - handle 12-hour format with AM/PM
      const timeStart = queueData.timeSlot.split(" - ")[0]; // e.g. "06:00 PM"

      // Check if the time format includes AM/PM
      if (timeStart.includes("AM") || timeStart.includes("PM")) {
        const timeParts = timeStart.trim().split(" ");
        if (timeParts.length === 2) {
          const [timeValue, period] = timeParts;
          const [hourStr, minuteStr] = timeValue.split(":");
          let hour = parseInt(hourStr, 10);
          const minute = parseInt(minuteStr, 10);

          // Convert to 24-hour format
          if (period.toUpperCase() === "PM" && hour < 12) {
            hour += 12;
          } else if (period.toUpperCase() === "AM" && hour === 12) {
            hour = 0;
          }

          console.log(
            `Parsed time: ${hour}:${minute} ${period} (24h: ${hour})`
          );
          queueDate.setHours(hour, minute, 0, 0);
        }
      } else {
        // Fallback to original parsing if no AM/PM
        const [hours, minutes] = timeStart
          .split(":")
          .map((part) => parseInt(part, 10));
        queueDate.setHours(hours, minutes, 0, 0);
      }

      const diffMs = queueDate.getTime() - now.getTime();
      const diffMins = Math.round(diffMs / 60000);

      console.log(
        `Queue starts at: ${queueDate.toLocaleTimeString()}, Current time: ${now.toLocaleTimeString()}`
      );
      console.log(`Diff in minutes: ${diffMins}`);

      // Format the wait time text
      let waitTimeText;
      if (diffMins <= 0) {
        waitTimeText = "Starting in 1 min"; // Always show at least 1 minute instead of "Starting soon"
      } else if (diffMins < 60) {
        waitTimeText = `Starting in ${diffMins} minutes`;
      } else {
        const hours = Math.floor(diffMins / 60);
        const mins = diffMins % 60;
        waitTimeText = `Starting in ${hours} hour${hours > 1 ? "s" : ""}${mins > 0 ? ` ${mins} min` : ""}`;
      }

      // Return the formatted wait time text
      return (
        <View className="flex-row items-center">
          <Text className="font-poppins-medium text-primary-500 text-base">
            {waitTimeText}
          </Text>
          <View className="ml-2 px-2 py-1 bg-yellow-100 rounded-md">
            <Text className="text-yellow-700 text-xs font-poppins-medium">
              UPCOMING
            </Text>
          </View>
        </View>
      );
    }

    // For live queues, check if the queue has actually started or is still in the future
    const queueDate = new Date(queueData.date);
    const now = new Date();

    // Parse time slots to get start time with AM/PM handling
    const timeStart = queueData.timeSlot.split(" - ")[0];
    let queueStartHour = 0;
    let queueStartMinute = 0;

    // Parse time with AM/PM format
    if (timeStart.includes("AM") || timeStart.includes("PM")) {
      const timeParts = timeStart.trim().split(" ");
      if (timeParts.length === 2) {
        const [timeValue, period] = timeParts;
        const [hourStr, minuteStr] = timeValue.split(":");
        let hour = parseInt(hourStr, 10);
        const minute = parseInt(minuteStr, 10);

        // Convert to 24-hour format
        if (period.toUpperCase() === "PM" && hour < 12) {
          hour += 12;
        } else if (period.toUpperCase() === "AM" && hour === 12) {
          hour = 0;
        }

        queueStartHour = hour;
        queueStartMinute = minute;
      }
    } else {
      // Fallback to original parsing if no AM/PM
      const [hours, minutes] = timeStart
        .split(":")
        .map((part) => parseInt(part, 10));
      queueStartHour = hours;
      queueStartMinute = minutes;
    }

    queueDate.setHours(queueStartHour, queueStartMinute, 0, 0);

    // Check if the queue start time is still in the future
    if (queueDate > now) {
      const diffMs = queueDate.getTime() - now.getTime();
      const diffMins = Math.round(diffMs / 60000);

      console.log(
        `Queue hasn't started yet. Starts at: ${queueDate.toLocaleTimeString()}`
      );

      // Format the wait time text
      let waitTimeText;
      if (diffMins <= 0) {
        waitTimeText = "Starting in 1 min"; // Always show at least 1 minute instead of "Starting soon"
      } else if (diffMins < 60) {
        waitTimeText = `Starting in ${diffMins} minutes`;
      } else {
        const hours = Math.floor(diffMins / 60);
        const mins = diffMins % 60;
        waitTimeText = `Starting in ${hours} hour${hours > 1 ? "s" : ""}${mins > 0 ? ` ${mins} min` : ""}`;
      }

      // Return the formatted wait time text
      return (
        <View className="flex-row items-center">
          <Text className="font-poppins-medium text-primary-500 text-base">
            {waitTimeText}
          </Text>
          <View className="ml-2 px-2 py-1 bg-yellow-100 rounded-md">
            <Text className="text-yellow-700 text-xs font-poppins-medium">
              UPCOMING
            </Text>
          </View>
        </View>
      );
    }

    // Queue has already started, use dynamic wait time estimation if available
    if (estimatedWaitTime) {
      // Use the dynamic wait time from the backend
      const waitTimeMinutes = estimatedWaitTime.waitTimeMinutes;
      const waitTimeStatus = estimatedWaitTime.waitTimeStatus;

      // Log wait time calculation details for debugging
      console.log(
        `Wait time calculation (dynamic): position=${queueData.position}, initialPositionAtJoin=${estimatedWaitTime.initialPositionAtJoin || "N/A"}, waitTimeMinutes=${waitTimeMinutes}, status=${waitTimeStatus}`
      );

      // Format the wait time with status indicator and include seconds to show timer is running
      // We'll return a JSX element instead of a string
      // Only show "You're next!" when position is 1, otherwise show the calculated wait time

      // Note: Time formatting is done later with safety checks for minimum wait times

      // Use displayWaitTimeMinutes if available, otherwise fall back to waitTimeMinutes
      const rawDisplayWaitTime =
        estimatedWaitTime.displayWaitTimeMinutes !== undefined
          ? estimatedWaitTime.displayWaitTimeMinutes
          : waitTimeMinutes;

      // Safety check: For positions > 2, ensure we never show less than minimum expected time
      // Get the actual service time for accurate minimum calculation
      const actualServiceTime =
        servingStatus?.servingTimeMinutes ||
        queueData?.serveTime ||
        serviceData?.queueInfo?.servingTime ||
        5; // Default to 5 minutes if all else fails

      // Formula: (position - 2) because we don't count the first person (being served) and ourselves
      const minimumExpectedMinutes =
        queueData.position > 2
          ? (queueData.position - 2) * actualServiceTime
          : 0;
      const displayWaitTime =
        queueData.position > 2
          ? Math.max(rawDisplayWaitTime, minimumExpectedMinutes)
          : rawDisplayWaitTime;

      console.log(
        `🛡️ Safety check: pos=${queueData.position}, serviceTime=${actualServiceTime}min, peopleAhead=${Math.max(0, queueData.position - 2)}, minExpected=${minimumExpectedMinutes}min, raw=${rawDisplayWaitTime}min, final=${displayWaitTime}min`
      );

      // Timer adjustment check removed - no longer needed for status badges

      // Recalculate formatted time with the safe display wait time
      const safeTotalMinutes = Math.ceil(displayWaitTime);
      const safeHours = Math.floor(safeTotalMinutes / 60);
      const safeMinutes = safeTotalMinutes % 60;

      let safeFormattedTime = "";
      if (safeHours > 0) {
        safeFormattedTime = `${safeHours} hour${safeHours > 1 ? "s" : ""} ${safeMinutes > 0 ? `${safeMinutes} min${safeMinutes > 1 ? "s" : ""}` : ""}`;
      } else {
        safeFormattedTime = `${safeMinutes} min${safeMinutes > 1 ? "s" : ""}`;
      }

      const waitTimeText =
        queueData.position === 1
          ? "You're next!"
          : displayWaitTime < minimumExpectedMinutes
            ? `${minimumExpectedMinutes} min` // Show minimum expected time for positions > 2
            : safeFormattedTime; // Show safe formatted time

      // Return the formatted wait time text without status badges
      return (
        <Text className="font-poppins-medium text-primary-500 text-base">
          {waitTimeText}
        </Text>
      );
    }
    // Fallback to position-based calculation if dynamic estimation is not available
    else if (queueData.position > 0 && serviceData?.queueInfo.servingTime) {
      // Use initialPositionAtJoin from estimatedWaitTime if available, otherwise fall back to position
      const waitTimeData = estimatedWaitTime as unknown as EstimatedWaitTime;
      const positionForCalculation =
        waitTimeData?.initialPositionAtJoin || queueData.position;
      const waitTimeMinutes =
        (positionForCalculation - 1) * serviceData.queueInfo.servingTime;

      // Log wait time calculation details for debugging
      console.log(
        `Wait time calculation (fallback): position=${queueData.position}, positionForCalculation=${positionForCalculation}, servingTime=${serviceData.queueInfo.servingTime}, waitTimeMinutes=${waitTimeMinutes}`
      );

      // Format the wait time text with status indicator and include seconds to show timer is running
      // We'll return a JSX element instead of a string
      // Only show "You're next!" when position is 1, otherwise show the calculated wait time

      // Calculate minutes part for display
      const totalSeconds = Math.max(0, Math.round(waitTimeMinutes * 60));
      const totalMinutes = Math.ceil(totalSeconds / 60); // Round up to nearest minute

      // Calculate hours and minutes for display (no seconds)
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;

      // Format time as "X mins" or "X hour Y mins"
      let formattedTime = "";
      if (hours > 0) {
        formattedTime = `${hours} hour${hours > 1 ? "s" : ""} ${minutes > 0 ? `${minutes} min${minutes > 1 ? "s" : ""}` : ""}`;
      } else {
        formattedTime = `${minutes} min${minutes > 1 ? "s" : ""}`;
      }

      const waitTimeText =
        queueData.position === 1
          ? "You're next!"
          : waitTimeMinutes < 1
            ? "1 min" // Always show at least 1 minute even for very short wait times
            : formattedTime; // Show simplified time format

      // Return the formatted wait time text without status badges
      return (
        <Text className="font-poppins-medium text-primary-500 text-base">
          {waitTimeText}
        </Text>
      );
    } else {
      return (
        <Text className="font-poppins-medium text-primary-500 text-base">
          Unknown
        </Text>
      );
    }
  };

  // Handle check-in
  const handleCheckIn = async () => {
    if (!queueData?.id || !user?.id) return;

    try {
      setCheckingIn(true);

      // Call the toggle check-in endpoint
      const response = await fetch(
        `${API_BASE_URL}/customer/queue/${queueData.id}/toggle-check-in`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ userId: user.id }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to toggle check-in status");
      }

      const result = await response.json();

      // Update local state - only isCheckedIn changes, status remains the same
      setQueueData((prev) =>
        prev
          ? {
              ...prev,
              isCheckedIn:
                result.data?.isCheckedIn !== undefined
                  ? result.data.isCheckedIn
                  : result.queue?.isCheckedIn !== undefined
                    ? result.queue.isCheckedIn
                    : false,
            }
          : null
      );

      Alert.alert(
        "Success",
        result.data?.isCheckedIn !== undefined
          ? result.data.isCheckedIn
            ? "You have successfully checked in!"
            : "Your check-in status has been reset to waiting."
          : result.queue?.isCheckedIn
            ? "You have successfully checked in!"
            : "Your check-in status has been reset to waiting."
      );

      // Refresh data
      debouncedFetchQueueData();
    } catch (error) {
      console.error("Error toggling check-in status:", error);
      Alert.alert(
        "Error",
        "Failed to update check-in status. Please try again."
      );
    } finally {
      setCheckingIn(false);
    }
  };

  // Handle leave queue (cancel)
  const handleLeaveQueue = async () => {
    if (!queueData?.id || !user?.id) return;

    // Confirm with user
    Alert.alert(
      "Leave Queue",
      "Are you sure you want to leave this queue? This action cannot be undone.",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Yes, Leave Queue",
          onPress: async () => {
            try {
              setCancelling(true);

              // Call our new updateQueueStatus function
              await updateQueueStatus(queueData.id.toString(), "cancelled");

              // Update local state
              setQueueData((prev) =>
                prev ? { ...prev, status: "cancelled" } : null
              );

              Alert.alert("Success", "You have successfully left the queue.");

              // Redirection is now handled in updateQueueStatus function
            } catch (error) {
              console.error("Error leaving queue:", error);
              Alert.alert("Error", "Failed to leave queue. Please try again.");
            } finally {
              setCancelling(false);
            }
          },
        },
      ]
    );
  };

  // Handle reschedule
  const handleReschedule = () => {
    if (!queueData?.serviceId) return;

    router.push({
      pathname: "/(root)/join-queue",
      params: {
        serviceId: queueData.serviceId.toString(),
        reschedule: "true",
        oldQueueId: queueData.id.toString(),
      },
    });
  };

  // Handle call
  const handleCall = () => {
    if (!queueData?.contactPhone) {
      Alert.alert("Error", "Phone number not available");
      return;
    }

    Linking.openURL(`tel:${queueData.contactPhone}`).catch(() =>
      Alert.alert("Error", "Could not open phone application")
    );
  };

  // Handle get directions
  const handleGetDirections = () => {
    if (!queueData?.address?.coordinates) {
      Alert.alert("Error", "Location coordinates not available");
      return;
    }

    const { latitude, longitude } = queueData.address.coordinates;
    const destination = queueData.address.googleMapsLink
      ? queueData.address.googleMapsLink
      : `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`;

    Linking.openURL(destination).catch(() =>
      Alert.alert("Error", "Could not open map application")
    );
  };

  // Get image source
  const getImageSource = () => {
    if (
      queueData?.images &&
      Array.isArray(queueData.images) &&
      queueData.images.length > 0 &&
      queueData.images[0]
    ) {
      return { uri: queueData.images[0] };
    }
    // Fallback to placeholder image
    return images.image;
  };

  // Fetch estimated wait time from the backend
  const fetchEstimatedWaitTime = async () => {
    if (!queueId) return;

    try {
      // Show small loading indicator instead of blanking out the screen
      setIsRefreshingData(true);

      console.log(`Fetching estimated wait time for queue ${queueId}`);

      // Call the API to get estimated wait time
      const response = await fetch(
        `${API_BASE_URL}/customer/queue/${queueId}/estimated-wait-time`
      );

      if (!response.ok) {
        console.error(
          `Failed to fetch estimated wait time: ${response.status}`
        );
        return;
      }

      const result = await response.json();

      if (result.status === "success" && result.data) {
        console.log("Estimated wait time data:", JSON.stringify(result.data));
        console.log(
          `DETAILED WAIT TIME: position=${result.data.position}, initialPositionAtJoin=${result.data.initialPositionAtJoin}, waitTimeMinutes=${result.data.waitTimeMinutes}, waitTimeStatus=${result.data.waitTimeStatus}`
        );

        // Check if we already have a timer running
        if (waitTimeCountdownInterval) {
          console.log(
            "⏸️ Timer already running - IGNORING update to prevent interference"
          );
          return; // Don't interfere with running timer
        }

        // Also check if this is a position change - if so, we should restart
        if (
          estimatedWaitTime &&
          estimatedWaitTime.position !== result.data.position
        ) {
          console.log(
            `🔄 Position changed from ${estimatedWaitTime.position} to ${result.data.position} - clearing old timer`
          );
          if (waitTimeCountdownInterval) {
            clearInterval(waitTimeCountdownInterval);
            setWaitTimeCountdownInterval(null);
          }
        }

        // Start new timer
        console.log("🚀 Starting new SIMPLE timer");

        const serverData = {
          ...result.data,
          receivedAt: new Date().getTime(),
          originalWaitTimeMinutes: result.data.waitTimeMinutes,
          displayWaitTimeMinutes: result.data.waitTimeMinutes,
          remainingSeconds: Math.ceil(result.data.waitTimeMinutes * 60),
        };

        setEstimatedWaitTime(serverData);

        setTimeout(() => {
          startWaitTimeCountdownWithData(serverData);
        }, 100);
      }
    } catch (error) {
      console.error("Error fetching estimated wait time:", error);
    } finally {
      // Reset the loading indicator
      setIsRefreshingData(false);
    }
  };

  // FRESH SIMPLE TIMER - ONLY RUNS WHEN 1ST PERSON IS SERVING
  const startWaitTimeCountdownWithData = (data: EstimatedWaitTime) => {
    console.log(`🚀 FRESH TIMER for position ${data.position}`);

    if (waitTimeCountdownInterval) {
      clearInterval(waitTimeCountdownInterval);
      setWaitTimeCountdownInterval(null);
    }

    const serviceTime =
      servingStatus?.servingTimeMinutes ||
      queueData?.serveTime ||
      serviceData?.queueInfo?.servingTime ||
      10;

    // Calculate pause times for different scenarios
    const waitingPauseMinutes = (data.position - 1) * serviceTime; // When 1st person waiting
    const overtimePauseMinutes =
      data.position === 2
        ? serviceTime * 0.1 // Position 2: 10% of serve time
        : (data.position - 2) * serviceTime; // Position > 2: (position-2) * serve time

    // Add 10% buffer for position 2
    let initialWaitTime = data.waitTimeMinutes;
    if (data.position === 2) {
      initialWaitTime = data.waitTimeMinutes + serviceTime * 0.1;
      console.log(
        `Position 2 buffer: ${data.waitTimeMinutes} + ${(serviceTime * 0.1).toFixed(1)} = ${initialWaitTime.toFixed(1)} min`
      );
    }

    let currentSeconds = Math.ceil(initialWaitTime * 60);
    let isTimerRunning = false;

    console.log(
      `Position ${data.position}: WaitingPause=${waitingPauseMinutes}min, OvertimePause=${overtimePauseMinutes}min, Initial=${initialWaitTime.toFixed(1)}min`
    );

    // Check if 1st person is serving and determine pause logic
    const checkAndStartTimer = async () => {
      try {
        const serviceId = queueData?.serviceId;
        if (!serviceId) return;

        const response = await fetch(
          `${API_BASE_URL}/customer/queues/redis/${serviceId}/all`
        );
        if (response.ok) {
          const result = await response.json();
          if (result.status === "success" && result.queues) {
            const firstPerson = result.queues.find(
              (q: any) =>
                q.serviceId === serviceId &&
                q.date === queueData?.date &&
                q.timeSlot === queueData?.timeSlot &&
                q.position === 1
            );

            const isFirstPersonServing =
              firstPerson?.status === "serving" &&
              firstPerson?.currentlyServing;
            let shouldPause = false;
            let pauseAtMinutes = 0;
            let pauseReason = "";

            if (isFirstPersonServing && firstPerson?.servingStartedAt) {
              // Check if 1st person is overtime
              const startTime = new Date(
                firstPerson.servingStartedAt
              ).getTime();
              const now = new Date().getTime();
              const elapsedMinutes = (now - startTime) / (1000 * 60);

              if (elapsedMinutes > serviceTime) {
                // 1st person overtime - use overtime pause logic
                shouldPause = true;
                pauseAtMinutes = overtimePauseMinutes;
                pauseReason = `1st person overtime (${elapsedMinutes.toFixed(1)}min > ${serviceTime}min)`;
                console.log(
                  `⏸️ ${pauseReason} - pause at ${pauseAtMinutes}min`
                );

                // Update first person status
                setFirstPersonStatus({
                  status: "overtime",
                  message: `First person is taking longer than expected\n(${Math.ceil(elapsedMinutes)} min of ${serviceTime} min service)`,
                });
              } else {
                // 1st person serving normally - timer can run
                console.log(
                  `✅ 1st person serving normally: ${elapsedMinutes.toFixed(1)}min / ${serviceTime}min`
                );

                // Update first person status
                setFirstPersonStatus({
                  status: "serving",
                  // message: `First person is being served`,
                  message: `First person is being served\n(${Math.ceil(elapsedMinutes)} min of ${serviceTime} min service)`,
                });
              }
            } else if (firstPerson?.status === "waiting") {
              // 1st person waiting - use waiting pause logic
              shouldPause = true;
              pauseAtMinutes = waitingPauseMinutes;
              pauseReason = "1st person waiting";
              console.log(`⏸️ ${pauseReason} - pause at ${pauseAtMinutes}min`);

              // Update first person status
              setFirstPersonStatus({
                status: "waiting",
                message: "First person in line is waiting to be served",
              });
            } else if (!firstPerson) {
              // No first person found - might be completed
              setFirstPersonStatus({
                status: "completed",
                message: "Queue is moving - next person will be served soon",
              });
            } else {
              // Unknown status
              setFirstPersonStatus({
                status: "unknown",
                message: "Checking queue status...",
              });
            }

            if (!shouldPause && !isTimerRunning) {
              // START TIMER - 1st person serving normally
              console.log(`▶️ Starting timer - 1st person serving normally`);
              isTimerRunning = true;

              const timer = setInterval(() => {
                const minutes = Math.floor(currentSeconds / 60);

                // Don't go below 1 minute (60 seconds)
                if (currentSeconds > 60) {
                  currentSeconds -= 1;
                  console.log(`⏬ ${minutes}min ${currentSeconds % 60}s`);

                  setEstimatedWaitTime((prev) =>
                    prev
                      ? {
                          ...prev,
                          displayWaitTimeMinutes: minutes,
                          remainingSeconds: currentSeconds,
                        }
                      : prev
                  );
                } else {
                  // Pause at 1 minute - don't count down further
                  currentSeconds = 60;
                  console.log(
                    `⏸️ Timer paused at 1 minute - minimum wait time`
                  );

                  setEstimatedWaitTime((prev) =>
                    prev
                      ? {
                          ...prev,
                          displayWaitTimeMinutes: 1,
                          remainingSeconds: 60,
                          waitTimeStatus: "delayed",
                        }
                      : prev
                  );
                }
              }, 1000);

              setWaitTimeCountdownInterval(timer);
            } else if (shouldPause && isTimerRunning) {
              // STOP TIMER - pause condition met
              console.log(`⏸️ Stopping timer - ${pauseReason}`);
              isTimerRunning = false;

              if (waitTimeCountdownInterval) {
                clearInterval(waitTimeCountdownInterval);
                setWaitTimeCountdownInterval(null);
              }

              // Set to pause time
              currentSeconds = pauseAtMinutes * 60;
              console.log(`⏸️ Paused at ${pauseAtMinutes} minutes`);

              setEstimatedWaitTime((prev) =>
                prev
                  ? {
                      ...prev,
                      displayWaitTimeMinutes: pauseAtMinutes,
                      remainingSeconds: currentSeconds,
                      waitTimeStatus: "delayed",
                    }
                  : prev
              );
            }
          }
        }
      } catch (error) {
        console.error("Error:", error);
      }
    };

    // Check every 3 seconds
    setInterval(checkAndStartTimer, 3000);
    checkAndStartTimer(); // Initial check

    console.log("✅ Fresh timer mechanism started");
  };

  // Note: We've removed the legacy startWaitTimeCountdown function
  // and now use startWaitTimeCountdownWithData directly

  // Check if queue is currently being served
  const checkServingStatus = async () => {
    if (!queueId) return;

    try {
      // Call the API to check serving status
      const response = await fetch(
        `${API_BASE_URL}/customer/queue/${queueId}/serving-status`
      );

      if (!response.ok) {
        console.error(`Failed to check serving status: ${response.status}`);
        return;
      }

      const result = await response.json();
      const status = result.data;

      console.log("Serving status:", status);

      // If the queue is being served, update state and start polling
      if (status.isServing) {
        setServingStatus(status);
        setRemainingServingTime(status.remainingSeconds);
        setInitialServingTime(status.remainingSeconds);

        // Store the serving start time - make sure it's not null
        if (status.servingStartedAt) {
          console.log(
            `Setting servingStartedAt to: ${status.servingStartedAt}`
          );
          // No need to set it here as it's already in the servingStatus object
        } else {
          console.warn(`No servingStartedAt in response, using current time`);
          // If no servingStartedAt is provided, use current time minus the elapsed time
          const now = new Date();
          const elapsedSeconds =
            status.servingTimeMinutes * 60 - status.remainingSeconds;
          const estimatedStartTime = new Date(
            now.getTime() - elapsedSeconds * 1000
          );
          console.log(
            `Estimated start time: ${estimatedStartTime.toISOString()}`
          );

          // Create a new status object with the estimated start time
          const updatedStatus = {
            ...status,
            servingStartedAt: estimatedStartTime.toISOString(),
          };

          // Update the status with the estimated start time
          setServingStatus(updatedStatus);
        }

        // Start polling for updates if not already polling
        if (!servingTimer) {
          startServingTimerPolling();
        }
      } else {
        // Not being served
        setServingStatus(null);

        // If timer was running, stop it
        if (servingTimer) {
          clearInterval(servingTimer);
          setServingTimer(null);
        }
      }
    } catch (error) {
      console.error("Error checking serving status:", error);
    }
  };

  /**
   * Start polling for serving status updates
   */
  const startServingTimerPolling = () => {
    // Clear any existing timer
    if (servingTimer) {
      clearInterval(servingTimer);
    }

    // Create a new timer that updates every second for real-time countdown
    const timer = setInterval(() => {
      setRemainingServingTime((prev) => {
        // Always decrement by 1, whether positive or negative
        // This ensures we track overtime correctly when prev is negative
        const newValue = prev - 1;
        console.log(`Timer update: ${prev} -> ${newValue}`);
        return newValue;
      });

      // Sync with backend every 15 seconds to ensure we're still in sync
      if (remainingServingTime % 15 === 0 && remainingServingTime > 0) {
        // Check serving status to sync time
        if (queueId) {
          fetch(`${API_BASE_URL}/customer/queue/${queueId}/serving-status`)
            .then((response) => {
              if (!response.ok) {
                throw new Error(
                  `Failed to sync serving status: ${response.status}`
                );
              }
              return response.json();
            })
            .then((result) => {
              const status = result.data;

              // Only update state if component is still mounted
              if (isMounted.current) {
                // Update state with latest status
                setServingStatus(status);

                // Only update both remaining and initial time if it differs significantly (>3 seconds)
                // to avoid jumpy UI
                if (
                  Math.abs(status.remainingSeconds - remainingServingTime) > 3
                ) {
                  setRemainingServingTime(status.remainingSeconds);
                  setInitialServingTime(status.remainingSeconds);
                }

                // If service is completed or not serving anymore, stop polling
                if (!status.isServing) {
                  clearInterval(timer);
                  setServingTimer(null);

                  // Refresh queue data to show the latest status
                  debouncedFetchQueueData();
                }
              }
            })
            .catch((error) => {
              console.error("Error syncing serving status:", error);
            });
        }
      }
    }, 1000); // Update every second for real-time countdown

    // Store timer for cleanup
    setServingTimer(timer);
  };

  /**
   * Format the remaining time as MM:SS
   * Note: This function is kept for future use when implementing serving time display
   */
  // const formatServingTimeRemaining = (seconds: number): string => {
  //   const minutes = Math.floor(seconds / 60);
  //   const remainingSeconds = seconds % 60;
  //   return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
  // };

  // Create a debounced version of fetchEstimatedWaitTime
  const debouncedFetchEstimatedWaitTime = useCallback(
    debounce(() => {
      if (queueId && queueData?.status === "waiting") {
        fetchEstimatedWaitTime();
      }
    }, 500), // 500ms debounce time
    [queueId, queueData?.status]
  );

  // Create a debounced version of fetchQueueData
  const debouncedFetchQueueData = useCallback(
    debounce(() => {
      fetchQueueData();
    }, 500), // 500ms debounce time
    [fetchQueueData]
  );

  // Add a useEffect to periodically refresh the estimated wait time
  useEffect(() => {
    let waitTimeTimer: NodeJS.Timeout | null = null;

    // Only set up the timer if we have a queue ID and the queue is in waiting status
    if (queueId && queueData?.status === "waiting") {
      console.log("Setting up estimated wait time refresh timer");

      // DISABLED: Periodic refresh to prevent timer interference
      // waitTimeTimer = setInterval(() => {
      //   console.log('Periodic wait time refresh triggered');
      //   debouncedFetchEstimatedWaitTime();
      // }, 30000);
      console.log("Periodic refresh DISABLED to prevent timer interference");

      // Initial fetch (debounced) only if we don't already have a timer running
      if (!waitTimeCountdownInterval) {
        console.log("Initial wait time fetch triggered");
        debouncedFetchEstimatedWaitTime();

        // If we already have wait time data, start the timer immediately
        if (
          estimatedWaitTime &&
          estimatedWaitTime.waitTimeMinutes !== undefined
        ) {
          console.log(
            "Using existing wait time data to start timer immediately"
          );
          startWaitTimeCountdownWithData(estimatedWaitTime);
        }
      } else {
        console.log("Timer already running, not starting a new one");
      }
    } else {
      console.log(
        `Not setting up wait time timer: queueId=${queueId}, status=${queueData?.status}`
      );

      // Clean up any existing timers if the queue is no longer in waiting status
      if (waitTimeCountdownInterval) {
        console.log(
          "Cleaning up existing wait time countdown timer due to status change"
        );
        clearInterval(waitTimeCountdownInterval);
        setWaitTimeCountdownInterval(null);
      }
    }

    // Clean up on unmount or when params change
    return () => {
      console.log("Cleaning up wait time timers on unmount");

      if (waitTimeTimer) {
        clearInterval(waitTimeTimer);
      }

      // Don't clear the countdown interval here to prevent timer restarts
      // We'll only clear it when the component unmounts completely
    };
  }, [queueId, queueData?.status, debouncedFetchEstimatedWaitTime]);

  // Separate useEffect to handle component unmount cleanup
  useEffect(() => {
    return () => {
      // This will only run when the component is completely unmounted
      if (waitTimeCountdownInterval) {
        console.log("Final cleanup of wait time countdown timer");
        clearInterval(waitTimeCountdownInterval);
        setWaitTimeCountdownInterval(null);
      }
    };
  }, []);

  // Add a useEffect for the grace period timer similar to the serving timer
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;

    // Only set up the timer if we're in grace period
    if (inGracePeriod && remainingGraceTime > 0) {
      console.log(
        `Setting up grace period timer with remaining time: ${remainingGraceTime} seconds`
      );
      timer = setInterval(() => {
        setRemainingGraceTime((prev) => {
          if (prev <= 1) {
            if (timer) {
              clearInterval(timer);
            }
            // When timer reaches zero, set inGracePeriod to false
            setInGracePeriod(false);

            // Immediately call the API to mark grace period as expired
            handleGracePeriodExpired();

            // Refresh data
            debouncedFetchQueueData();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    // Clean up on unmount or when params change
    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [inGracePeriod, gracePeriodStatus]);

  /**
   * Immediately mark the queue as no-show when grace period expires client-side
   * This prevents the delay between the timer reaching zero and the backend detecting it
   */
  const handleGracePeriodExpired = async () => {
    if (!queueId) return;

    try {
      console.log(
        "Grace period expired, calling API to mark as no-show immediately"
      );
      const response = await fetch(
        `${API_BASE_URL}/customer/queue/${queueId}/grace-period-expired`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        console.error(
          `Failed to mark grace period as expired: ${response.status}`
        );
      } else {
        console.log("Successfully marked grace period as expired");
      }
    } catch (error) {
      console.error("Error marking grace period as expired:", error);
    }
  };

  // Simple text-only timer display
  const SimpleCircularTimer = () => {
    // Get service time from service data or from the queue's serveTime
    // First try to get it from servingStatus which has the most accurate data
    const serviceTime =
      servingStatus?.servingTimeMinutes ||
      queueData?.serveTime ||
      serviceData?.queueInfo?.servingTime ||
      5; // Default to 5 minutes if all else fails
    const serviceTimeSeconds = serviceTime * 60;

    console.log(
      `Using service time: ${serviceTime} minutes (${serviceTimeSeconds} seconds)`
    );
    console.log(
      `Service time sources: servingStatus.servingTimeMinutes=${servingStatus?.servingTimeMinutes}, queueData.serveTime=${queueData?.serveTime}, serviceData.queueInfo.servingTime=${serviceData?.queueInfo?.servingTime}`
    );

    // Calculate if we're in overtime based on real-world time
    let isOvertime = false;
    let overtimeSeconds = 0;

    // Debug the current time values
    console.log(`Debug - Current time values:`);
    console.log(`serviceTime: ${serviceTime}`);
    console.log(`serviceTimeSeconds: ${serviceTimeSeconds}`);
    console.log(`remainingServingTime: ${remainingServingTime}`);
    console.log(`initialServingTime: ${initialServingTime}`);

    // Get the most accurate servingStartedAt
    const effectiveServingStartedAt = servingStatus?.servingStartedAt;

    if (effectiveServingStartedAt) {
      // Calculate based on actual start time and current time
      const startTime = new Date(effectiveServingStartedAt).getTime();
      const currentTime = new Date().getTime();
      const elapsedMilliseconds = currentTime - startTime;
      const elapsedSeconds = Math.floor(elapsedMilliseconds / 1000);

      console.log(
        `Service time: ${serviceTime} minutes (${serviceTimeSeconds} seconds)`
      );
      console.log(`Elapsed time: ${elapsedSeconds} seconds`);
      console.log(
        `Started at: ${new Date(effectiveServingStartedAt).toLocaleTimeString()}`
      );
      console.log(`Current time: ${new Date().toLocaleTimeString()}`);
      console.log(`ServingStartedAt source: ${effectiveServingStartedAt}`);

      // Force overtime if the elapsed time is greater than service time
      // This is the key fix - we're forcing overtime based on real-world time
      if (elapsedSeconds > serviceTimeSeconds) {
        isOvertime = true;
        overtimeSeconds = elapsedSeconds - serviceTimeSeconds;
        console.log(`In overtime: ${overtimeSeconds} seconds over`);
      }
    } else {
      // Fallback to the old calculation if servingStartedAt is not available
      console.log(`No servingStartedAt available, using fallback calculation`);
      console.log(
        `remainingServingTime: ${remainingServingTime}, initialServingTime: ${initialServingTime}`
      );

      // Always consider overtime if remainingServingTime is negative
      if (remainingServingTime < 0) {
        isOvertime = true;
        overtimeSeconds = Math.abs(remainingServingTime);
        console.log(
          `Overtime based on negative remainingServingTime: ${overtimeSeconds} seconds`
        );
      }
      // Or if we've served longer than the service time
      else if (
        initialServingTime > 0 &&
        initialServingTime - remainingServingTime > serviceTimeSeconds
      ) {
        isOvertime = true;
        overtimeSeconds =
          initialServingTime - remainingServingTime - serviceTimeSeconds;
        console.log(
          `Overtime based on elapsed time: ${overtimeSeconds} seconds`
        );
      }
    }

    // Format time as HH:MM:SS
    const getTimeString = () => {
      if (isOvertime) {
        // Format the overtime in hh:mm:ss format
        const overtimeHours = Math.floor(overtimeSeconds / 3600);
        const overtimeMinutes = Math.floor((overtimeSeconds % 3600) / 60);
        const overtimeRemainingSeconds = Math.floor(overtimeSeconds % 60);
        return `${overtimeHours.toString().padStart(2, "0")}:${overtimeMinutes.toString().padStart(2, "0")}:${overtimeRemainingSeconds.toString().padStart(2, "0")}`;
      } else {
        // Normal countdown display in hh:mm:ss format
        const hours = Math.floor(remainingServingTime / 3600);
        const minutes = Math.floor((remainingServingTime % 3600) / 60);
        const seconds = Math.floor(remainingServingTime % 60);
        return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
      }
    };

    return (
      <View className="items-center justify-center">
        {isOvertime ? (
          <>
            <Text className="text-danger-600 font-poppins-bold text-4xl">
              {getTimeString()}
            </Text>
            <Text className="text-danger-600">extra time</Text>
          </>
        ) : (
          <>
            <Text className="text-primary-500 font-poppins-bold text-4xl">
              {getTimeString()}
            </Text>
            <Text className="text-secondary-600">remaining</Text>
          </>
        )}
      </View>
    );
  };

  // Loading screen
  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#159AFF" />
        </View>
      </SafeAreaView>
    );
  }

  // Error screen if no data
  if (!queueData) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />
        <View className="flex-1 justify-center items-center px-6">
          <Text className="text-xl font-poppins-medium text-center mb-4">
            Queue information not found
          </Text>
          <Text className="text-secondary-600 text-center mb-6">
            We couldn't find information about this queue. It may have been
            cancelled or expired.
          </Text>
          <TouchableOpacity
            className="bg-primary-500 py-3 px-8 rounded-xl"
            onPress={() => router.replace("/(root)/(tabs)/queues")}
          >
            <Text className="text-white font-poppins-medium">
              Go to My Queues
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Header */}
      <View className="bg-white pb-4">
        <View className="px-8 pt-12">
          <View className="flex-row justify-start mt-4">
            <TouchableOpacity
              className="flex-row items-center absolute left-0 -top-2 border border-secondary-600 w-12 h-12 rounded-full justify-center"
              onPress={() => router.back()}
            >
              <Image source={images.back} className="w-4 h-4" />
            </TouchableOpacity>

            <View className="flex items-center justify-center w-full">
              <Text className="font-poppins-medium text-xl">Queue Status</Text>
              {/* Status Badge */}
              <View className="mt-2 mb-6">
                <View className={` flex-row items-center justify-center`}>
                  <View
                    className={`w-2 h-2 rounded-full mr-2 ${
                      queueStatus === "not-started"
                        ? "bg-warning-500"
                        : queueStatus === "live"
                          ? "bg-primary-500"
                          : "bg-danger-600"
                    }`}
                  />
                  <Text
                    className={`font-poppins-medium text-sm ${
                      queueStatus === "not-started"
                        ? "text-warning-500"
                        : queueStatus === "live"
                          ? "text-primary-500"
                          : "text-danger-600"
                    }`}
                  >
                    {queueStatus === "not-started"
                      ? "Upcoming Queue"
                      : queueStatus === "live"
                        ? "Live Queue"
                        : "Queue Ended"}
                  </Text>
                </View>
              </View>
              {/* Slot ID */}
              <View className="flex-row items-center mb-4 bg-secondary-600/10 rounded-2xl p-4">
                <Image source={images.id} className="w-4 h-4 mr-2" />
                <View className="flex-row items-center justify-center">
                  <Text className="text-secondary-600 text-sm">Slot ID: </Text>
                  <Text className="font-poppins-medium text-base">
                    {queueData.uniqueSlotId &&
                    queueData.uniqueSlotId.includes("-")
                      ? queueData.uniqueSlotId.split("-").pop() // Extract just the number part if in old format
                      : queueData.uniqueSlotId ||
                        Math.floor(1000 + Math.random() * 9000).toString()}
                  </Text>
                </View>
              </View>
              <View className="h-[1px] w-[90%] bg-secondary-600/20 rounded-full mt-4" />
              <View className="flex-row items-center mt-6">
                {queueData.isVIP ? (
                  <View className="flex-row items-center justify-center">
                    <Image
                      source={images.vip}
                      className="w-5 h-5 mr-2"
                      tintColor="#FFB800"
                    />
                    <Text className="font-poppins-medium text-secondary-600 text-base">
                      VIP Queue
                    </Text>
                  </View>
                ) : (
                  <Text className="font-poppins-medium text-secondary-600 text-base">
                    Normal Queue
                  </Text>
                )}
              </View>
            </View>
          </View>
        </View>
      </View>

      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={["#159AFF"]}
            tintColor="#159AFF"
          />
        }
      >
        {/* Grace Period UI - shown when it's the user's turn and they need to confirm presence */}
        {inGracePeriod && gracePeriodStatus ? (
          <View className="px-10 mx-10 items-start border border-primary-500/10 rounded-[35px] justify-center py-10">
            <View className=" flex-row items-center justify-start mb-10">
              <View className="p-3 rounded-full bg-primary-500/10  mr-5">
                <Image source={images.alert} className="w-10 h-10" />
              </View>
              <View className=" flex-col  items-start">
                <Text className="text-xl font-poppins-semibold text-primary-500 ">
                  It's Your Turn!
                </Text>

                <Text className="text-secondary-600 font-medium">
                  Confirm Your Presence
                </Text>
              </View>
            </View>

            <View className="flex-col w-full justify-center items-center">
              <View className="h-[1px] w-80 bg-secondary-600/20 rounded-full mb-8" />
              {/* Timer Display */}
              <View className="mb-3 flex-row items-center justify-center">
                <Text className="text-secondary-600 font-poppins-medium text-sm text-center mr-2">
                  Time Remaining:
                </Text>
                <Text className="text-primary-500 font-poppins-medium text-lg text-center">
                  {formatGraceTimeRemaining(remainingGraceTime)}
                </Text>
              </View>

              {/* Progress Bar */}
              <View className="w-full h-2 bg-gray-200 rounded-full mb-8 overflow-hidden">
                <Animated.View
                  className="h-full bg-primary-500 rounded-full"
                  style={{
                    width: `${calculateGraceProgress() * 100}%`,
                    backgroundColor:
                      calculateGraceProgress() > 0.6
                        ? "#159AFF" // Blue for > 60%
                        : calculateGraceProgress() > 0.3
                          ? "#F7B801" // Yellow for 30-60%
                          : "#FF4D4F", // Red for < 30%
                  }}
                />
              </View>

              {/* Confirmation Buttons */}
              <View className="flex-col justify-center space-x-4 w-full mb-6">
                <TouchableOpacity
                  className={`bg-danger-500 w-full flex-row justify-center items-center rounded-xl p-4 mb-4 ${
                    confirmingPresence ? "opacity-70" : ""
                  }`}
                  onPress={() => confirmPresence(false)}
                  disabled={confirmingPresence}
                >
                  {confirmingPresence ? (
                    <ActivityIndicator size="small" color="#ffffff" />
                  ) : (
                    <>
                      <Image
                        source={images.close}
                        className="w-6 h-6 mr-2"
                        tintColor={"#ffff"}
                      />
                      <Text className="text-white font-poppins-medium">
                        I'm Not Arrived,{" "}
                        <Text className="text-white font-poppins-regular text-sm">
                          Leave the Queue
                        </Text>
                      </Text>
                    </>
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  className={`bg-warning-500 w-full flex-row justify-center items-center rounded-xl p-4 mb-4 ${
                    confirmingPresence ? "opacity-70" : ""
                  }`}
                  onPress={() => moveToEndOfLine()}
                  disabled={confirmingPresence}
                >
                  {confirmingPresence ? (
                    <ActivityIndicator size="small" color="#ffffff" />
                  ) : (
                    <>
                      <Image
                        source={images.clock}
                        className="w-5 h-5 mr-3"
                        tintColor={"#ffff"}
                      />
                      <Text className="text-white font-poppins-medium">
                        Move to Last of Line
                      </Text>
                    </>
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  className={`bg-primary-500 w-full flex-row justify-center items-center rounded-xl p-4 ${
                    confirmingPresence ? "opacity-70" : ""
                  }`}
                  onPress={() => confirmPresence(true)}
                  disabled={confirmingPresence}
                >
                  {confirmingPresence ? (
                    <ActivityIndicator size="small" color="#ffffff" />
                  ) : (
                    <>
                      <Image source={images.check} className="w-4 h-3 mr-3" />
                      <Text className="text-white font-poppins-medium">
                        I'm Arrived,{" "}
                        <Text className="text-white font-poppins-regular text-sm">
                          Proceed to the Counter
                        </Text>
                      </Text>
                    </>
                  )}
                </TouchableOpacity>
              </View>

              <Text className="text-secondary-600 text-center text-sm mt-2">
                If you don't respond in time, your slot will be given to the
                next person in line.
              </Text>
            </View>
          </View>
        ) : queueData?.status === "serving" || servingStatus?.isServing ? (
          // UI for when service has started
          <View className="px-10 mx-10 items-center border border-primary-500/10 rounded-[35px] justify-center py-10">
            <View className="w-52 h-52">
              <LottieView
                source={require("@/assets/animations/serving.json")}
                autoPlay
                loop={true}
                style={{ width: "100%", height: "100%" }}
              />
            </View>
            <SimpleCircularTimer />

            <View className="h-[1px] w-80 bg-secondary-600/20 rounded-full my-10" />
            <View className="px-4 rounded-xl mb-5">
              <Text className="text-xl text-secondary-500 font-poppins-medium text-center mb-2">
                You are now being served!
              </Text>

              <Text className="text-secondary-600 font-poppins-regular text-sm text-center">
                Your service provider will assist you shortly. Thank you for
                your patience!
              </Text>
            </View>
          </View>
        ) : queueData?.position === 1 &&
          queueData?.isCheckedIn &&
          queueData?.status === "waiting" ? (
          // UI for when user is position 1, checked in, and waiting for service to start
          <View className="mx-10">
            <View className="px-10 items-center border border-primary-500/10 rounded-[35px] justify-center py-10">
              <View className="items-center justify-center">
                <View className="w-52 h-52">
                  <LottieView
                    source={require("@/assets/animations/waiting.json")}
                    autoPlay
                    loop={true}
                    style={{ width: "100%", height: "100%" }}
                  />
                </View>
              </View>
              <View className="px-4 rounded-xl mb-10">
                <Text className="text-2xl text-primary-500 font-poppins-medium text-center mb-2">
                  You're Next!
                </Text>

                <Text className="text-secondary-600 font-poppins-regular text-sm text-center">
                  Thank you for checking in. You'll be notified when your
                  service begins.
                </Text>
              </View>
            </View>
            <View className="flex-row justify-start mt-10  bg-primary-500/5 border p-4 rounded-2xl border-primary-500/20 items-center">
              <View className="p-2 px-[10px] border-2 border-primary-500 mr-3 rounded-xl">
                <Image
                  source={images.clock}
                  className="w-7 h-7  mb-1"
                  tintColor={"#159AFF"}
                />
              </View>
              <View className="flex-col justify-start items-start">
                <Text className="font-poppins-medium text-primary-500">
                  Waiting for service confirmation
                </Text>
                <Text className="font-poppins-regular text-sm text-secondary-600">
                  Your service provider will start serving you shortly
                </Text>
              </View>
            </View>
          </View>
        ) : (
          // Regular Queue Info UI (only shown when not in grace period or serving)
          <>
            {/* First Person Status Info */}
            {/* Animated Detailed Queue Status Info */}
            <View className="mx-10">
              <View
                className={`flex-row items-center w-full px-8 py-4 pb-20 rounded-[35px] bg-primary-500
              // {firstPersonStatus.status === "serving"
              //         ? "bg-success-500"
              //         : firstPersonStatus.status === "overtime"
              //           ? "bg-danger-500"
              //           : firstPersonStatus.status === "waiting"
              //             ? "bg-warning-500"
              //             : firstPersonStatus.status === "completed"
              //               ? "bg-primary-500"
              //               : "bg-gray-400" } `}
              >
                <Image
                  source={images.info}
                  className="w-5 h-5 mr-3"
                  tintColor={"#ffffff"}
                />
                <View className="flex-1">
                  <Text className="text-white text-sm font-poppins-regular">
                    {firstPersonStatus.message}
                  </Text>
                </View>
              </View>
            </View>

            {/* Queue Info Card */}
            <View className="px-10 -mt-16 mb-6 ">
              <View className="bg-white items-center justify-center rounded-[35px] border border-primary-500/10 p-6">
                <TouchableOpacity
                  className="flex-row items-center justify-center"
                  onPress={() => {
                    Alert.alert(
                      "Time Slot Details",
                      `Your queue is scheduled for:\n${formatDate(queueData.date)}\n${queueData.timeSlot}`
                    );
                  }}
                >
                  <Image
                    source={images.time}
                    className="w-8 h-8 mr-3"
                    tintColor="#159AFF"
                  />

                  <View>
                    <View className="flex-row items-center">
                      <Text className="text-secondary-600 text-sm">
                        Estimated Wait Time
                      </Text>

                      <Image source={images.info} className="w-4 h-4 ml-2" />
                    </View>
                    {calculateWaitTime()}
                  </View>
                </TouchableOpacity>
                <View className="h-[1px] w-72 bg-secondary-600/20 rounded-full mt-5" />

                <View className="flex-row items-center mt-6 mb-5">
                  <View className="flex-col items-center justify-center">
                    <Text className="text-secondary-600 font-poppins-medium text-base">
                      Your Position:
                    </Text>
                    <View className="flex-row mt-4">
                      {/* Digital clock style position display */}
                      {(() => {
                        // Get the position and convert to string
                        const displayPosition =
                          queueData.position.toString() || "0";
                        // Determine if we need leading zeros (only if position < 1000)
                        const needsLeadingZeros = displayPosition.length < 3;
                        // Calculate how many digits to display (minimum 3)
                        const digitsToShow = Math.max(
                          3,
                          displayPosition.length
                        );
                        // Create an array of digits with leading zeros if needed
                        const digits = [];

                        if (needsLeadingZeros) {
                          // Add leading zeros
                          for (
                            let i = 0;
                            i < digitsToShow - displayPosition.length;
                            i++
                          ) {
                            digits.push("0");
                          }
                          // Add the actual position digits
                          for (let i = 0; i < displayPosition.length; i++) {
                            digits.push(displayPosition[i]);
                          }
                        } else {
                          // Just use the position digits without leading zeros
                          for (let i = 0; i < displayPosition.length; i++) {
                            digits.push(displayPosition[i]);
                          }
                        }

                        // Return the digit boxes
                        return digits.map((digit, index) => (
                          <View
                            key={index}
                            className={`w-16 h-20 ${queueData.isVIP ? "bg-warning-500" : "bg-primary-500"} rounded-2xl mx-1 items-center justify-center`}
                          >
                            <Text className="text-white font-poppins-medium text-[35px] pt-2">
                              {digit}
                            </Text>
                          </View>
                        ));
                      })()}
                    </View>
                  </View>
                </View>
                {/* Queue Info */}
                <View className="border border-secondary-600 flex justify-center items-center rounded-3xl mx-10 p-6 mt-8 mb-4">
                  <Text className="font-poppins-regular text-secondary-600 absolute bottom-[47px] bg-white p-3 text-base mb-3">
                    Current Queue Count
                  </Text>
                  <View className="flex-row justify-between">
                    <View className="flex-row py-2">
                      <View className="items-center px-8 rounded-lg mr-2">
                        <View className="flex-row items-center">
                          <Image
                            source={images.queueyellow}
                            className="w-[22.37px] h-[19px] mr-2"
                            style={{ tintColor: "#FFB800" }}
                          />
                          <Text className="text-secondary-500 text-base font-poppins-medium">
                            VIP: {queueData?.queueCount?.vip || 0}
                          </Text>
                        </View>
                      </View>

                      <View className="items-center  px-8  rounded-lg">
                        <View className="flex-row items-center">
                          <Image
                            source={images.queueblue}
                            className="w-[22.37px] h-[19px] mr-2"
                            style={{ tintColor: "#159AFF" }}
                          />
                          <Text className="text-secondary-500 text-base font-poppins-medium">
                            Normal: {queueData?.queueCount?.normal || 0}
                          </Text>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </View>

            {/* Check-in button - only shown when not in grace period, not being served, and position > 1 */}
            {!inGracePeriod &&
              !servingStatus?.isServing &&
              queueData?.status === "waiting" && (
                <View className="px-10 mt-2 mb-8">
                  <View className="flex-row justify-center items-center">
                    <Text className="text-secondary-500 text-base mr-3">
                      {queueData?.isCheckedIn
                        ? "Not Arrived Yet?"
                        : "Are You Arrived?"}
                    </Text>

                    <TouchableOpacity
                      className="items-center"
                      onPress={handleCheckIn}
                      disabled={checkingIn}
                    >
                      <View
                        className={`px-4 py-2 ${queueData?.isCheckedIn ? "bg-red-500/10 border-red-500" : "bg-primary-500/10 border-primary-500"} border flex-row rounded-xl items-center justify-center`}
                      >
                        {checkingIn ? (
                          <ActivityIndicator
                            size="small"
                            className="mr-2"
                            color={
                              queueData?.isCheckedIn ? "#EF4444" : "#159AFF"
                            }
                          />
                        ) : (
                          <Image
                            source={
                              queueData?.isCheckedIn ? images.close : images.run
                            }
                            className="w-3 h-5 mr-2"
                            tintColor={
                              queueData?.isCheckedIn ? "#EF4444" : "#159AFF"
                            }
                          />
                        )}
                        <Text className="text-secondary-500 text-base">
                          {queueData?.isCheckedIn
                            ? "Cancel Check-In"
                            : "Check In"}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
          </>
        )}
      </ScrollView>

      {/* Bottom Actions */}
      <View className=" bg-white border-t border-gray-200">
        {/* Service Info */}
        <View className="px-3">
          <TouchableOpacity
            className="bg-white rounded-3xl overflow-hidden"
            onPress={() =>
              router.push({
                pathname: "/(root)/service-details",
                params: { id: queueData.serviceId.toString() },
              })
            }
          >
            <View className="p-4 my-2 w-full ">
              <View className="flex-row items-center">
                <Image
                  source={getImageSource()}
                  className="w-16 h-16 rounded-xl mr-3"
                  style={{ backgroundColor: "#f5f5f5" }}
                />
                <View className="flex-1">
                  <Text className="font-poppins-medium text-lg">
                    {queueData.serviceName}
                  </Text>
                  <View className="flex-row items-center">
                    <Text className="text-secondary-600 text-sm">
                      {queueData.serviceType}
                    </Text>
                    {queueData.hasSubUnits && queueData.subUnitName && (
                      <View className="bg-primary-50 px-2 py-1 rounded-lg">
                        <Text className="text-secondary-600 text-sm ">
                          | {queueData.subUnitName}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
                <TouchableOpacity className="items-center" onPress={handleCall}>
                  <View className="w-14 h-14 bg-primary-500/10 rounded-full items-center justify-center mr-4">
                    <Image
                      source={images.call}
                      className="w-6 h-6"
                      tintColor="#159AFF"
                    />
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  className="items-center"
                  onPress={handleGetDirections}
                >
                  <View className="w-14 h-14 bg-primary-500/10 rounded-full items-center justify-center">
                    <Image
                      source={images.direction}
                      className="w-7 h-7"
                      tintColor="#159AFF"
                    />
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableOpacity>
        </View>
        <View className="px-6 pb-6 flex-row justify-between">
          <TouchableOpacity
            className="flex-1 mr-3 py-5 px-6 rounded-2xl border border-primary-500 items-center"
            onPress={
              queueStatus !== "ended" ||
              queueData?.status === "waiting" ||
              queueData?.status === "checked-in"
                ? handleLeaveQueue
                : () => router.replace("/(root)/(tabs)/queues")
            }
            disabled={cancelling}
          >
            {cancelling ? (
              <ActivityIndicator size="small" color="#159AFF" />
            ) : (
              <Text className="text-primary-500 font-poppins-medium">
                {queueStatus !== "ended" ||
                queueData?.status === "waiting" ||
                queueData?.status === "checked-in"
                  ? "Leave Queue"
                  : "Go Back"}
              </Text>
            )}
          </TouchableOpacity>

          {(queueStatus !== "ended" ||
            queueData?.status === "waiting" ||
            queueData?.status === "checked-in") && (
            <TouchableOpacity
              className="flex-1 ml-3 py-5 px-6 rounded-2xl bg-primary-500 items-center"
              onPress={handleReschedule}
              disabled={rescheduling}
            >
              {rescheduling ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text className="text-white font-poppins-medium">
                  Reschedule
                </Text>
              )}
            </TouchableOpacity>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
}
