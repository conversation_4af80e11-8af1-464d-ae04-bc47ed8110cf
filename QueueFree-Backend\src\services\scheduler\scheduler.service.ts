import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { CustomerService } from '../../customer/customer.service';
import { QueueFlowService } from '../queue-flow/queue-flow.service';
import { RedisService } from '../redis/redis.service';

@Injectable()
export class SchedulerService {
  private readonly logger = new Logger(SchedulerService.name);

  constructor(
    private readonly customerService: CustomerService,
    private readonly queueFlowService: QueueFlowService,
    private readonly redisService: RedisService
  ) {
    this.logger.log('Scheduler service initialized');
  }

  /**
   * Update expired queues every 5 minutes
   * This will check all waiting queues and update their status if their time slot has ended:
   * - If the customer checked in, the queue will be marked as "completed"
   * - If the customer did not check in, the queue will be marked as "no-show"
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async updateExpiredQueues() {
    this.logger.log('Running scheduled task: Update expired queues');
    
    try {
      const result = await this.customerService.updateExpiredQueues();
      this.logger.log(`Scheduled task completed: ${result.message}`);
    } catch (error) {
      this.logger.error(`Error in scheduled task: ${error.message}`, error.stack);
    }
  }

  /**
   * Check for expired grace periods every 10 seconds
   * This will find all queues in grace period and mark as no-show if time has expired
   * This is a critical check that ensures no-shows are marked promptly
   */
  @Cron(CronExpression.EVERY_10_SECONDS)
  async checkGracePeriods() {
    this.logger.debug('Scheduler: Checking grace periods');
    
    try {
      await this.queueFlowService.checkGracePeriods();
      this.logger.log('Grace period check completed');
    } catch (error) {
      this.logger.error(`Error checking grace periods: ${error.message}`, error.stack);
    }
  }

  /**
   * Run an immediate check for expired queues
   * This can be called manually if needed
   */
  async runExpiredQueuesCheck() {
    this.logger.log('Running manual check for expired queues');
    return this.customerService.updateExpiredQueues();
  }

  /**
   * Start grace period for a specific queue
   * @param queueId The queue ID to start grace period for
   * @param serviceId The service ID associated with the queue
   */
  async startGracePeriod(queueId: number, serviceId: number) {
    this.logger.log(`Starting grace period for queue ${queueId}`);
    try {
      const queue = await this.queueFlowService.startGracePeriod(queueId, serviceId);
      this.logger.log(`Grace period started for queue ${queueId}`);
      return queue;
    } catch (error) {
      this.logger.error(`Error starting grace period: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Manually trigger the grace period check for a single queueId
   * This should be called when the customer's grace period timer reaches zero
   * to ensure the no-show status is set immediately
   */
  async checkAndMarkExpiredGracePeriod(queueId: number) {
    this.logger.log(`Manually checking grace period expiration for queue ${queueId}`);
    try {
      await this.queueFlowService.checkSingleGracePeriod(queueId);
      return {
        status: 'success',
        message: `Grace period check completed for queue ${queueId}`
      };
    } catch (error) {
      this.logger.error(`Error checking grace period for queue ${queueId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Manually trigger the automatic grace period check
   * This method has been disabled as grace periods should only be started manually from the partner app.
   */
  async triggerAutoGracePeriods() {
    this.logger.log('Auto grace period trigger has been disabled - grace periods are only started manually');
    return {
      status: 'success',
      message: 'Auto grace periods disabled'
    };
  }

  /**
   * Refresh TTL for all serving queues in Redis every hour
   * This prevents serving queues from disappearing due to TTL expiration
   */
  @Cron(CronExpression.EVERY_HOUR)
  async refreshServingQueuesTTL() {
    this.logger.log('Scheduler: Refreshing TTL for all serving queues in Redis');
    
    try {
      await this.redisService.refreshServingQueuesTTL();
      this.logger.log('Serving queues TTL refresh completed');
    } catch (error) {
      this.logger.error(`Error refreshing serving queues TTL: ${error.message}`, error.stack);
    }
  }
}
