"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const customer_module_1 = require("./customer/customer.module");
const user_entity_1 = require("./customer/user.entity");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const auth_module_1 = require("./auth/auth.module");
const partner_module_1 = require("./partner/partner.module");
const dotenv = require("dotenv");
const service_entity_1 = require("./partner/entities/service.entity");
const upload_module_1 = require("./services/upload.module");
const service_setup_entity_1 = require("./partner/entities/service-setup.entity");
const queue_entity_1 = require("./partner/entities/queue.entity");
const redis_module_1 = require("./services/redis/redis.module");
const migration_service_1 = require("./services/migration.service");
const redis_service_1 = require("./services/redis/redis.service");
const common_services_module_1 = require("./services/common-services.module");
dotenv.config();
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
            }),
            typeorm_1.TypeOrmModule.forRoot({
                type: 'postgres',
                url: process.env.DATABASE_URL,
                entities: [user_entity_1.User, service_entity_1.Service, service_setup_entity_1.ServiceSetup, queue_entity_1.Queue],
                synchronize: true,
                ssl: true,
                extra: {
                    ssl: {
                        rejectUnauthorized: false,
                    },
                },
                logging: true,
                autoLoadEntities: true,
                migrations: [__dirname + '/migrations/**/*{.ts,.js}'],
                migrationsRun: true,
            }),
            common_services_module_1.CommonServicesModule,
            redis_module_1.RedisModule,
            auth_module_1.AuthModule,
            customer_module_1.CustomerModule,
            partner_module_1.PartnerModule,
            upload_module_1.UploadModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService, migration_service_1.MigrationService, redis_service_1.RedisService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map