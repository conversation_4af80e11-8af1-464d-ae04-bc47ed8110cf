"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueueFlowModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const queue_flow_service_1 = require("./queue-flow.service");
const queue_entity_1 = require("../../partner/entities/queue.entity");
const service_setup_entity_1 = require("../../partner/entities/service-setup.entity");
const redis_module_1 = require("../redis/redis.module");
const schedule_1 = require("@nestjs/schedule");
let QueueFlowModule = class QueueFlowModule {
};
exports.QueueFlowModule = QueueFlowModule;
exports.QueueFlowModule = QueueFlowModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([queue_entity_1.Queue, service_setup_entity_1.ServiceSetup]),
            redis_module_1.RedisModule,
            schedule_1.ScheduleModule.forRoot(),
        ],
        providers: [queue_flow_service_1.QueueFlowService],
        exports: [queue_flow_service_1.QueueFlowService],
    })
], QueueFlowModule);
//# sourceMappingURL=queue-flow.module.js.map