import React, { useState } from 'react';
import { View, TextInput, Image, ImageSourcePropType, TouchableOpacity } from 'react-native';
import { images } from "@/constants";

type Props = {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  icon?: ImageSourcePropType;
  className?: string;
};

export default function SearchButton({ 
  value,
  onChangeText,
  placeholder = "Search services...",
  icon,
  className = ""
}: Props) {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <View className={`bg-white rounded-2xl px-5  border-2 flex-row items-center h-[60px] ${
      isFocused ? ' border-primary-500' : ' border-gray-200'
    } ${className}`}>
      {icon && (
        <Image 
          source={icon} 
          className="w-6 h-6 mr-3"
          resizeMode="contain"
          tintColor={isFocused ? "#159AFF" : "#666"}
        />
      )}
      <TextInput
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        className={`flex-1 font-poppins-regular text-[15px] ${
          isFocused ? 'text-secondary-500' : 'text-secondary-600'
        }`}
        placeholderTextColor={"#9CA3AF"}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)} 
      />
      {value.length > 0 && (
        <TouchableOpacity
          onPress={() => onChangeText('')}
          className="p-2"
        >
          <Image
            source={images.close}
            className="w-6 h-6"
            tintColor="#666"
          />
        </TouchableOpacity>
      )}
    </View>
  );
}
