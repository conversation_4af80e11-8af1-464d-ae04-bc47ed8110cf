"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddBankDetailsAndLeaveDays************* = void 0;
class AddBankDetailsAndLeaveDays************* {
    async up(queryRunner) {
        await queryRunner.query(`
            CREATE TABLE "bank_details" (
                "id" SERIAL PRIMARY KEY,
                "serviceId" integer UNIQUE REFERENCES services(id) ON DELETE CASCADE,
                "accountNumber" character varying NOT NULL,
                "accountHolderName" character varying NOT NULL,
                "ifscCode" character varying NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now()
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "service_setups" 
            ADD COLUMN "leaveDays" jsonb DEFAULT '[]'::jsonb
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "bank_details"`);
        await queryRunner.query(`ALTER TABLE "service_setups" DROP COLUMN "leaveDays"`);
    }
}
exports.AddBankDetailsAndLeaveDays************* = AddBankDetailsAndLeaveDays*************;
//# sourceMappingURL=*************-AddBankDetailsAndLeaveDays.js.map