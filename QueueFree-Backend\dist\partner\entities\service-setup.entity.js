"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceSetup = void 0;
const typeorm_1 = require("typeorm");
const service_entity_1 = require("./service.entity");
let ServiceSetup = class ServiceSetup {
};
exports.ServiceSetup = ServiceSetup;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], ServiceSetup.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => service_entity_1.Service),
    (0, typeorm_1.JoinColumn)(),
    __metadata("design:type", service_entity_1.Service)
], ServiceSetup.prototype, "service", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb'),
    __metadata("design:type", Object)
], ServiceSetup.prototype, "setupData", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', {
        default: '{"main": [], "subUnits": {}}',
        comment: 'Format: { main: string[], subUnits: { "subunit-0": string[], "subunit-1": string[], ... } }'
    }),
    __metadata("design:type", Object)
], ServiceSetup.prototype, "leaveDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', default: 120 }),
    __metadata("design:type", Number)
], ServiceSetup.prototype, "graceTime", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ServiceSetup.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ServiceSetup.prototype, "updatedAt", void 0);
exports.ServiceSetup = ServiceSetup = __decorate([
    (0, typeorm_1.Entity)('service_setups')
], ServiceSetup);
//# sourceMappingURL=service-setup.entity.js.map