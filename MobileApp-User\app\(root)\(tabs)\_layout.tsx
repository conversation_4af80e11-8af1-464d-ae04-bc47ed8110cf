import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TouchableWithoutFeedback,
  Image,
  Platform,
  ImageBackground,
  ViewStyle,
  GestureResponderEvent,
  Keyboard,
} from "react-native";
import { Tabs } from "expo-router";
import { images } from "@/constants";
import TabPressAnimation from "@/components/TabPressAnimation";
import { useRouter } from "expo-router";
import CreateButtonAnimation from "@/components/CreateButtonAnimation";

export default function TabsLayout() {
  const [pressedTab, setPressedTab] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    // Ensure keyboard is dismissed when tab layout mounts
    const cleanup = () => {
      Keyboard.dismiss();
    };

    cleanup();
    return cleanup;
  }, []);

  const lineIndicatorStyle = (focused: boolean): ViewStyle => ({
    position: "absolute",
    top: -25.5,
    width: 30,
    height: 3.5,
    backgroundColor: focused ? "#159AFF" : "transparent",
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  });

  const shadowCircleStyle = (focused: boolean): ViewStyle => ({
    position: "absolute",
    top: -12, // Position it below the line
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: "transparent",
    ...(Platform.OS === "ios"
      ? {
          shadowColor: "#77C4FF",
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: focused ? 0.5 : 0,
          shadowRadius: 6,
        }
      : {
          elevation: focused ? 6 : 0,
          shadowColor: "#77C4FF",
        }),
  });

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarButton: (props: {
          children: React.ReactNode;
          onPress?: (e: GestureResponderEvent) => void;
          style?: any;
          name?: string;
        }) => (
          <TouchableWithoutFeedback
            onPress={(e) => {
              setPressedTab(props.name || null);
              props.onPress?.(e);
              // Reset pressedTab after animation duration
              setTimeout(() => {
                setPressedTab(null);
              }, 300);
            }}
          >
            <View
              style={{
                flex: 1,
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              {props.children}
              {pressedTab === props.name && <TabPressAnimation />}
            </View>
          </TouchableWithoutFeedback>
        ),
        tabBarStyle: {
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          paddingVertical: 10,
          paddingHorizontal: 20,
          height: 90,
          backgroundColor: "#FFFFFF",
          borderTopWidth: 1,
          borderTopColor: "#DFF1FA",
          display: Platform.OS === "ios" ? "flex" : undefined,
          elevation: 8,
          shadowColor: "rgba(182, 223, 253, 0.3)",
          overflow: "hidden",
          shadowOffset: {
            width: 0,
            height: -4,
          },
          shadowOpacity: 1,
          shadowRadius: 30,
        },
        tabBarIconStyle: {
          marginTop: 10,
          marginBottom: 4,
        },
        tabBarLabelStyle: {
          marginBottom: 4,
        },
        tabBarActiveTintColor: "#159AFF",
        tabBarInactiveTintColor: "#455A64",
        tabBarActiveBackgroundColor: "transparent",
        tabBarInactiveBackgroundColor: "transparent",
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          tabBarIcon: ({ focused }) => (
            <View style={{ alignItems: "center" }}>
              <View style={lineIndicatorStyle(focused)} />
              <View style={shadowCircleStyle(focused)} className="-mt-8" />
              <CreateButtonAnimation animate={pressedTab === "home"}>
                <Image
                  source={images.home}
                  style={{
                    width: 24,
                    height: 24,
                    marginBottom: 3,
                    tintColor: focused ? "#159AFF" : "#455A64",
                  }}
                />
              </CreateButtonAnimation>
            </View>
          ),
          tabBarLabel: "Home",
          tabBarButton: (props) => (
            <TouchableWithoutFeedback
              onPress={(e) => {
                setPressedTab("home");
                props.onPress?.(e);
                setTimeout(() => {
                  setPressedTab(null);
                }, 300);
              }}
            >
              <View
                style={{
                  flex: 1,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {props.children}
                {pressedTab === "home" && <TabPressAnimation />}
              </View>
            </TouchableWithoutFeedback>
          ),
        }}
      />

      <Tabs.Screen
        name="queues"
        options={{
          tabBarIcon: ({ focused }) => (
            <View style={{ alignItems: "center" }}>
              <View style={lineIndicatorStyle(focused)} />
              <View style={shadowCircleStyle(focused)} className="-mt-8" />
              <CreateButtonAnimation animate={pressedTab === "queues"}>
                <Image
                  source={images.queues}
                  style={{
                    width: 22.5,
                    height: 24,
                    marginBottom: 3,
                    tintColor: focused ? "#159AFF" : "#455A64",
                  }}
                />
              </CreateButtonAnimation>
            </View>
          ),
          tabBarLabel: "Queues",
          tabBarButton: (props) => (
            <TouchableWithoutFeedback
              onPress={(e) => {
                setPressedTab("queues");
                props.onPress?.(e);
                setTimeout(() => {
                  setPressedTab(null);
                }, 300);
              }}
            >
              <View
                style={{
                  flex: 1,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {props.children}
                {pressedTab === "queues" && <TabPressAnimation />}
              </View>
            </TouchableWithoutFeedback>
          ),
        }}
      />

      <Tabs.Screen
        name="create"
        options={{
          tabBarIcon: () => (
            <View style={{ marginTop: -15 }}>
              <CreateButtonAnimation animate={pressedTab === "create"}>
                <Image
                  source={images.create}
                  style={{
                    width: 44,
                    height: 45,
                  }}
                />
              </CreateButtonAnimation>
            </View>
          ),
          tabBarLabel: () => null,
          tabBarButton: (props) => (
            <TouchableWithoutFeedback
              onPress={() => {
                setPressedTab("create");
                router.push("/add");
                setTimeout(() => {
                  setPressedTab(null);
                }, 300);
              }}
            >
              <View
                style={{
                  flex: 1,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {props.children}
              </View>
            </TouchableWithoutFeedback>
          ),
        }}
      />

      <Tabs.Screen
        name="wishlist"
        options={{
          tabBarIcon: ({ focused }) => (
            <View style={{ alignItems: "center" }}>
              <View style={lineIndicatorStyle(focused)} />
              <View style={shadowCircleStyle(focused)} className="-mt-8" />
              <CreateButtonAnimation animate={pressedTab === "wishlist"}>
                <Image
                  source={images.wishlist}
                  style={{
                    width: 24,
                    height: 21,
                    padding: 5,
                    marginBottom: 3,
                    marginTop: 3,
                    tintColor: focused ? "#159AFF" : "#455A64",
                  }}
                />
              </CreateButtonAnimation>
            </View>
          ),
          tabBarLabel: "Wishlist",
          tabBarButton: (props) => (
            <TouchableWithoutFeedback
              onPress={(e) => {
                setPressedTab("wishlist");
                props.onPress?.(e);
                setTimeout(() => {
                  setPressedTab(null);
                }, 300);
              }}
            >
              <View
                style={{
                  flex: 1,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {props.children}
                {pressedTab === "wishlist" && <TabPressAnimation />}
              </View>
            </TouchableWithoutFeedback>
          ),
        }}
      />

      <Tabs.Screen
        name="profile"
        options={{
          tabBarIcon: ({ focused }) => (
            <View style={{ alignItems: "center" }}>
              <View style={lineIndicatorStyle(focused)} />
              <View style={shadowCircleStyle(focused)} className="-mt-8" />
              <CreateButtonAnimation animate={pressedTab === "profile"}>
                <Image
                  source={images.profile}
                  style={{
                    width: 24,
                    height: 24,
                    marginBottom: 3,
                    tintColor: focused ? "#159AFF" : "#455A64",
                  }}
                />
              </CreateButtonAnimation>
            </View>
          ),
          tabBarLabel: "Profile",
          tabBarButton: (props) => (
            <TouchableWithoutFeedback
              onPress={(e) => {
                setPressedTab("profile");
                props.onPress?.(e);
                setTimeout(() => {
                  setPressedTab(null);
                }, 300);
              }}
            >
              <View
                style={{
                  flex: 1,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {props.children}
                {pressedTab === "profile" && <TabPressAnimation />}
              </View>
            </TouchableWithoutFeedback>
          ),
        }}
      />
    </Tabs>
  );
}
