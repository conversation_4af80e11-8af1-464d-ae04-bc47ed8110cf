"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddEstimatedServeTimeToQueue1720000000000 = void 0;
class AddEstimatedServeTimeToQueue1720000000000 {
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE queues ADD COLUMN "estimatedServeTime" TIMESTAMP WITH TIME ZONE NULL`);
        await queryRunner.query(`ALTER TABLE queues ADD COLUMN "waitTimeStatus" VARCHAR(255) DEFAULT 'on-time'`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE queues DROP COLUMN "estimatedServeTime"`);
        await queryRunner.query(`ALTER TABLE queues DROP COLUMN "waitTimeStatus"`);
    }
}
exports.AddEstimatedServeTimeToQueue1720000000000 = AddEstimatedServeTimeToQueue1720000000000;
//# sourceMappingURL=1720000000000-AddEstimatedServeTimeToQueue.js.map