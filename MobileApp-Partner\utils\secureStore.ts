import * as SecureStore from 'expo-secure-store';

const keysToRemove = [
  'serviceStatus',
  'serviceSetupData',
  'serviceFormData',
  'serviceAddress',
  'selectedLocation',
  'serviceImages',
  'documentData',
  'panCardImage',
  'serviceId',
  'setupComplete'
];

export const clearAllSecureStoreData = async () => {
  try {
    const promises = keysToRemove.map(key => SecureStore.deleteItemAsync(key));
    await Promise.all(promises);
    console.log('Successfully cleared all secure store data');
  } catch (error) {
    console.error('Error clearing secure store data:', error);
  }
};
