import { Repository } from 'typeorm';
import { Service } from './entities/service.entity';
import { ServiceSetup } from './entities/service-setup.entity';
import { BankDetails } from './entities/bank-details.entity';
import { Queue } from './entities/queue.entity';
import { UploadService } from '../services/upload.service';
import { Review } from './entities/review.entity';
import { RedisService } from '../services/redis/redis.service';
import { QueueFlowService } from '../services/queue-flow/queue-flow.service';
export declare class PartnerService {
    private readonly serviceRepository;
    private readonly setupRepository;
    private readonly bankDetailsRepository;
    private readonly queueRepository;
    private readonly reviewRepository;
    private readonly uploadService;
    private readonly redisService;
    private readonly queueFlowService;
    constructor(serviceRepository: Repository<Service>, setupRepository: Repository<ServiceSetup>, bankDetailsRepository: Repository<BankDetails>, queueRepository: Repository<Queue>, reviewRepository: Repository<Review>, uploadService: UploadService, redisService: RedisService, queueFlowService: QueueFlowService);
    createPartner(email: string): Promise<{
        status: string;
        message: string;
        isExisting: boolean;
        serviceId: number;
    }>;
    registerService(serviceData: any): Promise<{
        serviceId: number;
        status: string;
    }>;
    updateService(serviceData: any): Promise<{
        status: string;
        message: string;
        service: Service;
    }>;
    getServiceStatus(serviceId: number): Promise<{
        success: boolean;
        status: string;
    }>;
    updateServiceStatus(serviceId: number, status: 'pending' | 'success' | 'failed'): Promise<{
        status: string;
        message: string;
    }>;
    saveServiceSetup(serviceId: number, data: any): Promise<{
        status: string;
        message: string;
        data: {
            selectedDays: string[];
            availableHours?: {
                [day: string]: Array<{
                    start: string;
                    end: string;
                }>;
            };
            timeSlots?: Array<{
                start: string;
                end: string;
            }>;
            servingTime: string;
            basePrice: string;
            useDayWiseTimeSlots?: boolean;
            timeSlotsByDay?: {
                [day: string]: Array<{
                    start: string;
                    end: string;
                }>;
            };
            hasSubUnits?: boolean;
            subUnits?: Array<{
                name: string;
                availableHours: {
                    [day: string]: Array<{
                        start: string;
                        end: string;
                    }>;
                };
                dayWiseAvailableHours?: {
                    [day: string]: Array<{
                        start: string;
                        end: string;
                    }>;
                };
                avgServeTime: string;
                pricePerHead: string;
                selectedDays?: string[];
                useDayWiseTimeSlots?: boolean;
                timeSlots?: Array<{
                    start: string;
                    end: string;
                }>;
            }>;
        };
        termsAccepted: boolean;
        hasSetup: boolean;
    }>;
    private validateSetupData;
    getServiceSetup(serviceId: number): Promise<{
        status: string;
        message: string;
        hasSetup: boolean;
        data: null;
    } | {
        status: string;
        message: string;
        hasSetup: boolean;
        data: {
            selectedDays: string[];
            availableHours?: {
                [day: string]: Array<{
                    start: string;
                    end: string;
                }>;
            };
            timeSlots?: Array<{
                start: string;
                end: string;
            }>;
            servingTime: string;
            basePrice: string;
            useDayWiseTimeSlots?: boolean;
            timeSlotsByDay?: {
                [day: string]: Array<{
                    start: string;
                    end: string;
                }>;
            };
            hasSubUnits?: boolean;
            subUnits?: Array<{
                name: string;
                availableHours: {
                    [day: string]: Array<{
                        start: string;
                        end: string;
                    }>;
                };
                dayWiseAvailableHours?: {
                    [day: string]: Array<{
                        start: string;
                        end: string;
                    }>;
                };
                avgServeTime: string;
                pricePerHead: string;
                selectedDays?: string[];
                useDayWiseTimeSlots?: boolean;
                timeSlots?: Array<{
                    start: string;
                    end: string;
                }>;
            }>;
        };
    }>;
    getServiceDetailsByEmail(email: string): Promise<Service | {
        id: null;
        serviceName: string;
        termsAccepted: boolean;
        serviceType: string;
        businessPhone: string;
        serviceDescription: string;
        address: null;
        images: never[];
        verificationStatus: string;
        documents: null;
    }>;
    checkServiceCompletion(email: string): Promise<{
        exists: boolean;
        isVerified: boolean;
        isDetailsComplete: boolean;
        hasSetup: boolean;
        verificationStatus: null;
        termsAccepted: boolean;
        serviceId: null;
        setupComplete: boolean;
        serviceDetails?: undefined;
    } | {
        exists: boolean;
        isVerified: boolean;
        isDetailsComplete: boolean;
        hasSetup: boolean;
        verificationStatus: string;
        termsAccepted: boolean;
        serviceId: number;
        setupComplete: boolean;
        serviceDetails: {
            serviceName: string;
            serviceType: string;
            businessPhone: string;
            serviceDescription: string;
            address: {
                details: {
                    buildingNo: string;
                    locality: string;
                    city: string;
                    state: string;
                    pincode: string;
                };
                coordinates: {
                    latitude: number;
                    longitude: number;
                };
                area?: string;
                fullAddress?: string;
                googleMapsLink?: string;
            };
            images: string[];
            documents: {
                panNumber: string;
                gstin?: string;
                panCardImage: string;
            };
        };
    }>;
    acceptTerms(email: string): Promise<{
        status: string;
        message: string;
    }>;
    getVerificationStatusByEmail(email: string): Promise<Service>;
    toggleServiceStatus(email: string, isOpen: boolean): Promise<{
        status: string;
        message: string;
        isOpen: boolean;
    }>;
    getServiceOpenStatus(email: string): Promise<Service>;
    saveBankDetails(serviceId: number, data: any): Promise<{
        status: string;
        message: string;
    }>;
    getBankDetails(serviceId: number): Promise<{
        status: string;
        data: BankDetails;
    }>;
    saveLeaveDays(serviceId: number, leaveDays: any): Promise<{
        status: string;
        message: string;
    }>;
    getLeaveDays(serviceId: number): Promise<{
        status: string;
        data: {
            subUnits: {
                [subUnitId: string]: string[];
            };
        };
        hasSubUnits: boolean;
    } | {
        status: string;
        data: string[];
        hasSubUnits: boolean;
    }>;
    getAllServices(): Promise<{
        _id: string;
        serviceName: string;
        serviceType: string;
        address: {
            details: {
                buildingNo: string;
                locality: string;
                city: string;
                state: string;
                pincode: string;
            };
            coordinates: {
                latitude: number;
                longitude: number;
            };
            area?: string;
            fullAddress?: string;
            googleMapsLink?: string;
        };
        image: string | null;
        isOpen: boolean;
        rating: number;
        reviewCount: number;
        serviceDescription: string;
        queueInfo: {
            waitingTime: number;
            membersInQueue: number;
            vipCount: number;
            normalCount: number;
            cost: number;
            servingTime: number;
        };
    }[]>;
    getServiceById(id: number): Promise<{
        _id: string;
        serviceName: string;
        serviceType: string;
        address: {
            details: {
                buildingNo: string;
                locality: string;
                city: string;
                state: string;
                pincode: string;
            };
            coordinates: {
                latitude: number;
                longitude: number;
            };
            area?: string;
            fullAddress?: string;
            googleMapsLink?: string;
        };
        images: string[];
        isOpen: boolean;
        rating: number;
        reviewCount: number;
        reviews: Review[];
        serviceDescription: string;
        businessPhone: string;
        email: string;
        workingHours: {
            startTime: string;
            endTime: string;
        } | null;
        selectedDays: string[];
        queueInfo: {
            waitingTime: number;
            membersInQueue: number;
            vipCount: number;
            normalCount: number;
            cost: number;
            servingTime: number;
        };
    }>;
    getServiceReviews(serviceId: number, limit?: number, offset?: number): Promise<{
        reviews: Review[];
        totalCount: number;
        starDistribution: {
            1: number;
            2: number;
            3: number;
            4: number;
            5: number;
        };
    }>;
    addReview(serviceId: number, reviewData: {
        userId: string;
        userName: string;
        userProfilePic?: string;
        rating: number;
        comment: string;
    }): Promise<Review>;
    getServiceTimeSlots(serviceId: number, dateString: string, subUnitId?: number): Promise<{
        status: string;
        message: string;
        timeSlots: never[];
        workingDay?: undefined;
        isLegacy?: undefined;
    } | {
        status: string;
        workingDay: boolean;
        timeSlots: {
            timeSlot: string;
            normalQueueCount: number;
            vipQueueCount: number;
        }[];
        message?: undefined;
        isLegacy?: undefined;
    } | {
        status: string;
        workingDay: boolean;
        timeSlots: {
            timeSlot: string;
            normalQueueCount: number;
            vipQueueCount: number;
        }[];
        isLegacy: boolean;
        message?: undefined;
    }>;
    private mapTimeSlotsWithQueues;
    updateQueueStatus(queueId: number, status: string, forceUpdate?: boolean): Promise<Queue>;
    private fetchUserDetails;
    startGracePeriod(queueId: number): Promise<any>;
    getGracePeriodStatus(queueId: number): Promise<any>;
}
