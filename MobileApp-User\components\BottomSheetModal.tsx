import React, { useCallback, useRef, useMemo } from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import {
  BottomSheetModal as GorhomBottomSheet,
  BottomSheetModalProvider,
  BottomSheetBackdrop,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';

interface BottomSheetModalProps {
  visible: boolean;
  onClose: () => void;
  headerText: string;
  items: {
    id: string;
    label: string;
    icon?: any;
    iconClassName?: string;
  }[];
  onSelectItem: (item: string) => void;
  defaultHeight?: number; // percentage (0-100)
  expandedHeight?: number; // percentage (0-100)
  showBackdrop?: boolean;
  backdropOpacity?: number;
  selectedItem?: string;
}

const BottomSheetModal: React.FC<BottomSheetModalProps> = ({
  visible,
  onClose,
  headerText,
  items,
  onSelectItem,
  defaultHeight = 50,
  expandedHeight = 90,
  showBackdrop = true,
  backdropOpacity = 0.5,
  selectedItem,
}) => {
  const bottomSheetRef = useRef<GorhomBottomSheet>(null);

  // Convert height percentages to snap points
  const snapPoints = useMemo(() => {
    return [`${defaultHeight}%`, `${expandedHeight}%`];
  }, [defaultHeight, expandedHeight]);

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={backdropOpacity}
        pressBehavior={showBackdrop ? "close" : "none"}
      />
    ),
    [showBackdrop, backdropOpacity]
  );

  // Handle sheet changes
  React.useEffect(() => {
    if (visible) {
      bottomSheetRef.current?.present();
    } else {
      bottomSheetRef.current?.dismiss();
    }
  }, [visible]);

  const handleSheetChanges = useCallback((index: number) => {
    if (index === -1) {
      onClose();
    }
  }, [onClose]);

  return (
    <BottomSheetModalProvider>
      <GorhomBottomSheet
        ref={bottomSheetRef}
        index={visible ? 0 : -1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        handleIndicatorStyle={{
          width: 128,
          height: 4,
          backgroundColor: '#E5E7EB',
          marginTop: 8,
        }}
      >
        <View className="flex-1 z-100">
          <View className="p-6 border-b border-gray-200">
            <Text className="text-xl font-poppins-medium text-center">
              {headerText}
            </Text>
          </View>
          <BottomSheetScrollView 
            showsVerticalScrollIndicator={true}
            contentContainerStyle={{flexGrow: 1}}
          >
            {items.map((item) => (
              <TouchableOpacity
                key={item.id}
                className={`p-6 border-b border-gray-100 flex-row items-center ${
                  selectedItem === item.id ? 'bg-primary-50' : ''
                }`}
                onPress={() => {
                  onSelectItem(item.id);
                  bottomSheetRef.current?.dismiss();
                }}
              >
                {item.icon && (
                  <Image 
                    source={item.icon} 
                    className={`mr-3 ${item.iconClassName || 'w-8 h-8'}`}
                  />
                )}
                <Text className={`font-poppins-regular flex-1 ${
                  selectedItem === item.id ? 'text-primary-500' : 'text-secondary-600'
                }`}>
                  {item.label}
                </Text>
                <View className={`w-6 h-6 rounded-full border-4 ${
                  selectedItem === item.id ? 'border-primary-500' : 'border-gray-300'
                }`}>
                  
                </View>
              </TouchableOpacity>
            ))}
          </BottomSheetScrollView>
        </View>
      </GorhomBottomSheet>
    </BottomSheetModalProvider>
  );
};

export default BottomSheetModal;