import { View, Text, SafeAreaView, Image, TouchableOpacity, FlatList } from 'react-native';
import React from 'react';
import { router } from 'expo-router';
import { images } from '@/constants';

const Notifications = () => {
  const mockNotifications = [
    {
      id: '1',
      title: 'Your turn is coming up!',
      message: 'You are 5 minutes away from your turn at Restaurant',
      time: '2 min ago',
    },
    // Add more mock notifications as needed
  ];

  return (
    <SafeAreaView className="flex-1 bg-white">
      <View className="flex-1 px-8 pt-12 items-center">
                <View className="w-full flex-row  mt-4">
                  <TouchableOpacity
                    className="flex-row items-center absolute left-0 -top-2 border border-secondary-600 w-12 h-12 rounded-full justify-center"
                    onPress={() =>router.push("/(root)/(tabs)/home")}
                  >
                    <Image source={images.back} className="w-4 h-4" />
                  </TouchableOpacity>
                  <View className="flex-1 items-center ">
                    <Text className="font-poppins-medium text-2xl mb-2">
                      Notifications
                      </Text>
                    
                  </View>
                </View>
                

        {/* Notifications List */}
        <FlatList
          className="mt-6"
          data={mockNotifications}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <View className="p-4 border-b border-gray-100">
              <Text className="font-poppins-medium text-base">{item.title}</Text>
              <Text className="font-poppins-regular text-gray-600 mt-1">
                {item.message}
              </Text>
              <Text className="font-poppins-regular text-gray-400 text-sm mt-2">
                {item.time}
              </Text>
            </View>
          )}
        />
      </View>
    </SafeAreaView>
  );
};
export default Notifications
  