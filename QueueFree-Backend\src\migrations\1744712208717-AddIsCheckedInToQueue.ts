import { MigrationInterface, QueryRunner } from "typeorm";

export class AddIsCheckedInToQueue1744712208717 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE queues ADD COLUMN is_checked_in BOOLEAN NOT NULL DEFAULT false`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE queues DROP COLUMN is_checked_in`
        );
    }

}
