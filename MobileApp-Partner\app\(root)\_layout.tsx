import React from "react";
import { View, Text } from "react-native";
import { Stack } from "expo-router";

const RootLayout = () => {
  return (
    <View>
      <Text>Root Layout</Text>
    </View>
  );
};

const Layout = () => {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
      <Stack.Screen name="edit-service" options={{ headerShown: false }} />
    </Stack>
  );
};

export default Layout;
