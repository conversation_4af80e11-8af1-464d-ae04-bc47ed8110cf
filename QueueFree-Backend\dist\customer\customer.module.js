"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const customer_controller_1 = require("./customer.controller");
const customer_service_1 = require("./customer.service");
const user_entity_1 = require("./user.entity");
const queue_entity_1 = require("../partner/entities/queue.entity");
const service_entity_1 = require("../partner/entities/service.entity");
const service_setup_entity_1 = require("../partner/entities/service-setup.entity");
const redis_module_1 = require("../services/redis/redis.module");
const queue_flow_module_1 = require("../services/queue-flow/queue-flow.module");
let CustomerModule = class CustomerModule {
};
exports.CustomerModule = CustomerModule;
exports.CustomerModule = CustomerModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([user_entity_1.User, queue_entity_1.Queue, service_entity_1.Service, service_setup_entity_1.ServiceSetup]),
            redis_module_1.RedisModule,
            queue_flow_module_1.QueueFlowModule,
        ],
        controllers: [customer_controller_1.CustomerController],
        providers: [customer_service_1.CustomerService],
        exports: [customer_service_1.CustomerService],
    })
], CustomerModule);
//# sourceMappingURL=customer.module.js.map