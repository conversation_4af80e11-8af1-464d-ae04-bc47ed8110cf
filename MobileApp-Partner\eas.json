{"cli": {"version": ">= 16.3.2", "appVersionSource": "local"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug", "image": "ubuntu-22.04-jdk-17-ndk-r21e"}, "env": {"npm_install_cmd": "npm install --legacy-peer-deps"}, "channel": "development"}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease", "image": "ubuntu-22.04-jdk-17-ndk-r21e"}, "env": {"npm_install_cmd": "npm install --legacy-peer-deps"}, "channel": "preview"}, "production": {"android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease", "image": "ubuntu-22.04-jdk-17-ndk-r21e"}, "env": {"npm_install_cmd": "npm install --legacy-peer-deps"}, "channel": "production"}}, "submit": {"production": {}}}