"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SchedulerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchedulerService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const customer_service_1 = require("../../customer/customer.service");
const queue_flow_service_1 = require("../queue-flow/queue-flow.service");
const redis_service_1 = require("../redis/redis.service");
let SchedulerService = SchedulerService_1 = class SchedulerService {
    constructor(customerService, queueFlowService, redisService) {
        this.customerService = customerService;
        this.queueFlowService = queueFlowService;
        this.redisService = redisService;
        this.logger = new common_1.Logger(SchedulerService_1.name);
        this.logger.log('Scheduler service initialized');
    }
    async updateExpiredQueues() {
        this.logger.log('Running scheduled task: Update expired queues');
        try {
            const result = await this.customerService.updateExpiredQueues();
            this.logger.log(`Scheduled task completed: ${result.message}`);
        }
        catch (error) {
            this.logger.error(`Error in scheduled task: ${error.message}`, error.stack);
        }
    }
    async checkGracePeriods() {
        this.logger.debug('Scheduler: Checking grace periods');
        try {
            await this.queueFlowService.checkGracePeriods();
            this.logger.log('Grace period check completed');
        }
        catch (error) {
            this.logger.error(`Error checking grace periods: ${error.message}`, error.stack);
        }
    }
    async runExpiredQueuesCheck() {
        this.logger.log('Running manual check for expired queues');
        return this.customerService.updateExpiredQueues();
    }
    async startGracePeriod(queueId, serviceId) {
        this.logger.log(`Starting grace period for queue ${queueId}`);
        try {
            const queue = await this.queueFlowService.startGracePeriod(queueId, serviceId);
            this.logger.log(`Grace period started for queue ${queueId}`);
            return queue;
        }
        catch (error) {
            this.logger.error(`Error starting grace period: ${error.message}`, error.stack);
            throw error;
        }
    }
    async checkAndMarkExpiredGracePeriod(queueId) {
        this.logger.log(`Manually checking grace period expiration for queue ${queueId}`);
        try {
            await this.queueFlowService.checkSingleGracePeriod(queueId);
            return {
                status: 'success',
                message: `Grace period check completed for queue ${queueId}`
            };
        }
        catch (error) {
            this.logger.error(`Error checking grace period for queue ${queueId}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async triggerAutoGracePeriods() {
        this.logger.log('Auto grace period trigger has been disabled - grace periods are only started manually');
        return {
            status: 'success',
            message: 'Auto grace periods disabled'
        };
    }
    async refreshServingQueuesTTL() {
        this.logger.log('Scheduler: Refreshing TTL for all serving queues in Redis');
        try {
            await this.redisService.refreshServingQueuesTTL();
            this.logger.log('Serving queues TTL refresh completed');
        }
        catch (error) {
            this.logger.error(`Error refreshing serving queues TTL: ${error.message}`, error.stack);
        }
    }
};
exports.SchedulerService = SchedulerService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_5_MINUTES),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SchedulerService.prototype, "updateExpiredQueues", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_10_SECONDS),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SchedulerService.prototype, "checkGracePeriods", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_HOUR),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SchedulerService.prototype, "refreshServingQueuesTTL", null);
exports.SchedulerService = SchedulerService = SchedulerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [customer_service_1.CustomerService,
        queue_flow_service_1.QueueFlowService,
        redis_service_1.RedisService])
], SchedulerService);
//# sourceMappingURL=scheduler.service.js.map