import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TouchableWithoutFeedback,
  Image,
  Platform,
  ImageBackground,
  ViewStyle,
  GestureResponderEvent,
  Keyboard,
} from "react-native";
import { Tabs } from "expo-router";
import { images } from "@/constants";
import TabPressAnimation from "@/components/TabPressAnimation";
import CreateButtonAnimation from "@/components/CreateButtonAnimation";

export default function TabsLayout() {
  const [pressedTab, setPressedTab] = useState<string | null>(null);

  useEffect(() => {
    // Ensure keyboard is dismissed when tab layout mounts
    const cleanup = () => {
      Keyboard.dismiss();
    };

    cleanup();
    return cleanup;
  }, []);

  const lineIndicatorStyle = (focused: boolean): ViewStyle => ({
    position: "absolute",
    top: -25.5,
    width: 30,
    height: 3.5,
    backgroundColor: focused ? "#159AFF" : "transparent",
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  });

  const shadowCircleStyle = (focused: boolean): ViewStyle => ({
    position: "absolute",
    top: -12, // Position it below the line
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: "transparent",
    ...(Platform.OS === "ios"
      ? {
          shadowColor: "#77C4FF",
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: focused ? 0.5 : 0,
          shadowRadius: 6,
        }
      : {
          elevation: focused ? 6 : 0,
          shadowColor: "#77C4FF",
        }),
  });

  return (
    <Tabs
      initialRouteName="dashboard"
      screenOptions={{
        headerShown: false,
        tabBarButton: (props: {
          children: React.ReactNode;
          onPress?: (e: GestureResponderEvent) => void;
          style?: any;
          name?: string;
        }) => (
          <TouchableWithoutFeedback 
            onPress={(e) => {
              setPressedTab(props.name || null);
              props.onPress?.(e);
              // Reset pressedTab after animation duration
              setTimeout(() => {
                setPressedTab(null);
              }, 300);
            }}
          >
            <View
              style={{
                flex: 1,
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              {props.children}
              {pressedTab === props.name && <TabPressAnimation />}
            </View>
          </TouchableWithoutFeedback>
        ),
        tabBarStyle: {
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          paddingVertical: 10,
          paddingHorizontal: 20,
          height: 90,
          backgroundColor: '#FFFFFF',
          borderTopWidth: 1,
          borderTopColor: '#DFF1FA',
          display: Platform.OS === 'ios' ? 'flex' : undefined,
          elevation: 8,
          shadowColor: 'rgba(182, 223, 253, 0.3)',
          overflow: "hidden",
          shadowOffset: {
            width: 0,
            height: -4,
          },
          shadowOpacity: 1,
          shadowRadius: 30,
        },
        tabBarIconStyle: {
          marginTop: 10,
          marginBottom: 4,
        },
        tabBarLabelStyle: {
          marginBottom: 4,
        },
        tabBarActiveTintColor: "#159AFF",
        tabBarInactiveTintColor: "#455A64",
        tabBarActiveBackgroundColor: "transparent",
        tabBarInactiveBackgroundColor: "transparent",
      }}
    >
      <Tabs.Screen
        name="dashboard"
        options={{
          tabBarIcon: ({ focused }) => (
            <View style={{ alignItems: "center" }}>
              <View style={lineIndicatorStyle(focused)} />
              <View style={shadowCircleStyle(focused)} className="-mt-8" />
              <CreateButtonAnimation animate={pressedTab === "dashboard"}>
                <Image
                  source={images.dashboard}
                  style={{
                    width: 24,
                    height: 24,
                    marginBottom: 3,
                    tintColor: focused ? "#159AFF" : "#455A64",
                  }}
                />
              </CreateButtonAnimation>
            </View>
          ),
          tabBarLabel: "Dashboard",
          tabBarButton: (props) => (
            <TouchableWithoutFeedback 
              onPress={(e) => {
                setPressedTab("dashboard");
                props.onPress?.(e);
                setTimeout(() => {
                  setPressedTab(null);
                }, 300);
              }}
            >
              <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
                {props.children}
                {pressedTab === "dashboard" && <TabPressAnimation />}
              </View>
            </TouchableWithoutFeedback>
          ),
        }}
      />
      <Tabs.Screen
        name="insights"
        options={{
          tabBarIcon: ({ focused }) => (
            <View style={{ alignItems: "center" }}>
              <View style={lineIndicatorStyle(focused)} />
              <View style={shadowCircleStyle(focused)} className="-mt-8" />
              <CreateButtonAnimation animate={pressedTab === "insights"}>
                <Image
                  source={images.insights}
                  style={{
                    width: 24,
                    height: 24,
                    padding: 5,
                    marginBottom: 3,
                    tintColor: focused ? "#159AFF" : "#455A64",
                  }}
                />
              </CreateButtonAnimation>
            </View>
          ),
          tabBarLabel: "Insights",
          tabBarButton: (props) => (
            <TouchableWithoutFeedback 
              onPress={(e) => {
                setPressedTab("insights");
                props.onPress?.(e);
                setTimeout(() => {
                  setPressedTab(null);
                }, 300);
              }}
            >
              <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
                {props.children}
                {pressedTab === "insights" && <TabPressAnimation />}
              </View>
            </TouchableWithoutFeedback>
          ),
        }}
      />
      <Tabs.Screen
        name="manage"
        options={{
          tabBarIcon: ({ focused }) => (
            <View style={{ alignItems: "center" }}>
              <View style={lineIndicatorStyle(focused)} />
              <View style={shadowCircleStyle(focused)} className="-mt-8" />
              <CreateButtonAnimation animate={pressedTab === "manage"}>
                <Image
                  source={images.manage}
                  style={{
                    width: 24,
                    height: 24,
                    marginBottom: 3,
                    tintColor: focused ? "#159AFF" : "#455A64",
                  }}
                />
              </CreateButtonAnimation>
            </View>
          ),
          tabBarLabel: "Manage",
          tabBarButton: (props) => (
            <TouchableWithoutFeedback 
              onPress={(e) => {
                setPressedTab("manage");
                props.onPress?.(e);
                setTimeout(() => {
                  setPressedTab(null);
                }, 300);
              }}
            >
              <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
                {props.children}
                {pressedTab === "manage" && <TabPressAnimation />}
              </View>
            </TouchableWithoutFeedback>
          ),
        }}
      />
    </Tabs>
  );
}
