import { CustomerService } from '../../customer/customer.service';
import { QueueFlowService } from '../queue-flow/queue-flow.service';
import { RedisService } from '../redis/redis.service';
export declare class SchedulerService {
    private readonly customerService;
    private readonly queueFlowService;
    private readonly redisService;
    private readonly logger;
    constructor(customerService: CustomerService, queueFlowService: QueueFlowService, redisService: RedisService);
    updateExpiredQueues(): Promise<void>;
    checkGracePeriods(): Promise<void>;
    runExpiredQueuesCheck(): Promise<{
        updated: number;
        message: string;
    }>;
    startGracePeriod(queueId: number, serviceId: number): Promise<import("../../partner/entities/queue.entity").Queue>;
    checkAndMarkExpiredGracePeriod(queueId: number): Promise<{
        status: string;
        message: string;
    }>;
    triggerAutoGracePeriods(): Promise<{
        status: string;
        message: string;
    }>;
    refreshServingQueuesTTL(): Promise<void>;
}
