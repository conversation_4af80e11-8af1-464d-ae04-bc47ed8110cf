"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddEmailToServices1710538800000 = void 0;
class AddEmailToServices1710538800000 {
    async up(queryRunner) {
        const hasEmailColumn = await queryRunner.hasColumn('services', 'email');
        if (!hasEmailColumn) {
            await queryRunner.query(`ALTER TABLE "services" ADD COLUMN "email" character varying`);
        }
        await queryRunner.query(`DELETE FROM "services" WHERE "email" IS NULL`);
        await queryRunner.query(`ALTER TABLE "services" ALTER COLUMN "email" SET NOT NULL`);
        const constraintExists = await queryRunner.query(`
            SELECT constraint_name 
            FROM information_schema.table_constraints 
            WHERE table_name = 'services' 
            AND constraint_name = 'UQ_services_email'
        `);
        if (!constraintExists.length) {
            await queryRunner.query(`ALTER TABLE "services" ADD CONSTRAINT "UQ_services_email" UNIQUE ("email")`);
        }
    }
    async down(queryRunner) {
        await queryRunner.query(`
            DO $$ 
            BEGIN
                IF EXISTS (
                    SELECT 1 
                    FROM information_schema.table_constraints 
                    WHERE table_name = 'services' 
                    AND constraint_name = 'UQ_services_email'
                ) THEN
                    ALTER TABLE "services" DROP CONSTRAINT "UQ_services_email";
                END IF;
            END $$;
        `);
        await queryRunner.query(`ALTER TABLE "services" ALTER COLUMN "email" DROP NOT NULL`);
        const hasEmailColumn = await queryRunner.hasColumn('services', 'email');
        if (hasEmailColumn) {
            await queryRunner.query(`ALTER TABLE "services" DROP COLUMN "email"`);
        }
    }
}
exports.AddEmailToServices1710538800000 = AddEmailToServices1710538800000;
//# sourceMappingURL=1710538800000-AddEmailToServices.js.map