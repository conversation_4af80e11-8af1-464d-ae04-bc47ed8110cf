import { Repository } from 'typeorm';
import { User } from './user.entity';
import { JoinQueueDto } from './dto/join-queue.dto';
import { RescheduleQueueDto } from './dto/reschedule-queue.dto';
import { Queue } from '../partner/entities/queue.entity';
import { Service } from '../partner/entities/service.entity';
import { ServiceSetup } from '../partner/entities/service-setup.entity';
import { RedisService } from '../services/redis/redis.service';
import { ModuleRef } from '@nestjs/core';
export declare class CustomerService {
    private readonly userRepository;
    private readonly queueRepository;
    private readonly serviceRepository;
    private readonly serviceSetupRepository;
    private readonly redisService;
    private readonly moduleRef;
    private queueFlowService;
    constructor(userRepository: Repository<User>, queueRepository: Repository<Queue>, serviceRepository: Repository<Service>, serviceSetupRepository: Repository<ServiceSetup>, redisService: RedisService, moduleRef: ModuleRef);
    private getQueueFlowService;
    private hasTimeSlotEnded;
    createUser(email: string, fullName?: string, clerkId?: string): Promise<{
        status: string;
        message: string;
        user: User;
        isExisting: boolean;
    }>;
    findUserByClerkId(clerkId: string): Promise<User | null>;
    updateLocation(email: string, latitude: number, longitude: number): Promise<{
        success: boolean;
        message: string;
    }>;
    updateVIPStatus(email: string, isVIP: boolean): Promise<{
        success: boolean;
        message: string;
        isVIP: boolean;
    }>;
    getUserProfile(email: string): Promise<{
        success: boolean;
        user: {
            id: number;
            email: string;
            fullName: string;
            mobileNumber: string;
            isVIP: boolean;
            lastLocationUpdate: Date;
        };
    }>;
    updateUserDetails(email: string, fullName?: string, mobileNumber?: string): Promise<{
        success: boolean;
        message: string;
        user: {
            id: number;
            email: string;
            fullName: string;
            mobileNumber: string;
            isVIP: boolean;
        };
    }>;
    updateClerkId(email: string, clerkId: string): Promise<{
        success: boolean;
        message: string;
        user: {
            id: number;
            email: string;
            clerkId: string;
        };
    }>;
    addToWishlist(email: string, serviceId: string): Promise<{
        success: boolean;
        message: string;
        wishlist: string[];
    }>;
    removeFromWishlist(email: string, serviceId: string): Promise<{
        success: boolean;
        message: string;
        wishlist: string[];
    }>;
    getWishlist(email: string): Promise<{
        status: string;
        wishlist: {
            serviceId: string;
        }[];
    }>;
    joinQueue(joinQueueDto: JoinQueueDto): Promise<Queue>;
    rescheduleQueue(rescheduleQueueDto: RescheduleQueueDto): Promise<Queue>;
    getUserQueues(userId: string): Promise<any>;
    getQueuesByStatus(userId: string, status: string): Promise<any[]>;
    getActiveUserQueues(userId: string): Promise<any[]>;
    getServiceActiveQueues(serviceId: number, date: string): Promise<any[]>;
    getQueueById(queueId: number | string, userId?: string): Promise<any>;
    checkInQueue(queueId: string, userId: string): Promise<any>;
    toggleCheckInQueue(queueId: string, userId: string): Promise<any>;
    cancelQueue(queueId: string, userId: string): Promise<any>;
    private getQueuePosition;
    completeQueue(queueId: string, userId: string, isCheckedIn?: boolean): Promise<any>;
    getServiceQueueCounts(serviceId: number, date: string, subUnitId?: string): Promise<Record<string, {
        normalCount: number;
        vipCount: number;
    }>>;
    getServiceQueuesByStatus(serviceId: number, date: string, status: string): Promise<any[]>;
    updateExpiredQueues(): Promise<{
        updated: number;
        message: string;
    }>;
    private updateQueueStatusByIds;
    getQueueByUniqueSlotId(uniqueSlotId: string): Promise<any>;
    getAllQueuesFromRedis(serviceId: number): Promise<any[]>;
    getAllQueuesDirectFromRedis(serviceId: number): Promise<any[]>;
    private getAllQueuesFromDatabase;
    ensureQueueConsistency(serviceId: number): Promise<void>;
    restoreQueueData(serviceId: number): Promise<void>;
    getQueueGracePeriodStatus(queueId: number): Promise<any>;
}
