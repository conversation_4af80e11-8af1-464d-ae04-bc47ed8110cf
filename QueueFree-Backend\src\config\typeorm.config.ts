import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import { Service } from '../partner/entities/service.entity';
import { ServiceSetup } from '../partner/entities/service-setup.entity';
import { User } from '../customer/user.entity';
import { AddEmailToServices1710538800000 } from '../migrations/1710538800000-AddEmailToServices';
import { UpdateSetupDataToJsonb1710539000000 } from '../migrations/1710539000000-UpdateSetupDataToJsonb';
import { AddTermsAcceptedToServices1710539100000 } from '../migrations/1710539100000-AddTermsAcceptedToServices';

config();

export default new DataSource({
  type: 'postgres',
  url: process.env.DATABASE_URL,
  entities: [Service, ServiceSetup, User],
  migrations: [
    AddEmailToServices1710538800000,
    UpdateSetupDataToJsonb1710539000000,
    AddTermsAcceptedToServices1710539100000
  ],
  ssl: true,
  extra: {
    ssl: {
      rejectUnauthorized: false,
    },
  },
});
