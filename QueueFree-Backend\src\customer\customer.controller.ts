import { <PERSON>, <PERSON>, Body, BadRequestException, Get, Param, Put, Delete, Query, HttpException, HttpStatus, NotFoundException } from '@nestjs/common';
import { CustomerService } from './customer.service';
import { JoinQueueDto } from './dto/join-queue.dto';
import { QueueFlowService } from '../services/queue-flow/queue-flow.service';
import { SchedulerService } from '../services/scheduler/scheduler.service';
import { In } from 'typeorm';

@Controller('customer')
export class CustomerController {
  constructor(
    private readonly customerService: CustomerService,
    private readonly queueFlowService: QueueFlowService,
    private readonly schedulerService: SchedulerService,
  ) {}

  @Post('register')
  async registerUser(@Body() body: { email: string;}) {
    return this.customerService.createUser(body.email);
  }

  @Post('update-location')
  async updateLocation(
    @Body() data: { email: string; latitude: number; longitude: number }
  ) {
    try {
      return await this.customerService.updateLocation(
        data.email,
        data.latitude,
        data.longitude
      );
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Put('update-vip')
  async updateVIPStatus(
    @Body() data: { email: string; isVIP: boolean }
  ) {
    try {
      return await this.customerService.updateVIPStatus(
        data.email,
        data.isVIP
      );
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('profile/:email')
  async getUserProfile(@Param('email') email: string) {
    try {
      return await this.customerService.getUserProfile(email);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Put('update-details')
  async updateUserDetails(
    @Body() data: { email: string; fullName?: string; mobileNumber?: string }
  ) {
    try {
      return await this.customerService.updateUserDetails(
        data.email,
        data.fullName,
        data.mobileNumber
      );
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Put('update-clerk-id')
  async updateClerkId(
    @Body() data: { email: string; clerkId: string }
  ) {
    try {
      return await this.customerService.updateClerkId(
        data.email,
        data.clerkId
      );
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('wishlist/add')
  async addToWishlist(@Body() data: { email: string; serviceId: string }) {
    try {
      return await this.customerService.addToWishlist(data.email, data.serviceId);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Delete('wishlist/remove')
  async removeFromWishlist(@Body() data: { email: string; serviceId: string }) {
    try {
      return await this.customerService.removeFromWishlist(data.email, data.serviceId);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('wishlist/:email')
  async getWishlist(@Param('email') email: string) {
    try {
      return await this.customerService.getWishlist(email);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('queues/join')
  async joinQueue(@Body() joinQueueDto: JoinQueueDto) {
    try {
      const result = await this.customerService.joinQueue(joinQueueDto);

      // Enhanced response with more complete data for the mobile app
      return {
        status: 'success',
        message: 'Successfully joined the queue',
        queueId: result.id,
        serviceId: result.serviceId, // Include serviceId for cache invalidation on client
        uniqueSlotId: result.uniqueSlotId || Math.floor(1000 + Math.random() * 9000).toString(),
        timeSlot: result.timeSlot,
        date: result.date,
        hasSubUnits: result.hasSubUnits,
        subUnitId: result.subUnitId,
        subUnitName: result.subUnitName,
        position: result.position, // Include the position
        timestamp: new Date().toISOString(), // Add timestamp for caching control
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to join queue',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queues/:userId')
  async getUserQueues(@Param('userId') userId: string) {
    try {
      const queues = await this.customerService.getUserQueues(userId);
      return {
        status: 'success',
        queues,
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to fetch user queues',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queues/:userId/active')
  async getActiveUserQueues(@Param('userId') userId: string) {
    try {
      const queues = await this.customerService.getActiveUserQueues(userId);
      return {
        status: 'success',
        queues,
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to fetch active user queues',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queues/:userId/by-status')
  async getQueuesByStatusQuery(
    @Param('userId') userId: string,
    @Query('status') status: string
  ) {
    try {
      console.log(`Fetching queues for userId: ${userId}, status: ${status}`);
      const queues = await this.customerService.getQueuesByStatus(userId, status);
      return {
        status: 'success',
        queues,
      };
    } catch (error) {
      console.error(`Error fetching queues by status: ${error.message}`);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || `Failed to fetch ${status} queues`,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queues/:userId/:status')
  async getQueuesByStatus(
    @Param('userId') userId: string,
    @Param('status') status: string
  ) {
    try {
      const queues = await this.customerService.getQueuesByStatus(userId, status);
      return {
        status: 'success',
        queues,
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || `Failed to fetch ${status} queues`,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queue/:queueId')
  async getQueueById(@Param('queueId') queueId: string) {
    try {
      const queue = await this.customerService.getQueueById(queueId);
      return {
        status: 'success',
        queue,
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to fetch queue details',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queue/:queueId/estimated-wait-time')
  async getEstimatedWaitTime(@Param('queueId') queueId: string) {
    try {
      // Sanitize and validate queueId
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum)) {
        throw new BadRequestException('Invalid queue ID');
      }

      console.log(`Retrieving estimated wait time for queue: ${queueId}`);

      // Use the QueueFlowService to get the estimated wait time
      const waitTimeData = await this.queueFlowService.getEstimatedWaitTime(queueIdNum);

      return {
        status: 'success',
        data: {
          queueId: queueIdNum,
          waitTimeMinutes: waitTimeData.waitTimeMinutes,
          waitTimeStatus: waitTimeData.waitTimeStatus,
          estimatedServeTime: waitTimeData.estimatedServeTime.toISOString(),
          position: waitTimeData.position,
          initialPositionAtJoin: waitTimeData.initialPositionAtJoin
        }
      };
    } catch (error) {
      console.error(`Error getting estimated wait time for queue ${queueId}:`, error);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to get estimated wait time',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queue/:queueId/actual-service-time')
  async getActualServiceTime(@Param('queueId') queueId: string) {
    try {
      // Sanitize and validate queueId
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum)) {
        throw new BadRequestException('Invalid queue ID');
      }

      console.log(`Retrieving actual service time for queue: ${queueId}`);

      // Use the QueueFlowService to get the actual service time
      const serviceTimeData = await this.queueFlowService.getActualServiceTime(queueIdNum);

      return {
        status: 'success',
        data: {
          queueId: queueIdNum,
          serviceTimeMinutes: serviceTimeData.serviceTimeMinutes,
          servingStartedAt: serviceTimeData.servingStartedAt?.toISOString() || null,
          statusUpdatedAt: serviceTimeData.statusUpdatedAt?.toISOString() || null,
          status: serviceTimeData.status
        }
      };
    } catch (error) {
      console.error(`Error getting actual service time for queue ${queueId}:`, error);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to get actual service time',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Put('/queue/:queueId/check-in')
  async checkInQueue(
    @Param('queueId') queueId: string,
    @Body() body: { userId: string },
  ) {
    return this.customerService.checkInQueue(queueId, body.userId);
  }

  @Put('/queue/:queueId/toggle-check-in')
  async toggleCheckInQueue(
    @Param('queueId') queueId: string,
    @Body() body: { userId: string },
  ) {
    return this.customerService.toggleCheckInQueue(queueId, body.userId);
  }

  @Put('queue/:queueId/cancel')
  async cancelQueue(
    @Param('queueId') queueId: string,
    @Body() body: { userId: string }
  ) {
    try {
      // Sanitize and validate queueId
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum) || queueIdNum <= 0) {
        throw new BadRequestException('Invalid queue ID format');
      }

      // First get the queue from database to ensure we have all data
      const queue = await this.customerService['queueRepository'].findOne({
        where: { id: queueIdNum, userId: body.userId },
        relations: ['service']
      });

      if (!queue) {
        throw new NotFoundException(`Queue with ID ${queueIdNum} not found for this user`);
      }

      // Get all queues for this service, date, and time slot (both waiting and serving)
      const allQueues = await this.customerService['queueRepository'].find({
        where: {
          serviceId: queue.serviceId,
          date: queue.date,
          timeSlot: queue.timeSlot,
          status: In(['waiting', 'serving']),
        },
        order: {
          isVIP: 'DESC', // VIP first
          position: 'ASC', // Then by position
          createdAt: 'ASC' // Then by creation time
        }
      });

      // Separate waiting and serving queues
      const servingQueues = allQueues.filter(q => q.status === 'serving');
      const waitingQueues = allQueues.filter(q => q.status === 'waiting');

      console.log(`Found ${servingQueues.length} serving queues and ${waitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);

      // Update the queue status in the database
      queue.status = 'cancelled';
      queue.statusUpdatedAt = new Date();
      // Set position to -1 to indicate it's no longer in the queue
      queue.position = -1;
      const updatedQueue = await this.customerService['queueRepository'].save(queue);

      // Update Redis for the cancelled queue
      const redisService = this.customerService['redisService'];

      // Get the current queue data from Redis
      const queueData = await redisService.getQueue(queueIdNum.toString()) || {};

      // Update the status and position
      queueData.status = 'cancelled';
      queueData.position = -1;

      // Save the updated queue data to Redis
      await redisService.saveQueue(queueIdNum.toString(), queueData);

      // Also update the status using the dedicated method
      await redisService.updateQueueStatus(queueIdNum.toString(), 'cancelled', true);

      // Create positions object for Redis
      const positions: Record<string, number> = {};
      let vipQueues: any[] = [];
      let normalQueues: any[] = [];
      let allRemainingQueues: any[] = [];

      // Only reorder positions if the cancelled queue was in waiting status before cancellation
      if (queue.status === 'waiting' || queue.status === 'checked-in') {
        // Reorder positions for all remaining waiting queues
        // Filter out the cancelled queue
        const remainingWaitingQueues = waitingQueues.filter(q => q.id !== queueIdNum);

        // Reassign positions to all remaining waiting queues
        let position = 0;

        // Process VIP queues first, then non-VIP queues
        vipQueues = remainingWaitingQueues.filter(q => q.isVIP);
        normalQueues = remainingWaitingQueues.filter(q => !q.isVIP);

        console.log(`Reordering positions: ${vipQueues.length} VIP queues and ${normalQueues.length} normal queues`);

        // Process VIP queues first
        for (const q of vipQueues) {
          position++;
          q.position = position;
          positions[q.id] = position;

          // Update Redis for this queue
          const qData = await redisService.getQueue(q.id.toString()) || {};
          qData.position = position;
          await redisService.saveQueue(q.id.toString(), qData);
        }

        // Then process normal queues
        for (const q of normalQueues) {
          position++;
          q.position = position;
          positions[q.id] = position;

          // Update Redis for this queue
          const qData = await redisService.getQueue(q.id.toString()) || {};
          qData.position = position;
          await redisService.saveQueue(q.id.toString(), qData);
        }

        // Combine VIP and normal queues for saving
        allRemainingQueues = [...vipQueues, ...normalQueues];
        console.log(`Reordered positions for ${allRemainingQueues.length} queues after cancelling queue ${queueIdNum}`);

        // Save all queues to database
        if (allRemainingQueues.length > 0) {
          await this.customerService['queueRepository'].save(allRemainingQueues);
        }
      } else {
        console.log(`Queue ${queueIdNum} was not in waiting status, no need to reorder positions`);
      }

      // Save all positions to Redis and invalidate cache
      const dateStr = new Date(queue.date).toISOString().split('T')[0];

      await redisService.saveQueuePosition(
        queue.serviceId.toString(),
        dateStr,
        queue.timeSlot,
        positions
      );

      // Invalidate any cached queue positions
      await redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);

      // Invalidate service queues cache
      await redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
      await redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');

      // Recalculate estimated serve times for all queues in this service
      try {
        console.log(`Recalculating estimated serve times after queue ${queueId} was cancelled`);
        await this.queueFlowService.recalculateEstimatedServeTimes(
          queue.serviceId,
          queue.date,
          queue.timeSlot
        );
        console.log(`Successfully recalculated estimated serve times for all queues after cancelling queue ${queueId}`);
      } catch (error) {
        console.error(`Error recalculating estimated serve times: ${error.message}`);
        // Continue with the response even if recalculation fails
      }

      return {
        status: 'success',
        message: 'Queue cancelled and positions reordered',
        data: updatedQueue
      };
    } catch (error) {
      console.error('Error cancelling queue:', error);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to cancel queue',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queues/active/:serviceId/:date')
  async getServiceActiveQueues(
    @Param('serviceId') serviceId: string,
    @Param('date') date: string
  ) {
    try {
      const queues = await this.customerService.getServiceActiveQueues(parseInt(serviceId), date);
      return {
        status: 'success',
        queues,
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to fetch service active queues',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Put('queue/:queueId/complete')
  async completeQueue(
    @Param('queueId') queueId: string,
    @Body() body: { userId: string, isCheckedIn?: boolean }
  ) {
    try {
      console.log(`Complete queue request for queue ${queueId} with isCheckedIn=${body.isCheckedIn}`);

      // Sanitize and validate queueId
      const queueIdNum = parseInt(queueId, 10);

      if (isNaN(queueIdNum) || queueIdNum <= 0) {
        throw new BadRequestException('Invalid queue ID format');
      }

      // First get the queue from database to ensure we have all data
      const queue = await this.customerService['queueRepository'].findOne({
        where: { id: queueIdNum, userId: body.userId },
        relations: ['service']
      });

      if (!queue) {
        throw new NotFoundException(`Queue with ID ${queueIdNum} not found for this user`);
      }

      // Get all queues for this service, date, and time slot (both waiting and serving)
      const allQueues = await this.customerService['queueRepository'].find({
        where: {
          serviceId: queue.serviceId,
          date: queue.date,
          timeSlot: queue.timeSlot,
          status: In(['waiting', 'serving']),
        },
        order: {
          isVIP: 'DESC', // VIP first
          position: 'ASC', // Then by position
          createdAt: 'ASC' // Then by creation time
        }
      });

      // Separate waiting and serving queues
      const servingQueues = allQueues.filter(q => q.status === 'serving');
      const waitingQueues = allQueues.filter(q => q.status === 'waiting');

      console.log(`Found ${servingQueues.length} serving queues and ${waitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);

      // Update the queue status in the database
      queue.status = 'completed';
      queue.statusUpdatedAt = new Date();
      // Set position to -1 to indicate it's no longer in the queue
      queue.position = -1;

      // If isCheckedIn is provided, update that as well
      if (body.isCheckedIn !== undefined) {
        queue.isCheckedIn = body.isCheckedIn;
      }

      const updatedQueue = await this.customerService['queueRepository'].save(queue);

      // Update Redis for the completed queue
      const redisService = this.customerService['redisService'];

      // Get the current queue data from Redis
      const queueData = await redisService.getQueue(queueIdNum.toString()) || {};

      // Update the status and position
      queueData.status = 'completed';
      queueData.position = -1;

      // Save the updated queue data to Redis
      await redisService.saveQueue(queueIdNum.toString(), queueData);

      // Also update the status using the dedicated method
      await redisService.updateQueueStatus(queueIdNum.toString(), 'completed', true);

      // Create positions object for Redis
      const positions: Record<string, number> = {};
      let vipQueues: any[] = [];
      let normalQueues: any[] = [];
      let allRemainingQueues: any[] = [];

      // Only reorder positions if the completed queue was in waiting status before completion
      if (queue.status === 'waiting' || queue.status === 'checked-in') {
        // Reorder positions for all remaining waiting queues
        // Filter out the completed queue
        const remainingWaitingQueues = waitingQueues.filter(q => q.id !== queueIdNum);

        // Reassign positions to all remaining waiting queues
        let position = 0;

        // Process VIP queues first, then non-VIP queues
        vipQueues = remainingWaitingQueues.filter(q => q.isVIP);
        normalQueues = remainingWaitingQueues.filter(q => !q.isVIP);

        console.log(`Reordering positions: ${vipQueues.length} VIP queues and ${normalQueues.length} normal queues`);

        // Process VIP queues first
        for (const q of vipQueues) {
          position++;
          q.position = position;
          positions[q.id] = position;

          // Update Redis for this queue
          const qData = await redisService.getQueue(q.id.toString()) || {};
          qData.position = position;
          await redisService.saveQueue(q.id.toString(), qData);
        }

        // Then process normal queues
        for (const q of normalQueues) {
          position++;
          q.position = position;
          positions[q.id] = position;

          // Update Redis for this queue
          const qData = await redisService.getQueue(q.id.toString()) || {};
          qData.position = position;
          await redisService.saveQueue(q.id.toString(), qData);
        }

        // Combine VIP and normal queues for saving
        allRemainingQueues = [...vipQueues, ...normalQueues];
        console.log(`Reordered positions for ${allRemainingQueues.length} queues after completing queue ${queueIdNum}`);

        // Save all queues to database
        if (allRemainingQueues.length > 0) {
          await this.customerService['queueRepository'].save(allRemainingQueues);
        }
      } else {
        console.log(`Queue ${queueIdNum} was not in waiting status, no need to reorder positions`);
      }

      // Save all positions to Redis and invalidate cache
      const dateStr = new Date(queue.date).toISOString().split('T')[0];
      await redisService.saveQueuePosition(
        queue.serviceId.toString(),
        dateStr,
        queue.timeSlot,
        positions
      );

      // Invalidate any cached queue positions
      await redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);

      // Invalidate service queues cache
      await redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
      await redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');

      // Recalculate estimated serve times for all queues in this service
      try {
        console.log(`Recalculating estimated serve times after queue ${queueId} was completed`);
        this.queueFlowService.recalculateEstimatedServeTimes(
          queue.serviceId,
          queue.date,
          queue.timeSlot
        ).catch(error => {
          console.error(`Error recalculating estimated serve times: ${error.message}`);
        });
      } catch (error) {
        console.error(`Error triggering recalculation of estimated serve times: ${error.message}`);
      }

      return {
        status: 'success',
        message: 'Queue completed and positions reordered',
        data: updatedQueue
      };
    } catch (error) {
      console.error('Error completing queue:', error);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to complete queue',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queues/counts/:serviceId/:date')
  async getServiceQueueCounts(
    @Param('serviceId') serviceId: string,
    @Param('date') date: string,
    @Query('subUnitId') subUnitId?: string
  ) {
    try {
      const counts = await this.customerService.getServiceQueueCounts(parseInt(serviceId), date, subUnitId);

      return {
        status: 'success',
        counts
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to fetch queue counts',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queues/redis/:serviceId')
  async getServiceQueuesFromRedis(
    @Param('serviceId') serviceId: string,
    @Query('subUnitId') subUnitId?: string
  ) {
    try {
      // Fetch all active queues for this service without date filtering
      const queues = await this.customerService.getServiceActiveQueues(parseInt(serviceId), 'all');

      console.log(`Got ${queues.length} queues for service ${serviceId} from Redis/DB`);

      // Filter by subUnitId if provided
      let filteredQueues = queues;
      if (subUnitId !== undefined) {
        console.log(`Filtering queues by subUnitId: ${subUnitId}`);
        filteredQueues = queues.filter(queue =>
          queue.hasSubUnits && queue.subUnitId === subUnitId
        );
        console.log(`After filtering, found ${filteredQueues.length} queues for subUnitId ${subUnitId}`);
      }

      return {
        status: 'success',
        queues: filteredQueues,
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to fetch queues from Redis',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queues/redis/:serviceId/all')
  async getServiceQueuesAllStatuses(@Param('serviceId') serviceId: string) {
    try {
      console.log(`API request received to fetch all queues for service ID: ${serviceId}`);

      // Validate serviceId
      const id = parseInt(serviceId);
      if (isNaN(id)) {
        console.error(`Invalid service ID format: ${serviceId}`);
        throw new Error('Invalid service ID format');
      }

      // Use a direct optimized method for faster Redis retrieval
      const startTime = Date.now();
      const allQueues = await this.customerService.getAllQueuesDirectFromRedis(id);
      const endTime = Date.now();

      console.log(`Fast query completed in ${endTime - startTime}ms for service ${id} (found ${allQueues.length} queues)`);

      return {
        status: 'success',
        queues: allQueues,
      };
    } catch (error) {
      console.error(`Error in getServiceQueuesAllStatuses for service ${serviceId}:`, error);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to fetch all queues',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queues/redis/:serviceId/:status')
  async getServiceQueuesFromRedisByStatus(
    @Param('serviceId') serviceId: string,
    @Param('status') status: string,
    @Query('subUnitId') subUnitId?: string
  ) {
    try {
      // Skip if 'all' to avoid conflict with the specific route
      if (status === 'all') {
        throw new HttpException(
          {
            status: 'error',
            message: 'For all statuses, use the /all endpoint directly',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Map 'up' to 'upcoming' to match what the service expects
      const mappedStatus = status === 'up' ? 'upcoming' : status;

      // Validate status
      const validStatuses = ['upcoming', 'completed', 'cancelled', 'waiting', 'checked-in'];
      if (!validStatuses.includes(mappedStatus)) {
        throw new HttpException(
          {
            status: 'error',
            message: 'Invalid status parameter. Must be one of: upcoming, completed, cancelled',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Fetch all queues first
      let queues = await this.customerService.getServiceActiveQueues(parseInt(serviceId), 'all');
      console.log(`Got ${queues.length} queues for service ${serviceId} with status ${mappedStatus}`);

      // If not upcoming or waiting, we need to filter by specific status
      if (mappedStatus !== 'upcoming' && mappedStatus !== 'waiting') {
        const statusQueues = await this.customerService.getServiceQueuesByStatus(parseInt(serviceId), 'all', mappedStatus);
        queues = statusQueues;
      }

      // Filter by subUnitId if provided
      if (subUnitId !== undefined) {
        console.log(`Filtering queues by subUnitId: ${subUnitId}`);
        queues = queues.filter(queue =>
          queue.hasSubUnits && queue.subUnitId === subUnitId
        );
        console.log(`After filtering, found ${queues.length} queues for subUnitId ${subUnitId}`);
      }

      // Return all active queues without date filtering, let the client filter by date
      return {
        status: 'success',
        queues,
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || `Failed to fetch ${status} queues from Redis`,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Endpoint to manually trigger the update of expired queues
   * This will check all waiting queues and update their status if their time slot has ended:
   * - If the customer checked in, the queue will be marked as "completed"
   * - If the customer did not check in, the queue will be marked as "no-show"
   */
  @Get('update-expired-queues')
  async updateExpiredQueues(): Promise<{updated: number, message: string}> {
    console.log('Manual update of expired queues triggered');
    return this.customerService.updateExpiredQueues();
  }

  @Get('profile-by-clerk/:clerkId')
  async getUserProfileByClerkId(@Param('clerkId') clerkId: string) {
    try {
      const user = await this.customerService.findUserByClerkId(clerkId);

      if (!user) {
        throw new NotFoundException('User not found for this clerk ID');
      }

      return {
        status: 'success',
        user: {
          id: user.id,
          email: user.email,
          fullName: user.fullName,
          mobileNumber: user.mobileNumber,
          isVIP: user.isVIP,
          lastLocationUpdate: user.lastLocationUpdate,
          clerkId: user.clerkId
        }
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to fetch user profile',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('user-mobile/:clerkId')
  async getUserMobileByClerkId(@Param('clerkId') clerkId: string) {
    try {
      const user = await this.customerService.findUserByClerkId(clerkId);

      if (!user) {
        throw new NotFoundException('User not found for this clerk ID');
      }

      return {
        status: 'success',
        data: {
          mobileNumber: user.mobileNumber || null,
          fullName: user.fullName || null,
          email: user.email
        }
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to fetch user mobile number',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queue-mobile/:queueId')
  async getQueueMobileNumber(@Param('queueId') queueId: string) {
    try {
      const queue = await this.customerService.getQueueById(queueId);
      if (!queue) {
        throw new NotFoundException(`Queue with ID ${queueId} not found`);
      }

      // If mobile number is already in the queue data, return it
      if (queue.mobileNumber) {
        return {
          status: 'success',
          data: {
            mobileNumber: queue.mobileNumber,
            fullName: queue.fullName || queue.userName || 'Unknown',
            userId: queue.userId
          }
        };
      }

      // If userId is a clerkId, try to get the mobile number from the user
      if (queue.userId && !queue.userId.includes('@')) {
        const user = await this.customerService.findUserByClerkId(queue.userId);
        if (user && user.mobileNumber) {
          return {
            status: 'success',
            data: {
              mobileNumber: user.mobileNumber,
              fullName: user.fullName || queue.fullName || queue.userName || 'Unknown',
              userId: queue.userId
            }
          };
        }
      }

      // If no mobile number is found
      return {
        status: 'success',
        data: {
          mobileNumber: null,
          fullName: queue.fullName || queue.userName || 'Unknown',
          userId: queue.userId
        }
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to fetch queue mobile number',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queue-by-uniqueslotid/:uniqueSlotId')
  async getQueueByUniqueSlotId(@Param('uniqueSlotId') uniqueSlotId: string) {
    try {
      const queue = await this.customerService.getQueueByUniqueSlotId(uniqueSlotId);
      return {
        status: 'success',
        queue,
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to fetch queue by unique slot ID',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queues/ensure-consistency/:serviceId')
  async ensureQueueConsistency(@Param('serviceId') serviceId: string) {
    try {
      console.log(`Ensuring queue consistency for service ${serviceId}`);

      // Validate serviceId
      const id = parseInt(serviceId);
      if (isNaN(id)) {
        throw new BadRequestException('Invalid service ID format');
      }

      // First run consistency check to fix any status mismatches
      await this.customerService.ensureQueueConsistency(id);

      // Then restore any missing data
      await this.customerService.restoreQueueData(id);

      return {
        status: 'success',
        message: 'Queue data consistency check completed'
      };
    } catch (error) {
      console.error('Error ensuring queue consistency:', error);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to ensure queue consistency',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('queue/:queueId/grace-period-status')
  async getGracePeriodStatus(@Param('queueId') queueId: string) {
    try {
      const queueData = await this.customerService.getQueueGracePeriodStatus(parseInt(queueId, 10));
      return { status: 'success', data: queueData };
    } catch (error) {
      return { status: 'error', message: error.message };
    }
  }

  /**
   * Mark a queue as no-show when grace period expires on the client side
   * This provides an immediate response when the grace period timer reaches zero
   */
  @Put('queue/:queueId/grace-period-expired')
  async markGracePeriodExpired(@Param('queueId') queueId: string) {
    try {
      await this.schedulerService.checkAndMarkExpiredGracePeriod(parseInt(queueId, 10));
      return { status: 'success', message: 'Grace period check completed' };
    } catch (error) {
      return { status: 'error', message: error.message };
    }
  }

  @Put('queue/:queueId/confirm-presence')
  async confirmPresence(
    @Param('queueId') queueId: string,
    @Body() body: { isPresent: boolean, userId: string }
  ) {
    try {
      // Check if the queue belongs to this user for security
      const queue = await this.customerService.getQueueById(parseInt(queueId, 10), body.userId);

      if (!queue) {
        throw new NotFoundException('Queue not found or does not belong to this user');
      }

      // Use the QueueFlowService to handle presence confirmation
      const updatedQueue = await this.queueFlowService.handlePresenceConfirmation(
        parseInt(queueId, 10),
        body.isPresent
      );

      return {
        status: 'success',
        message: body.isPresent ? 'Presence confirmed' : 'Marked as not present',
        data: updatedQueue
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to confirm presence'
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('queue/:queueId/serving-status')
  async getServingStatus(@Param('queueId') queueId: string) {
    try {
      const queueIdNum = parseInt(queueId, 10);
      if (isNaN(queueIdNum)) {
        throw new BadRequestException('Invalid queue ID');
      }

      console.log(`Retrieving serving status for queue: ${queueId}`);

      // First check if we have the data cached in Redis for faster response
      try {
        const redisData = await this.customerService['redisService'].getQueue(queueId);

        if (redisData &&
            redisData.status === 'serving' &&
            redisData.currentlyServing === true &&
            redisData.servingStartedAt) {

          // We have Redis data with serving info already
          console.log(`Found cached serving data in Redis for queue ${queueId}`);

          // Get the service setup to ensure correct serving time
          const serviceSetup = await this.customerService['serviceSetupRepository'].findOne({
            where: { service: { id: redisData.serviceId } }
          });

          // Check if this is a service with subunits
          let servingTimeMinutes = 15; // Default

          if (redisData.hasSubUnits && redisData.subUnitId && serviceSetup?.setupData?.hasSubUnits &&
              Array.isArray(serviceSetup.setupData.subUnits) && serviceSetup.setupData.subUnits.length > 0) {

            // Get the subunit ID from the queue
            const subUnitId = parseInt(redisData.subUnitId, 10);

            // Make sure the subunit ID is valid
            const subUnitIndex = isNaN(subUnitId) ? 0 : Math.min(subUnitId, serviceSetup.setupData.subUnits.length - 1);

            // Get the subunit
            const subUnit = serviceSetup.setupData.subUnits[subUnitIndex];

            // Get serve time from subunit
            if (subUnit?.avgServeTime) {
              servingTimeMinutes = parseInt(subUnit.avgServeTime, 10);
              console.log(`Using subunit ${subUnitIndex} serve time: ${servingTimeMinutes} minutes`);
            }
          }
          // For regular services, use the service setup serve time
          else if (serviceSetup?.setupData?.servingTime) {
            servingTimeMinutes = parseInt(serviceSetup.setupData.servingTime, 10);
            console.log(`Using service setup serve time: ${servingTimeMinutes} minutes`);
          }

          // Use Redis value if available (most accurate)
          if (redisData.serveTime) {
            servingTimeMinutes = redisData.serveTime;
            console.log(`Using queue's stored serve time from Redis: ${servingTimeMinutes} minutes`);
          } else if (redisData.servingTimeMinutes) {
            servingTimeMinutes = redisData.servingTimeMinutes;
            console.log(`Using servingTimeMinutes from Redis: ${servingTimeMinutes} minutes`);
          }

          // Calculate remaining time based on start time and serving duration
          const servingStartedAt = new Date(redisData.servingStartedAt);
          const estimatedEndTime = new Date(servingStartedAt);
          estimatedEndTime.setMinutes(estimatedEndTime.getMinutes() + servingTimeMinutes);

          const now = new Date();
          const remainingMs = Math.max(0, estimatedEndTime.getTime() - now.getTime());
          const remainingSeconds = Math.floor(remainingMs / 1000);
          const remainingMinutes = Math.floor(remainingSeconds / 60);

          console.log(`Queue ${queueId} serving time calculation from Redis:`);
          console.log(`Started: ${servingStartedAt.toISOString()}`);
          console.log(`End time: ${estimatedEndTime.toISOString()}`);
          console.log(`Serving time: ${servingTimeMinutes} minutes`);
          console.log(`Remaining: ${remainingMinutes} minutes ${remainingSeconds % 60} seconds`);

          return {
            status: 'success',
            data: {
              queueId: redisData.id,
              serviceId: redisData.serviceId,
              isServing: true,
              servingStartedAt: redisData.servingStartedAt,
              estimatedEndTime: estimatedEndTime.toISOString(),
              servingTimeMinutes: servingTimeMinutes,
              remainingMinutes: remainingMinutes,
              remainingSeconds: remainingSeconds
            }
          };
        }
      } catch (error) {
        console.error(`Error getting Redis serving data for queue ${queueId}:`, error);
        // Continue to database query if Redis fails
      }

      // Get the queue
      const queue = await this.customerService['queueRepository'].findOne({
        where: { id: queueIdNum },
        relations: ['service']
      });

      if (!queue) {
        throw new NotFoundException(`Queue with ID ${queueIdNum} not found`);
      }

      // Get the service setup to get serving time
      const serviceSetup = await this.customerService['serviceSetupRepository'].findOne({
        where: { service: { id: queue.serviceId } }
      });

      const servingTimeMinutes = serviceSetup?.setupData?.servingTime
        ? parseInt(serviceSetup.setupData.servingTime)
        : 15; // Default 15 minutes

      // Check if the queue is currently being served
      const isServing = queue.status === 'serving' && queue.currentlyServing;

      // If not serving, return basic status
      if (!isServing || !queue.servingStartedAt) {
        return {
          status: 'success',
          data: {
            queueId: queue.id,
            serviceId: queue.serviceId,
            isServing: false,
            servingStartedAt: null,
            estimatedEndTime: null,
            servingTimeMinutes: servingTimeMinutes,
            remainingMinutes: 0,
            remainingSeconds: 0
          }
        };
      }

      // Calculate estimated end time and remaining time
      const servingStartedAt = new Date(queue.servingStartedAt);
      const estimatedEndTime = new Date(servingStartedAt);
      estimatedEndTime.setMinutes(estimatedEndTime.getMinutes() + servingTimeMinutes);

      const now = new Date();
      const remainingMs = Math.max(0, estimatedEndTime.getTime() - now.getTime());
      const remainingSeconds = Math.floor(remainingMs / 1000);
      const remainingMinutes = Math.floor(remainingSeconds / 60);

      console.log(`Queue ${queueId} serving time calculation from DB:`);
      console.log(`Started: ${servingStartedAt.toISOString()}`);
      console.log(`End time: ${estimatedEndTime.toISOString()}`);
      console.log(`Serving time: ${servingTimeMinutes} minutes`);
      console.log(`Remaining: ${remainingMinutes} minutes ${remainingSeconds % 60} seconds`);

      // Update Redis with current status for future requests
      try {
        const redisData = {
          id: queue.id,
          serviceId: queue.serviceId,
          status: 'serving',
          currentlyServing: true,
          servingStartedAt: queue.servingStartedAt.toISOString(),
          estimatedEndTime: estimatedEndTime.toISOString(),
          servingTime: servingTimeMinutes,
          servingTimeMinutes: servingTimeMinutes,
          remainingMinutes: remainingMinutes,
          remainingSeconds: remainingSeconds,
          statusUpdatedAt: new Date().toISOString(),
          calculatedAt: new Date().toISOString()
        };

        await this.customerService['redisService'].set(`queue:${queueId}`, redisData, { ex: 86400 }); // 24 hours TTL
      } catch (error) {
        console.error(`Error updating Redis serving data for queue ${queueId}:`, error);
        // Continue without Redis update
      }

      return {
        status: 'success',
        data: {
          queueId: queue.id,
          serviceId: queue.serviceId,
          isServing: true,
          servingStartedAt: queue.servingStartedAt,
          estimatedEndTime: estimatedEndTime.toISOString(),
          servingTimeMinutes: servingTimeMinutes,
          remainingMinutes: remainingMinutes,
          remainingSeconds: remainingSeconds
        }
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to get serving status'
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('queues/:userId/status/serving')
  async getServingQueues(@Param('userId') userId: string) {
    try {
      console.log(`Fetching serving queues for userId: ${userId}`);
      // Find queues with "serving" status
      const queues = await this.customerService.getQueuesByStatus(userId, 'serving');
      return {
        status: 'success',
        queues,
      };
    } catch (error) {
      console.error(`Error fetching serving queues: ${error.message}`);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to fetch serving queues',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
