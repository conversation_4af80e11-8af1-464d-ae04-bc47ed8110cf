const { getDefaultConfig } = require("expo/metro-config");
const { withNativeWind } = require("nativewind/metro");

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname, {
  isCSSEnabled: true,
});

// Add this configuration
const { resolver: { sourceExts, assetExts } } = config;

config.resolver = {
  ...config.resolver,
  assetExts: assetExts.filter(ext => ext !== "svg"),
  sourceExts: [...sourceExts, "svg", "mjs", "cjs"],
  // Make sure to prioritize platform extensions
  platforms: ['web', 'android', 'ios', 'native'],
  // Exclude Mapbox packages from bundling
  blockList: [
    /.*mapbox.*/,
    /.*\/node_modules\/@rnmapbox\/.*/,
    /.*firebase.*/,
    /.*\/node_modules\/firebase\/.*/,
    /.*\/node_modules\/expo-firebase-core\/.*/,
    /.*\/node_modules\/expo-firebase-recaptcha\/.*/
  ]
};

// Remove custom resolver which is causing issues
delete config.resolver.resolveRequest;

module.exports = withNativeWind(config, { input: "./global.css" });