"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PartnerController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartnerController = void 0;
const common_1 = require("@nestjs/common");
const partner_service_1 = require("./partner.service");
const scheduler_service_1 = require("../services/scheduler/scheduler.service");
const common_2 = require("@nestjs/common");
const queue_flow_service_1 = require("../services/queue-flow/queue-flow.service");
const typeorm_1 = require("typeorm");
let PartnerController = PartnerController_1 = class PartnerController {
    constructor(partnerService, schedulerService, queueFlowService) {
        this.partnerService = partnerService;
        this.schedulerService = schedulerService;
        this.queueFlowService = queueFlowService;
        this.logger = new common_2.Logger(PartnerController_1.name);
    }
    async registerPartner(body) {
        return this.partnerService.createPartner(body.email);
    }
    async registerService(serviceData) {
        return this.partnerService.registerService(serviceData);
    }
    async getServiceStatus(serviceId) {
        try {
            const cleanId = serviceId.replace(/[^0-9]/g, '');
            const id = parseInt(cleanId, 10);
            if (!cleanId || isNaN(id) || id <= 0) {
                throw new common_1.BadRequestException('Invalid service ID format');
            }
            const result = await this.partnerService.getServiceStatus(id);
            return result;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw new common_1.NotFoundException(error.message);
            }
            if (error instanceof common_1.BadRequestException) {
                throw new common_1.BadRequestException(error.message);
            }
            throw new common_1.InternalServerErrorException(error?.message || 'Failed to fetch service status');
        }
    }
    async saveServiceSetup(serviceId, setupData) {
        return this.partnerService.saveServiceSetup(parseInt(serviceId), setupData);
    }
    async getServiceSetup(serviceId) {
        try {
            const id = parseInt(serviceId, 10);
            if (isNaN(id)) {
                throw new common_1.BadRequestException('Invalid service ID format');
            }
            const result = await this.partnerService.getServiceSetup(id);
            return result;
        }
        catch (error) {
            console.error('Error in getServiceSetup:', error);
            if (error instanceof common_1.NotFoundException) {
                throw new common_1.NotFoundException(error.message);
            }
            throw new common_1.BadRequestException(error.message || 'Failed to get service setup');
        }
    }
    async getServiceDetails(email) {
        return this.partnerService.getServiceDetailsByEmail(email);
    }
    async updateService(serviceData) {
        return this.partnerService.updateService(serviceData);
    }
    async checkServiceCompletion(email) {
        return this.partnerService.checkServiceCompletion(email);
    }
    async acceptTerms(email) {
        return this.partnerService.acceptTerms(email);
    }
    async getVerificationStatusByEmail(email) {
        try {
            const service = await this.partnerService.getVerificationStatusByEmail(email);
            return {
                success: true,
                status: service.verificationStatus || 'pending'
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw new common_1.NotFoundException(error.message);
            }
            throw new common_1.BadRequestException('Failed to get verification status');
        }
    }
    async toggleServiceStatus(email, data) {
        return this.partnerService.toggleServiceStatus(email, data.isOpen);
    }
    async getServiceOpenStatus(email) {
        try {
            const service = await this.partnerService.getServiceOpenStatus(email);
            return {
                success: true,
                isOpen: service.isOpen
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw new common_1.NotFoundException(error.message);
            }
            throw new common_1.BadRequestException('Failed to get service status');
        }
    }
    async saveBankDetails(serviceId, bankDetails) {
        return this.partnerService.saveBankDetails(parseInt(serviceId), bankDetails);
    }
    async getBankDetails(serviceId) {
        return this.partnerService.getBankDetails(parseInt(serviceId));
    }
    async saveLeaveDays(serviceId, leaveDays) {
        return this.partnerService.saveLeaveDays(parseInt(serviceId), leaveDays);
    }
    async getLeaveDays(serviceId) {
        return this.partnerService.getLeaveDays(parseInt(serviceId));
    }
    async getAllServices() {
        return this.partnerService.getAllServices();
    }
    async getServiceById(id) {
        try {
            const cleanId = id.replace(/[^0-9]/g, '');
            const serviceId = parseInt(cleanId, 10);
            if (!cleanId || isNaN(serviceId) || serviceId <= 0) {
                throw new common_1.BadRequestException('Invalid service ID format');
            }
            const result = await this.partnerService.getServiceById(serviceId);
            return result;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw new common_1.NotFoundException(error.message);
            }
            if (error instanceof common_1.BadRequestException) {
                throw new common_1.BadRequestException(error.message);
            }
            throw new common_1.InternalServerErrorException(error?.message || 'Failed to fetch service details');
        }
    }
    async getServiceReviews(id, limit = '10', offset = '0') {
        try {
            const cleanId = id.replace(/[^0-9]/g, '');
            const serviceId = parseInt(cleanId, 10);
            if (!cleanId || isNaN(serviceId) || serviceId <= 0) {
                throw new common_1.BadRequestException('Invalid service ID format');
            }
            const limitNum = parseInt(limit, 10) || 10;
            const offsetNum = parseInt(offset, 10) || 0;
            const result = await this.partnerService.getServiceReviews(serviceId, limitNum, offsetNum);
            return result;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw new common_1.NotFoundException(error.message);
            }
            if (error instanceof common_1.BadRequestException) {
                throw new common_1.BadRequestException(error.message);
            }
            throw new common_1.InternalServerErrorException(error?.message || 'Failed to fetch service reviews');
        }
    }
    async addReview(id, reviewData) {
        try {
            const cleanId = id.replace(/[^0-9]/g, '');
            const serviceId = parseInt(cleanId, 10);
            if (!cleanId || isNaN(serviceId) || serviceId <= 0) {
                throw new common_1.BadRequestException('Invalid service ID format');
            }
            if (!reviewData.userId || !reviewData.userName || !reviewData.rating || !reviewData.comment) {
                throw new common_1.BadRequestException('Missing required review data');
            }
            const result = await this.partnerService.addReview(serviceId, reviewData);
            return result;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw new common_1.NotFoundException(error.message);
            }
            if (error instanceof common_1.BadRequestException) {
                throw new common_1.BadRequestException(error.message);
            }
            throw new common_1.InternalServerErrorException(error?.message || 'Failed to add review');
        }
    }
    async getServiceTimeSlots(id, date, subUnitId) {
        try {
            const cleanId = id.replace(/[^0-9]/g, '');
            const serviceId = parseInt(cleanId, 10);
            if (!cleanId || isNaN(serviceId) || serviceId <= 0) {
                throw new common_1.BadRequestException('Invalid service ID format');
            }
            const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (!dateRegex.test(date)) {
                throw new common_1.BadRequestException('Invalid date format. Use YYYY-MM-DD');
            }
            let subUnitIdNum = undefined;
            if (subUnitId) {
                subUnitIdNum = parseInt(subUnitId, 10);
                if (isNaN(subUnitIdNum)) {
                    throw new common_1.BadRequestException('Invalid subUnitId format');
                }
                console.log(`Using subUnitId: ${subUnitIdNum} for time slots request`);
            }
            const result = await this.partnerService.getServiceTimeSlots(serviceId, date, subUnitIdNum);
            return result;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw new common_1.NotFoundException(error.message);
            }
            if (error instanceof common_1.BadRequestException) {
                throw new common_1.BadRequestException(error.message);
            }
            throw new common_1.InternalServerErrorException(error?.message || 'Failed to fetch time slots');
        }
    }
    async testServiceSetup(serviceId) {
        try {
            const id = parseInt(serviceId, 10);
            if (isNaN(id)) {
                throw new common_1.BadRequestException('Invalid service ID format');
            }
            const result = await this.partnerService.getServiceSetup(id);
            const leaveDaysResult = await this.partnerService.getLeaveDays(id);
            return {
                status: 'success',
                serviceId: id,
                setupData: result.data,
                leaveDays: leaveDaysResult.data
            };
        }
        catch (error) {
            console.error('Error in testServiceSetup:', error);
            throw new common_1.BadRequestException(error.message || 'Failed to test service setup');
        }
    }
    async completeQueue(queueId) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum) || queueIdNum <= 0) {
                throw new common_1.BadRequestException('Invalid queue ID format');
            }
            const queue = await this.partnerService['queueRepository'].findOne({
                where: { id: queueIdNum },
                relations: ['service']
            });
            if (!queue) {
                throw new common_1.NotFoundException(`Queue with ID ${queueIdNum} not found`);
            }
            const allQueues = await this.partnerService['queueRepository'].find({
                where: {
                    serviceId: queue.serviceId,
                    date: queue.date,
                    timeSlot: queue.timeSlot,
                    status: (0, typeorm_1.In)(['waiting', 'serving']),
                },
                order: {
                    isVIP: 'DESC',
                    position: 'ASC',
                    createdAt: 'ASC'
                }
            });
            const servingQueues = allQueues.filter(q => q.status === 'serving');
            const waitingQueues = allQueues.filter(q => q.status === 'waiting');
            this.logger.log(`Found ${servingQueues.length} serving queues and ${waitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);
            queue.status = 'completed';
            queue.statusUpdatedAt = new Date();
            queue.position = -1;
            const updatedQueue = await this.partnerService['queueRepository'].save(queue);
            const redisService = this.partnerService['redisService'];
            const queueData = await redisService.getQueue(queueIdNum.toString()) || {};
            queueData.status = 'completed';
            queueData.position = -1;
            await redisService.saveQueue(queueIdNum.toString(), queueData);
            await this.partnerService.updateQueueStatus(queueIdNum, 'completed', true);
            const remainingWaitingQueues = waitingQueues.filter(q => q.id !== queueIdNum);
            const positions = {};
            let position = 0;
            const vipQueues = remainingWaitingQueues.filter(q => q.isVIP);
            const normalQueues = remainingWaitingQueues.filter(q => !q.isVIP);
            this.logger.log(`Reordering positions: ${vipQueues.length} VIP queues and ${normalQueues.length} normal queues`);
            for (const q of vipQueues) {
                position++;
                q.position = position;
                positions[q.id] = position;
                const qData = await redisService.getQueue(q.id.toString()) || {};
                qData.position = position;
                await redisService.saveQueue(q.id.toString(), qData);
            }
            for (const q of normalQueues) {
                position++;
                q.position = position;
                positions[q.id] = position;
                const qData = await redisService.getQueue(q.id.toString()) || {};
                qData.position = position;
                await redisService.saveQueue(q.id.toString(), qData);
            }
            const allRemainingQueues = [...vipQueues, ...normalQueues];
            this.logger.log(`Reordered positions for ${allRemainingQueues.length} queues after completing queue ${queueIdNum}`);
            if (allRemainingQueues.length > 0) {
                await this.partnerService['queueRepository'].save(allRemainingQueues);
            }
            const dateStr = new Date(queue.date).toISOString().split('T')[0];
            const positionsToSave = positions;
            await redisService.saveQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot, positionsToSave);
            await redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);
            await redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
            await redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');
            await this.queueFlowService.recalculateEstimatedServeTimes(queue.serviceId, queue.date, queue.timeSlot);
            this.logger.log(`Recalculated estimated serve times for all queues after completing queue ${queueIdNum}`);
            return {
                status: 'success',
                message: 'Queue marked as completed and positions reordered',
                data: updatedQueue
            };
        }
        catch (error) {
            this.logger.error(`Error completing queue: ${error.message}`, error.stack);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to mark queue as completed',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async confirmPresence(queueId, body) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum) || queueIdNum <= 0) {
                throw new common_1.BadRequestException('Invalid queue ID format');
            }
            const updatedQueue = await this.queueFlowService.handlePresenceConfirmation(queueIdNum, body.isPresent);
            return {
                status: 'success',
                message: body.isPresent ? 'Presence confirmed' : 'Marked as not present',
                data: updatedQueue
            };
        }
        catch (error) {
            this.logger.error(`Error confirming presence: ${error.message}`, error.stack);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to confirm presence',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async cancelQueue(queueId) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum) || queueIdNum <= 0) {
                throw new common_1.BadRequestException('Invalid queue ID format');
            }
            const queue = await this.partnerService['queueRepository'].findOne({
                where: { id: queueIdNum },
                relations: ['service']
            });
            if (!queue) {
                throw new common_1.NotFoundException(`Queue with ID ${queueIdNum} not found`);
            }
            const allQueues = await this.partnerService['queueRepository'].find({
                where: {
                    serviceId: queue.serviceId,
                    date: queue.date,
                    timeSlot: queue.timeSlot,
                    status: (0, typeorm_1.In)(['waiting', 'serving']),
                },
                order: {
                    isVIP: 'DESC',
                    position: 'ASC',
                    createdAt: 'ASC'
                }
            });
            const servingQueues = allQueues.filter(q => q.status === 'serving');
            const waitingQueues = allQueues.filter(q => q.status === 'waiting');
            this.logger.log(`Found ${servingQueues.length} serving queues and ${waitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);
            queue.status = 'cancelled';
            queue.statusUpdatedAt = new Date();
            queue.position = -1;
            const updatedQueue = await this.partnerService['queueRepository'].save(queue);
            const redisService = this.partnerService['redisService'];
            const queueData = await redisService.getQueue(queueIdNum.toString()) || {};
            queueData.status = 'cancelled';
            queueData.position = -1;
            await redisService.saveQueue(queueIdNum.toString(), queueData);
            await this.partnerService.updateQueueStatus(queueIdNum, 'cancelled', true);
            if (queue.status === 'waiting' || queue.status === 'checked-in') {
                const remainingWaitingQueues = waitingQueues.filter(q => q.id !== queueIdNum);
                const positions = {};
                let position = 0;
                const vipQueues = remainingWaitingQueues.filter(q => q.isVIP);
                const normalQueues = remainingWaitingQueues.filter(q => !q.isVIP);
                this.logger.log(`Reordering positions: ${vipQueues.length} VIP queues and ${normalQueues.length} normal queues`);
                for (const q of vipQueues) {
                    position++;
                    q.position = position;
                    positions[q.id] = position;
                    const qData = await redisService.getQueue(q.id) || {};
                    qData.position = position;
                    await redisService.saveQueue(q.id.toString(), qData);
                }
                for (const q of normalQueues) {
                    position++;
                    q.position = position;
                    positions[q.id] = position;
                    const qData = await redisService.getQueue(q.id) || {};
                    qData.position = position;
                    await redisService.saveQueue(q.id.toString(), qData);
                }
                const allRemainingQueues = [...vipQueues, ...normalQueues];
                this.logger.log(`Reordered positions for ${allRemainingQueues.length} queues after cancelling queue ${queueIdNum}`);
                if (allRemainingQueues.length > 0) {
                    await this.partnerService['queueRepository'].save(allRemainingQueues);
                }
            }
            else {
                this.logger.log(`Queue ${queueIdNum} was not in waiting status, no need to reorder positions`);
            }
            const dateStr = new Date(queue.date).toISOString().split('T')[0];
            const positionsToSave = {};
            if (queue.status === 'waiting' || queue.status === 'checked-in') {
                let vipQueuesLocal = [];
                let normalQueuesLocal = [];
                const remainingWaitingQueues = waitingQueues.filter(q => q.id !== queueIdNum);
                vipQueuesLocal = remainingWaitingQueues.filter(q => q.isVIP);
                normalQueuesLocal = remainingWaitingQueues.filter(q => !q.isVIP);
                const allRemainingQueues = [...vipQueuesLocal, ...normalQueuesLocal];
                for (const q of allRemainingQueues) {
                    positionsToSave[q.id] = q.position;
                }
            }
            await redisService.saveQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot, positionsToSave);
            await redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);
            await redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
            await redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');
            await this.queueFlowService.recalculateEstimatedServeTimes(queue.serviceId, queue.date, queue.timeSlot);
            this.logger.log(`Recalculated estimated serve times for all queues after cancelling queue ${queueIdNum}`);
            return {
                status: 'success',
                message: 'Queue cancelled and positions reordered',
                data: updatedQueue
            };
        }
        catch (error) {
            this.logger.error(`Error cancelling queue: ${error.message}`, error.stack);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to cancel queue',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async markQueueAsNoShow(queueId) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum) || queueIdNum <= 0) {
                throw new common_1.BadRequestException('Invalid queue ID format');
            }
            const queue = await this.partnerService['queueRepository'].findOne({
                where: { id: queueIdNum },
                relations: ['service']
            });
            if (!queue) {
                throw new common_1.NotFoundException(`Queue with ID ${queueIdNum} not found`);
            }
            const allQueues = await this.partnerService['queueRepository'].find({
                where: {
                    serviceId: queue.serviceId,
                    date: queue.date,
                    timeSlot: queue.timeSlot,
                    status: (0, typeorm_1.In)(['waiting', 'serving']),
                },
                order: {
                    isVIP: 'DESC',
                    position: 'ASC',
                    createdAt: 'ASC'
                }
            });
            const servingQueues = allQueues.filter(q => q.status === 'serving');
            const waitingQueues = allQueues.filter(q => q.status === 'waiting');
            this.logger.log(`Found ${servingQueues.length} serving queues and ${waitingQueues.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);
            queue.status = 'no-show';
            queue.statusUpdatedAt = new Date();
            queue.position = -1;
            const updatedQueue = await this.partnerService['queueRepository'].save(queue);
            const redisService = this.partnerService['redisService'];
            const queueData = await redisService.getQueue(queueIdNum.toString()) || {};
            queueData.status = 'no-show';
            queueData.position = -1;
            await redisService.saveQueue(queueIdNum.toString(), queueData);
            console.log(`Updating queue ${queueIdNum} to no-show status using RedisService`);
            await this.partnerService.updateQueueStatus(queueIdNum, 'no-show', true);
            const verifiedData = await redisService.getQueue(queueIdNum);
            console.log(`Verified queue ${queueIdNum} Redis status: ${verifiedData?.status}`);
            if (queue.status === 'waiting' || queue.status === 'checked-in') {
                const remainingWaitingQueues = waitingQueues.filter(q => q.id !== queueIdNum);
                const positions = {};
                let position = 0;
                const vipQueues = remainingWaitingQueues.filter(q => q.isVIP);
                const normalQueues = remainingWaitingQueues.filter(q => !q.isVIP);
                this.logger.log(`Reordering positions: ${vipQueues.length} VIP queues and ${normalQueues.length} normal queues`);
                for (const q of vipQueues) {
                    position++;
                    q.position = position;
                    positions[q.id] = position;
                    const qData = await redisService.getQueue(q.id) || {};
                    qData.position = position;
                    await redisService.saveQueue(q.id.toString(), qData);
                }
                for (const q of normalQueues) {
                    position++;
                    q.position = position;
                    positions[q.id] = position;
                    const qData = await redisService.getQueue(q.id) || {};
                    qData.position = position;
                    await redisService.saveQueue(q.id.toString(), qData);
                }
                const allRemainingQueues = [...vipQueues, ...normalQueues];
                this.logger.log(`Reordered positions for ${allRemainingQueues.length} queues after marking queue ${queueIdNum} as no-show`);
                if (allRemainingQueues.length > 0) {
                    await this.partnerService['queueRepository'].save(allRemainingQueues);
                }
            }
            else {
                this.logger.log(`Queue ${queueIdNum} was not in waiting status, no need to reorder positions`);
            }
            const dateStr = new Date(queue.date).toISOString().split('T')[0];
            const positionsToSave = {};
            if (queue.status === 'waiting' || queue.status === 'checked-in') {
                let vipQueuesLocal = [];
                let normalQueuesLocal = [];
                const remainingWaitingQueues = waitingQueues.filter(q => q.id !== queueIdNum);
                vipQueuesLocal = remainingWaitingQueues.filter(q => q.isVIP);
                normalQueuesLocal = remainingWaitingQueues.filter(q => !q.isVIP);
                const allRemainingQueues = [...vipQueuesLocal, ...normalQueuesLocal];
                for (const q of allRemainingQueues) {
                    positionsToSave[q.id] = q.position;
                }
            }
            await redisService.saveQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot, positionsToSave);
            await redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);
            await redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
            await redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');
            await this.queueFlowService.recalculateEstimatedServeTimes(queue.serviceId, queue.date, queue.timeSlot);
            this.logger.log(`Recalculated estimated serve times for all queues after marking queue ${queueIdNum} as no-show`);
            return {
                status: 'success',
                message: 'Queue marked as no-show and positions reordered',
                data: updatedQueue
            };
        }
        catch (error) {
            this.logger.error(`Error marking queue as no-show: ${error.message}`, error.stack);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to mark queue as no-show',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async moveQueueToEnd(queueId) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum) || queueIdNum <= 0) {
                throw new common_1.BadRequestException('Invalid queue ID format');
            }
            const queue = await this.partnerService['queueRepository'].findOne({
                where: { id: queueIdNum },
                relations: ['service']
            });
            if (!queue) {
                throw new common_1.NotFoundException(`Queue with ID ${queueIdNum} not found`);
            }
            const allQueuesForSlot = await this.partnerService['queueRepository'].find({
                where: {
                    serviceId: queue.serviceId,
                    date: queue.date,
                    timeSlot: queue.timeSlot,
                    status: 'waiting',
                },
                order: {
                    position: 'ASC',
                    createdAt: 'ASC'
                }
            });
            this.logger.log(`Found ${allQueuesForSlot.length} waiting queues for service ${queue.serviceId}, date ${queue.date}, timeSlot ${queue.timeSlot}`);
            const filteredQueues = allQueuesForSlot.filter(q => q.id !== queueIdNum);
            queue.status = 'waiting';
            queue.currentlyServing = false;
            queue.servingStartedAt = null;
            queue.inGracePeriod = false;
            queue.graceStartedAt = null;
            queue.confirmedPresence = false;
            const redisService = this.partnerService['redisService'];
            const positionsToSave = {};
            let position = 0;
            for (const q of filteredQueues) {
                position++;
                q.position = position;
                positionsToSave[q.id] = position;
                const qData = await redisService.getQueue(q.id) || {};
                qData.position = position;
                await redisService.saveQueue(q.id.toString(), qData);
            }
            position++;
            queue.position = position;
            positionsToSave[queue.id] = position;
            this.logger.log(`Moving queue ${queueIdNum} to end of line with new position ${position}`);
            await this.partnerService['queueRepository'].save(filteredQueues);
            const updatedQueue = await this.partnerService['queueRepository'].save(queue);
            let queueData = await redisService.getQueue(queueIdNum) || {};
            queueData = {
                ...queueData,
                position: position,
                status: 'waiting',
                currentlyServing: false,
                servingStartedAt: null,
                inGracePeriod: false,
                graceStartedAt: null,
                confirmedPresence: false,
                updatedAt: new Date().toISOString()
            };
            await redisService.saveQueue(queueIdNum.toString(), queueData);
            const dateStr = new Date(queue.date).toISOString().split('T')[0];
            await redisService.saveQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot, positionsToSave);
            await redisService.invalidateQueuePosition(queue.serviceId.toString(), dateStr, queue.timeSlot);
            await redisService.invalidateServiceQueues(queue.serviceId.toString(), dateStr);
            await redisService.invalidateServiceQueues(queue.serviceId.toString(), 'all');
            await this.queueFlowService.recalculateEstimatedServeTimes(queue.serviceId, queue.date, queue.timeSlot);
            this.logger.log(`Recalculated estimated serve times for all queues after moving queue ${queueIdNum} to end of line`);
            return {
                status: 'success',
                message: 'Queue moved to end of line',
                data: {
                    ...updatedQueue,
                    newPosition: position
                }
            };
        }
        catch (error) {
            console.error('Error moving queue to end of line:', error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to move queue to end of line',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async startServingQueue(queueId) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum)) {
                throw new common_1.BadRequestException('Invalid queue ID');
            }
            const result = await this.queueFlowService.startServing(queueIdNum);
            console.log(`Started serving queue ${queueIdNum}, servingStartedAt: ${result.servingStartedAt?.toISOString() || 'not set'}`);
            return {
                status: 'success',
                message: 'Started serving the queue member',
                data: result
            };
        }
        catch (error) {
            console.error(`Error starting service for queue ${queueId}:`, error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to start serving queue',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async startGracePeriod(queueId) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum) || queueIdNum <= 0) {
                throw new common_1.BadRequestException('Invalid queue ID format');
            }
            const queue = await this.partnerService['queueRepository'].findOne({
                where: { id: queueIdNum }
            });
            if (!queue) {
                throw new common_1.NotFoundException(`Queue with ID ${queueIdNum} not found`);
            }
            const result = await this.schedulerService.startGracePeriod(queueIdNum, queue.serviceId);
            return {
                status: 'success',
                message: 'Started grace period for queue member',
                data: result
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to start grace period',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getGracePeriodStatus(queueId) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum) || queueIdNum <= 0) {
                throw new common_1.BadRequestException('Invalid queue ID format');
            }
            const result = await this.partnerService.getGracePeriodStatus(queueIdNum);
            return {
                status: 'success',
                data: result
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to get grace period status',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getEstimatedWaitTime(queueId) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum)) {
                throw new common_1.BadRequestException('Invalid queue ID');
            }
            this.logger.log(`Retrieving estimated wait time for queue: ${queueId}`);
            const waitTimeData = await this.queueFlowService.getEstimatedWaitTime(queueIdNum);
            return {
                status: 'success',
                data: {
                    queueId: queueIdNum,
                    waitTimeMinutes: waitTimeData.waitTimeMinutes,
                    waitTimeStatus: waitTimeData.waitTimeStatus,
                    estimatedServeTime: waitTimeData.estimatedServeTime.toISOString()
                }
            };
        }
        catch (error) {
            this.logger.error(`Error getting estimated wait time for queue ${queueId}:`, error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to get estimated wait time',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getActualServiceTime(queueId) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum)) {
                throw new common_1.BadRequestException('Invalid queue ID');
            }
            this.logger.log(`Retrieving actual service time for queue: ${queueId}`);
            const serviceTimeData = await this.queueFlowService.getActualServiceTime(queueIdNum);
            return {
                status: 'success',
                data: {
                    queueId: queueIdNum,
                    serviceTimeMinutes: serviceTimeData.serviceTimeMinutes,
                    servingStartedAt: serviceTimeData.servingStartedAt?.toISOString() || null,
                    statusUpdatedAt: serviceTimeData.statusUpdatedAt?.toISOString() || null,
                    status: serviceTimeData.status
                }
            };
        }
        catch (error) {
            this.logger.error(`Error getting actual service time for queue ${queueId}:`, error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to get actual service time',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async moveQueueToEndOfLine(queueId) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum)) {
                throw new common_1.BadRequestException('Invalid queue ID');
            }
            this.logger.log(`Moving queue ${queueId} to the end of the line`);
            const updatedQueue = await this.queueFlowService.moveToEndOfLine(queueIdNum);
            return {
                status: 'success',
                message: 'Queue moved to the end of the line',
                data: {
                    queueId: updatedQueue.id,
                    position: updatedQueue.position,
                    status: updatedQueue.status,
                    estimatedServeTime: updatedQueue.estimatedServeTime?.toISOString() || null
                }
            };
        }
        catch (error) {
            this.logger.error(`Error moving queue ${queueId} to the end of the line:`, error);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to move queue to the end of the line',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getServingStatus(queueId, hasSubUnits, subUnitId) {
        try {
            const queueIdNum = parseInt(queueId, 10);
            if (isNaN(queueIdNum)) {
                throw new common_1.BadRequestException('Invalid queue ID');
            }
            console.log(`Retrieving serving status for queue: ${queueId}, hasSubUnits: ${hasSubUnits}, subUnitId: ${subUnitId}`);
            try {
                const redisData = await this.partnerService['redisService'].getQueue(queueId);
                if (redisData &&
                    redisData.status === 'serving' &&
                    redisData.currentlyServing === true &&
                    redisData.servingStartedAt) {
                    console.log(`Found cached serving data in Redis for queue ${queueId}`);
                    const serviceSetup = await this.partnerService['setupRepository'].findOne({
                        where: { service: { id: redisData.serviceId } }
                    });
                    let servingTimeMinutes = 15;
                    const hasSubUnitsValue = hasSubUnits === 'true' || redisData.hasSubUnits === true;
                    const subUnitIdValue = subUnitId || redisData.subUnitId || '0';
                    console.log(`Processing queue with hasSubUnits: ${hasSubUnitsValue}, subUnitId: ${subUnitIdValue}`);
                    if (hasSubUnitsValue &&
                        serviceSetup?.setupData?.hasSubUnits &&
                        Array.isArray(serviceSetup.setupData.subUnits) &&
                        serviceSetup.setupData.subUnits.length > 0) {
                        const subUnitIndex = parseInt(subUnitIdValue, 10);
                        const validIndex = isNaN(subUnitIndex) ? 0 : Math.min(subUnitIndex, serviceSetup.setupData.subUnits.length - 1);
                        const subUnit = serviceSetup.setupData.subUnits[validIndex];
                        if (subUnit?.avgServeTime) {
                            servingTimeMinutes = parseInt(subUnit.avgServeTime, 10);
                            console.log(`Using subunit ${validIndex} serve time: ${servingTimeMinutes} minutes`);
                        }
                    }
                    else if (serviceSetup?.setupData?.servingTime) {
                        servingTimeMinutes = parseInt(serviceSetup.setupData.servingTime, 10);
                        console.log(`Using service setup serve time: ${servingTimeMinutes} minutes`);
                    }
                    if (redisData.serveTime) {
                        servingTimeMinutes = redisData.serveTime;
                        console.log(`Using queue's stored serve time from Redis: ${servingTimeMinutes} minutes`);
                    }
                    else if (redisData.servingTimeMinutes) {
                        servingTimeMinutes = redisData.servingTimeMinutes;
                        console.log(`Using servingTimeMinutes from Redis: ${servingTimeMinutes} minutes`);
                    }
                    const servingStartedAt = new Date(redisData.servingStartedAt);
                    const estimatedEndTime = new Date(servingStartedAt);
                    estimatedEndTime.setMinutes(estimatedEndTime.getMinutes() + servingTimeMinutes);
                    const now = new Date();
                    const remainingMs = Math.max(0, estimatedEndTime.getTime() - now.getTime());
                    const remainingSeconds = Math.floor(remainingMs / 1000);
                    const remainingMinutes = Math.floor(remainingSeconds / 60);
                    console.log(`Queue ${queueId} serving time calculation from Redis:`);
                    console.log(`Started: ${servingStartedAt.toISOString()}`);
                    console.log(`End time: ${estimatedEndTime.toISOString()}`);
                    console.log(`Serving time: ${servingTimeMinutes} minutes`);
                    console.log(`Remaining: ${remainingMinutes} minutes ${remainingSeconds % 60} seconds`);
                    return {
                        status: 'success',
                        data: {
                            queueId: redisData.id,
                            serviceId: redisData.serviceId,
                            isServing: true,
                            servingStartedAt: redisData.servingStartedAt,
                            estimatedEndTime: estimatedEndTime.toISOString(),
                            servingTimeMinutes: servingTimeMinutes,
                            remainingMinutes: remainingMinutes,
                            remainingSeconds: remainingSeconds,
                            hasSubUnits: hasSubUnitsValue,
                            subUnitId: subUnitIdValue
                        }
                    };
                }
            }
            catch (error) {
                console.error(`Error getting Redis serving data for queue ${queueId}:`, error);
            }
            const queue = await this.partnerService['queueRepository'].findOne({
                where: { id: queueIdNum },
                relations: ['service']
            });
            if (!queue) {
                throw new common_1.NotFoundException(`Queue with ID ${queueIdNum} not found`);
            }
            const serviceSetup = await this.partnerService['setupRepository'].findOne({
                where: { service: { id: queue.serviceId } }
            });
            let servingTimeMinutes = 15;
            const hasSubUnitsValue = hasSubUnits === 'true' || queue.hasSubUnits === true;
            const subUnitIdValue = subUnitId || queue.subUnitId || '0';
            console.log(`Processing queue from DB with hasSubUnits: ${hasSubUnitsValue}, subUnitId: ${subUnitIdValue}`);
            if (hasSubUnitsValue &&
                serviceSetup?.setupData?.hasSubUnits &&
                Array.isArray(serviceSetup.setupData.subUnits) &&
                serviceSetup.setupData.subUnits.length > 0) {
                const subUnitIndex = parseInt(subUnitIdValue, 10);
                const validIndex = isNaN(subUnitIndex) ? 0 : Math.min(subUnitIndex, serviceSetup.setupData.subUnits.length - 1);
                const subUnit = serviceSetup.setupData.subUnits[validIndex];
                if (subUnit?.avgServeTime) {
                    servingTimeMinutes = parseInt(subUnit.avgServeTime, 10);
                    console.log(`Using subunit ${validIndex} serve time from DB: ${servingTimeMinutes} minutes`);
                }
            }
            else if (serviceSetup?.setupData?.servingTime) {
                servingTimeMinutes = parseInt(serviceSetup.setupData.servingTime, 10);
                console.log(`Using service setup serve time from DB: ${servingTimeMinutes} minutes`);
            }
            const isServing = queue.status === 'serving' && queue.currentlyServing;
            if (!isServing || !queue.servingStartedAt) {
                return {
                    status: 'success',
                    data: {
                        queueId: queue.id,
                        serviceId: queue.serviceId,
                        isServing: false,
                        servingStartedAt: null,
                        estimatedEndTime: null,
                        servingTimeMinutes: servingTimeMinutes,
                        remainingMinutes: 0,
                        remainingSeconds: 0,
                        hasSubUnits: hasSubUnitsValue,
                        subUnitId: subUnitIdValue
                    }
                };
            }
            const servingStartedAt = new Date(queue.servingStartedAt);
            const estimatedEndTime = new Date(servingStartedAt);
            estimatedEndTime.setMinutes(estimatedEndTime.getMinutes() + servingTimeMinutes);
            const now = new Date();
            const remainingMs = Math.max(0, estimatedEndTime.getTime() - now.getTime());
            const remainingSeconds = Math.floor(remainingMs / 1000);
            const remainingMinutes = Math.floor(remainingSeconds / 60);
            console.log(`Queue ${queueId} serving time calculation from DB:`);
            console.log(`Started: ${servingStartedAt.toISOString()}`);
            console.log(`End time: ${estimatedEndTime.toISOString()}`);
            console.log(`Serving time: ${servingTimeMinutes} minutes`);
            console.log(`Remaining: ${remainingMinutes} minutes ${remainingSeconds % 60} seconds`);
            try {
                const redisData = {
                    id: queue.id,
                    serviceId: queue.serviceId,
                    status: 'serving',
                    currentlyServing: true,
                    servingStartedAt: queue.servingStartedAt.toISOString(),
                    estimatedEndTime: estimatedEndTime.toISOString(),
                    servingTime: servingTimeMinutes,
                    servingTimeMinutes: servingTimeMinutes,
                    remainingMinutes: remainingMinutes,
                    remainingSeconds: remainingSeconds,
                    statusUpdatedAt: new Date().toISOString(),
                    calculatedAt: new Date().toISOString(),
                    hasSubUnits: hasSubUnitsValue,
                    subUnitId: subUnitIdValue
                };
                await this.partnerService['redisService'].set(`queue:${queueId}`, redisData, { ex: 86400 });
            }
            catch (error) {
                console.error(`Error updating Redis serving data for queue ${queueId}:`, error);
            }
            return {
                status: 'success',
                data: {
                    queueId: queue.id,
                    serviceId: queue.serviceId,
                    isServing: true,
                    servingStartedAt: queue.servingStartedAt,
                    estimatedEndTime: estimatedEndTime.toISOString(),
                    servingTimeMinutes: servingTimeMinutes,
                    remainingMinutes: remainingMinutes,
                    remainingSeconds: remainingSeconds,
                    hasSubUnits: hasSubUnitsValue,
                    subUnitId: subUnitIdValue
                }
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to get serving status',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async triggerAutoGracePeriods() {
        try {
            return {
                status: 'success',
                message: 'Auto grace periods have been disabled. Please use the manual start-grace-period endpoint instead.'
            };
        }
        catch (error) {
            throw new common_1.HttpException({ status: 'error', message: error.message || 'Auto grace periods disabled' }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async refreshServingQueues() {
        try {
            this.logger.log('Manual refresh of serving queues TTL triggered');
            await this.schedulerService.refreshServingQueuesTTL();
            return {
                status: 'success',
                message: 'Serving queues TTL refresh has been triggered'
            };
        }
        catch (error) {
            this.logger.error(`Error in manual refresh of serving queues TTL: ${error.message}`, error.stack);
            throw new common_1.HttpException({
                status: 'error',
                message: error.message || 'Failed to refresh serving queues TTL'
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.PartnerController = PartnerController;
__decorate([
    (0, common_1.Post)('register'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "registerPartner", null);
__decorate([
    (0, common_1.Post)('register-service'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "registerService", null);
__decorate([
    (0, common_1.Get)('service-status/:serviceId'),
    __param(0, (0, common_1.Param)('serviceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "getServiceStatus", null);
__decorate([
    (0, common_1.Post)('service-setup/:serviceId'),
    __param(0, (0, common_1.Param)('serviceId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "saveServiceSetup", null);
__decorate([
    (0, common_1.Get)('service-setup/:serviceId'),
    __param(0, (0, common_1.Param)('serviceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "getServiceSetup", null);
__decorate([
    (0, common_1.Get)('service-details/:email'),
    __param(0, (0, common_1.Param)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "getServiceDetails", null);
__decorate([
    (0, common_1.Put)('update-service'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "updateService", null);
__decorate([
    (0, common_1.Get)('service-completion/:email'),
    __param(0, (0, common_1.Param)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "checkServiceCompletion", null);
__decorate([
    (0, common_1.Put)('accept-terms/:email'),
    __param(0, (0, common_1.Param)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "acceptTerms", null);
__decorate([
    (0, common_1.Get)('verification-status/:email'),
    __param(0, (0, common_1.Param)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "getVerificationStatusByEmail", null);
__decorate([
    (0, common_1.Put)('toggle-service/:email'),
    __param(0, (0, common_1.Param)('email')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "toggleServiceStatus", null);
__decorate([
    (0, common_1.Get)('service-status/:email'),
    __param(0, (0, common_1.Param)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "getServiceOpenStatus", null);
__decorate([
    (0, common_1.Post)('bank-details/:serviceId'),
    __param(0, (0, common_1.Param)('serviceId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "saveBankDetails", null);
__decorate([
    (0, common_1.Get)('bank-details/:serviceId'),
    __param(0, (0, common_1.Param)('serviceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "getBankDetails", null);
__decorate([
    (0, common_1.Post)('leave-days/:serviceId'),
    __param(0, (0, common_1.Param)('serviceId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "saveLeaveDays", null);
__decorate([
    (0, common_1.Get)('leave-days/:serviceId'),
    __param(0, (0, common_1.Param)('serviceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "getLeaveDays", null);
__decorate([
    (0, common_1.Get)('services'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "getAllServices", null);
__decorate([
    (0, common_1.Get)('services/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "getServiceById", null);
__decorate([
    (0, common_1.Get)('services/:id/reviews'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('offset')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "getServiceReviews", null);
__decorate([
    (0, common_1.Post)('services/:id/reviews'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "addReview", null);
__decorate([
    (0, common_1.Get)('services/:id/time-slots/:date'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('date')),
    __param(2, (0, common_1.Query)('subUnitId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "getServiceTimeSlots", null);
__decorate([
    (0, common_1.Get)('test-service-setup/:serviceId'),
    __param(0, (0, common_1.Param)('serviceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "testServiceSetup", null);
__decorate([
    (0, common_1.Put)('queues/:queueId/complete'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "completeQueue", null);
__decorate([
    (0, common_1.Put)('queues/:queueId/confirm-presence'),
    __param(0, (0, common_1.Param)('queueId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "confirmPresence", null);
__decorate([
    (0, common_1.Put)('queues/:queueId/cancel'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "cancelQueue", null);
__decorate([
    (0, common_1.Put)('queues/:queueId/no-show'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "markQueueAsNoShow", null);
__decorate([
    (0, common_1.Put)('queues/:queueId/move-to-end'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "moveQueueToEnd", null);
__decorate([
    (0, common_1.Put)('queues/:queueId/start-serving'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "startServingQueue", null);
__decorate([
    (0, common_1.Put)('queues/:queueId/start-grace-period'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "startGracePeriod", null);
__decorate([
    (0, common_1.Get)('queues/:queueId/grace-period-status'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "getGracePeriodStatus", null);
__decorate([
    (0, common_1.Get)('queues/:queueId/estimated-wait-time'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "getEstimatedWaitTime", null);
__decorate([
    (0, common_1.Get)('queues/:queueId/actual-service-time'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "getActualServiceTime", null);
__decorate([
    (0, common_1.Put)('queues/:queueId/move-to-end'),
    __param(0, (0, common_1.Param)('queueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "moveQueueToEndOfLine", null);
__decorate([
    (0, common_1.Get)('queues/:queueId/serving-status'),
    __param(0, (0, common_1.Param)('queueId')),
    __param(1, (0, common_1.Query)('hasSubUnits')),
    __param(2, (0, common_1.Query)('subUnitId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "getServingStatus", null);
__decorate([
    (0, common_1.Post)('trigger-auto-grace-periods'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "triggerAutoGracePeriods", null);
__decorate([
    (0, common_1.Get)('refresh-serving-queues'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PartnerController.prototype, "refreshServingQueues", null);
exports.PartnerController = PartnerController = PartnerController_1 = __decorate([
    (0, common_1.Controller)('partner'),
    __metadata("design:paramtypes", [partner_service_1.PartnerService,
        scheduler_service_1.SchedulerService,
        queue_flow_service_1.QueueFlowService])
], PartnerController);
//# sourceMappingURL=partner.controller.js.map