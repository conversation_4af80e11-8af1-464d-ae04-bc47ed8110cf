import { Service } from './service.entity';
export declare class Queue {
    id: number;
    service: Service;
    serviceId: number;
    status: string;
    isVIP: boolean;
    date: Date;
    timeSlot: string;
    userId: string;
    userName: string;
    uniqueSlotId: string;
    isCheckedIn: boolean;
    graceStartedAt: Date | null;
    confirmedPresence: boolean;
    inGracePeriod: boolean;
    currentlyServing: boolean;
    servingStartedAt: Date | null;
    statusUpdatedAt: Date | null;
    position: number;
    initialPositionAtJoin: number;
    estimatedServeTime: Date | null;
    waitTimeStatus: string;
    hasSubUnits: boolean;
    subUnitId: string;
    subUnitName: string;
    serveTime: number;
    createdAt: Date;
}
