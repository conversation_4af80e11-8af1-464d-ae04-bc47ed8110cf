import React, { useEffect, useState } from 'react';
import { View, Text, Image, SafeAreaView, StatusBar, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import * as Location from 'expo-location';
import { images } from '@/constants';
import ButtonBlueMain from '@/components/ButtonBlueMain';

const AllowLocationScreen = () => {
  const [permissionStatus, setPermissionStatus] = useState<Location.PermissionStatus | null>(null);

  const handleLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      setPermissionStatus(status);
      if (status === 'granted') {
        router.replace('/(location)/select-location');
      }
    } catch (error) {
      console.log('Error requesting location permission:', error);
    }
  };

  useEffect(() => {
    // Check initial permission status
    (async () => {
      const { status } = await Location.getForegroundPermissionsAsync();
      setPermissionStatus(status);
      if (status === 'granted') {
        router.replace('/(location)/select-location');
      }
    })();
  }, []);

  return (
    <SafeAreaView className="flex-1 bg-white  items-center px-6">
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      <Image 
        source={images.location}
        resizeMode='cover'
        className="w-[550px] h-[550px] mb-10 mt-7"
      />
      <Text className="font-poppins-medium text-[24px] text-center mb-2">
        Enable Location Services
      </Text>
      <View className='w-[300px]'>
      <Text className="font-poppins-regular text-secondary-600 text-center text-sm mb-20">
        We need your location to show you nearby queues and provide better service
      </Text>
      </View>
      <ButtonBlueMain
        label="Allow Location Access"
        onPress={handleLocationPermission}
        bgVariant="primary"
        textVariant="primary"
        className='w-[350px]'
        textClassName='text-[13px] '
        icon={images.myLocation}
      />
    </SafeAreaView>
  );
};

export default AllowLocationScreen;
