{"expo": {"name": "<PERSON>ue<PERSON>ree-Partner", "slug": "<PERSON>ue<PERSON>ree-Partner", "version": "1.0.0", "orientation": "portrait", "scheme": "myapp", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "runtimeVersion": {"policy": "appVersion"}, "bundleIdentifier": "com.app.queuefreepartner", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "resizeMode": "contain", "backgroundColor": "#000000"}, "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.RECORD_AUDIO"], "package": "com.selfie_jones.QueueFreePartner", "runtimeVersion": "1.0.0"}, "web": {"bundler": "metro", "output": "server", "favicon": "./assets/images/icon.png"}, "plugins": ["expo-router", "expo-font", "expo-secure-store", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow QueueFree to use your location."}], ["expo-image-picker", {"photosPermission": "Allow QueueFree to access your photos to upload service images.", "cameraPermission": "Allow QueueFree to access your camera to take service photos."}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "9357d87e-cb06-4386-9bdc-45a0eeda35de"}}, "updates": {"url": "https://u.expo.dev/9357d87e-cb06-4386-9bdc-45a0eeda35de"}, "owner": "selfie_jones"}}