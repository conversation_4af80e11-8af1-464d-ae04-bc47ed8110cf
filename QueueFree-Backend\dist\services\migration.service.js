"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MigrationService = void 0;
const common_1 = require("@nestjs/common");
const redis_service_1 = require("./redis/redis.service");
let MigrationService = class MigrationService {
    constructor(redisService) {
        this.redisService = redisService;
    }
    async onModuleInit() {
        console.log('Running Redis key migration on application startup...');
        try {
            await this.redisService.migrateAllQueueKeys();
            const oldFormatKeys = await this.redisService.getKeys('queue:*:status:*');
            if (oldFormatKeys && oldFormatKeys.length > 0) {
                console.log(`Found ${oldFormatKeys.length} old format keys after migration. Cleaning up...`);
                for (const key of oldFormatKeys) {
                    await this.redisService.del(key);
                    console.log(`Deleted old format key: ${key}`);
                }
            }
            console.log('Redis key migration completed successfully');
        }
        catch (error) {
            console.error('Error during Redis key migration:', error);
        }
    }
};
exports.MigrationService = MigrationService;
exports.MigrationService = MigrationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [redis_service_1.RedisService])
], MigrationService);
//# sourceMappingURL=migration.service.js.map