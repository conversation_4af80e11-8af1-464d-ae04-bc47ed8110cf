import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  StatusBar,
  SafeAreaView,
  TextInput,
  Keyboard,
  Modal,
  Alert,
  Switch,
  Platform,
  ActivityIndicator,
} from "react-native";
import { useRouter } from "expo-router";
import { useUser } from "@clerk/clerk-expo";
import * as SecureStore from "expo-secure-store";
import { images } from "@/constants";
import ButtonBlueMain from "@/components/ButtonBlueMain";
import DateTimePicker from "@react-native-community/datetimepicker";
import * as Updates from 'expo-updates';
import AsyncStorage from '@react-native-async-storage/async-storage';

const weekDays = [
  { short: "S", full: "Sunday" },
  { short: "M", full: "Monday" },
  { short: "T", full: "Tuesday" },
  { short: "W", full: "Wednesday" },
  { short: "T", full: "Thursday" },
  { short: "F", full: "Friday" },
  { short: "S", full: "Saturday" },
];

interface TimeSlot {
  start: string;
  end: string;
}

interface TimeSlotsByDay {
  [day: string]: TimeSlot[];
}

interface SubUnit {
  name: string;
  availableHours: {
    [key: string]: [string, string];
  };
  dayWiseAvailableHours?: {
    [day: string]: TimeSlot[];
  };
  avgServeTime: string;
  pricePerHead: string;
  selectedDays?: string[];
  useDayWiseTimeSlots?: boolean;
  timeSlots?: TimeSlot[];
}

interface TimePickerProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (time: string) => void;
  initialTime?: string;
}

const TimePicker = ({
  visible,
  onClose,
  onSelect,
  initialTime = "09:00",
}: TimePickerProps) => {
  // Parse initial time
  const parseTime = (timeStr: string) => {
    try {
      let hour = 9;
      let minute = 0;
      let ampm = "AM";

      // Parse "09:00" or "09:00 AM" format
      if (timeStr) {
        const parts = timeStr.trim().split(" ");
        const timePart = parts[0];
        const [h, m] = timePart.split(":").map((num) => parseInt(num, 10));

        if (!isNaN(h)) hour = h;
        if (!isNaN(m)) minute = m;

        if (parts.length > 1) {
          ampm = parts[1].toUpperCase();
        }
      }

      // Convert to 24-hour format for the Date object
      let hours24 = hour;
      if (ampm === "PM" && hour < 12) hours24 += 12;
      if (ampm === "AM" && hour === 12) hours24 = 0;

      const date = new Date();
      date.setHours(hours24, minute, 0, 0);
      return date;
    } catch (err) {
      // Default to 9am if parsing fails
      const date = new Date();
      date.setHours(9, 0, 0, 0);
      return date;
    }
  };

  const [date, setDate] = useState(parseTime(initialTime));
  const [show, setShow] = useState(visible);

  // Update visibility based on props
  useEffect(() => {
    setShow(visible);
    if (visible) {
      setDate(parseTime(initialTime));
    }
  }, [visible, initialTime]);

  const onChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === "android") {
      setShow(false);

      // On Android, if the user cancels, selectedDate will be undefined
      if (!selectedDate) {
        onClose();
        return;
      }
    }

    const currentDate = selectedDate || date;
    setDate(currentDate);

    if (Platform.OS === "ios") {
      // Don't close the picker on iOS, as this is handled by the modal buttons
      return;
      }

    // Format and return the time for Android
    const hours = currentDate.getHours();
    const minutes = currentDate.getMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";
    const hour12 = hours % 12 || 12; // Convert to 12-hour format

    const formattedTime = `${hour12.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")} ${ampm}`;
    onSelect(formattedTime);
  };

  const handleConfirm = () => {
    // Format the time in 12-hour format with AM/PM
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";
    const hour12 = hours % 12 || 12; // Convert to 12-hour format

    const formattedTime = `${hour12.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")} ${ampm}`;
    onSelect(formattedTime);
    onClose();
  };

  if (Platform.OS === "android") {
    // On Android, render the native time picker directly
    return show ? (
      <DateTimePicker
        testID="dateTimePicker"
        value={date}
        mode="time"
        is24Hour={false}
        display="default"
        onChange={onChange}
      />
    ) : null;
  }

  // On iOS, render in a modal
  return (
    <Modal visible={visible} transparent animationType="fade">
      <View className="flex-1 bg-black/50 justify-center items-center">
        <View className="bg-white rounded-3xl p-6 w-[320px]">
          <Text className="text-lg font-[Poppins-Medium] mb-3 text-center">
            Select Time
          </Text>
          <View className="h-[1px] w-full bg-secondary-500/20 mb-6 mt-2" />

          {show && (
            <DateTimePicker
              testID="dateTimePicker"
              value={date}
              mode="time"
              is24Hour={false}
              display="spinner"
              onChange={onChange}
              textColor="#000000"
              themeVariant="light"
              style={{ height: 180, width: "100%" }}
            />
          )}

          <View className="flex-row justify-end mt-4">
            <TouchableOpacity onPress={onClose} className="p-3">
              <Text className="text-gray-500">Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={handleConfirm} className="p-3">
              <Text className="text-primary-500 font-[Poppins-Medium]">
                Confirm
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

interface SetupData {
  selectedDays: string[];
  timeSlots: TimeSlot[];
  timeSlotsByDay?: TimeSlotsByDay;
  useDayWiseTimeSlots: boolean;
  servingTime: string;
  basePrice: string;
  hasSubUnits: boolean;
  subUnits: SubUnit[];
  availableHours?: { [key: string]: Array<{ start: string; end: string }> };
}

interface StandardizedSetupData {
  selectedDays: string[];
  servingTime: string;
  basePrice: string;
  hasSubUnits: boolean;
  useDayWiseTimeSlots?: boolean;
  availableHours?: { [key: string]: Array<{ start: string; end: string }> };
  subUnits?: Array<{
    name: string;
    avgServeTime: string;
    pricePerHead: string;
    selectedDays: string[];
    useDayWiseTimeSlots?: boolean;
    availableHours: { [key: string]: Array<{ start: string; end: string }> };
  }>;
}

export default function ServiceSetupScreen() {
  const router = useRouter();
  const { user } = useUser();
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [selectedDays, setSelectedDays] = useState<string[]>(
    weekDays.slice(1, 6).map((day) => day.full) // Monday to Friday selected by default
  );
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([
    { start: "09:00 AM", end: "05:00 PM" },
  ]);

  // New state for day-wise time slots
  const [useDayWiseTimeSlots, setUseDayWiseTimeSlots] = useState(false);
  const [timeSlotsByDay, setTimeSlotsByDay] = useState<TimeSlotsByDay>({});

  const [servingTime, setServingTime] = useState("30");
  const [basePrice, setBasePrice] = useState("");
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [activeTimeSlot, setActiveTimeSlot] = useState<{
    index: number;
    field: "start" | "end";
    unitIndex?: number;
    day?: string;
  } | null>(null);
  const [showServingTimeInfo, setShowServingTimeInfo] = useState(false);
  const [showSubUnitInfo, setShowSubUnitInfo] = useState(false);
  const [serviceId, setServiceId] = useState<string | null>(null);
  const [verificationStatus, setVerificationStatus] =
    useState<string>("pending");
  const [isEditMode, setIsEditMode] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  // Add loading state for data fetching
  const [isLoading, setIsLoading] = useState(false);

  // New state for sub-units feature
  const [hasSubUnits, setHasSubUnits] = useState(false);
  const [subUnits, setSubUnits] = useState<SubUnit[]>([]);
  const [copiedSubUnit, setCopiedSubUnit] = useState<SubUnit | null>(null);
  const [expandedUnitIndex, setExpandedUnitIndex] = useState<number | null>(
    null
  );

  // Add refs for subunit name inputs
  const subunitNameRefs = useRef<{ [key: string]: any }>({});

  // Track original configuration for change detection
  const [originalHasSubUnits, setOriginalHasSubUnits] = useState<boolean | null>(null);
  const [originalConfig, setOriginalConfig] = useState<any>(null);

  const initializeData = async () => {
    try {
      setIsLoading(true);
      if (!user?.primaryEmailAddress?.emailAddress) {
        console.log("No email found");
        return;
      }

      // Get service details first
      const email = encodeURIComponent(user.primaryEmailAddress.emailAddress);
      const serviceDetailsResponse = await fetch(
        `http://**************:3000/api/partner/service-details/${email}`
      );

      if (!serviceDetailsResponse.ok) {
        throw new Error("Failed to fetch service details");
      }

      const serviceData = await serviceDetailsResponse.json();
      
      // Set service ID and try loading setup data 
      setServiceId(serviceData.id.toString());

      // Try loading from backend first, fall back to local storage
      try {
        await loadExistingSetup(serviceData.id);
      } catch (error) {
        console.log("No backend setup data, using local storage");
        await loadSavedSetupData();
      }
    } catch (error) {
      console.error("Error initializing setup data:", error);
    } finally {
      setIsLoading(false);
    }
  };

const loadExistingSetup = async (serviceId: number) => {
  try {
    setIsLoading(true);
    console.log("Loading setup data for service ID:", serviceId);
    const response = await fetch(
      `http://**************:3000/api/partner/service-setup/${serviceId}`
    );

    if (!response.ok) {
      console.error("Failed to fetch setup data:", await response.text());
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log("Setup API response:", result);

    // Check both hasSetup and data existence
    if (result?.status === "success") {
      if (result.hasSetup && result.data) {
        // Has existing setup - load it
        console.log("Found existing setup data:", result.data);
        
        // Save original configuration for change detection
        setOriginalConfig(JSON.parse(JSON.stringify(result.data)));
        
        // Always load the selectedDays
        setSelectedDays(result.data.selectedDays || weekDays.slice(1, 6).map((day) => day.full));
        setServingTime(result.data.servingTime || "30");
        setBasePrice(result.data.basePrice || "");

        // Handle hasSubUnits flag
        const serviceHasSubUnits = result.data.hasSubUnits === true;
        setHasSubUnits(serviceHasSubUnits);
        setOriginalHasSubUnits(serviceHasSubUnits);

        if (serviceHasSubUnits && Array.isArray(result.data.subUnits)) {
          // Process subunits with the new format
          const processedSubUnits = result.data.subUnits.map((unit: any) => {
            const processedUnit: SubUnit = {
              name: unit.name,
              avgServeTime: unit.avgServeTime || "15",
              pricePerHead: unit.pricePerHead || "150",
              availableHours: {},
              selectedDays: unit.selectedDays || [...result.data.selectedDays],
              // Set useDayWiseTimeSlots from backend data or detect from structure
              useDayWiseTimeSlots: unit.useDayWiseTimeSlots !== undefined ? unit.useDayWiseTimeSlots : false
            };

            // Convert availableHours to UI format
            if (unit.availableHours && typeof unit.availableHours === 'object') {
              // Process availableHours
              processedUnit.availableHours = {};
              
              // Extract selectedDays from availableHours keys if not present
              if (!Array.isArray(processedUnit.selectedDays) || processedUnit.selectedDays.length === 0) {
                processedUnit.selectedDays = Object.keys(unit.availableHours);
              }
              
              // Format for the UI
              Object.keys(unit.availableHours).forEach(day => {
                if (Array.isArray(unit.availableHours[day]) && unit.availableHours[day].length > 0) {
                  // Convert to legacy format expected by UI for each day
                  processedUnit.availableHours[day] = [
                    unit.availableHours[day][0].start,
                    unit.availableHours[day][0].end
                  ] as [string, string];
                }
              });
              
              // If useDayWiseTimeSlots wasn't explicitly set in the backend data
              // determine it from the data structure
              if (unit.useDayWiseTimeSlots === undefined) {
                // Set useDayWiseTimeSlots based on whether availableHours has multiple different time slots per day
                const hasMultipleTimeSlots = Object.values(unit.availableHours).some(
                  daySlots => Array.isArray(daySlots) && daySlots.length > 1
                );
                
                processedUnit.useDayWiseTimeSlots = hasMultipleTimeSlots;
              }
              
              if (processedUnit.useDayWiseTimeSlots) {
                processedUnit.dayWiseAvailableHours = {};
                
                // Process each day's time slots
                Object.keys(unit.availableHours).forEach(day => {
                  if (Array.isArray(unit.availableHours[day])) {
                    processedUnit.dayWiseAvailableHours![day] = unit.availableHours[day];
                  }
                });
              } else {
                // Use regular time slots - take from the first day
                const firstDay = Object.keys(unit.availableHours)[0];
                if (firstDay && Array.isArray(unit.availableHours[firstDay]) && unit.availableHours[firstDay].length > 0) {
                  processedUnit.timeSlots = unit.availableHours[firstDay];
                } else {
                  processedUnit.timeSlots = [{ start: "09:00 AM", end: "05:00 PM" }];
                }
              }
            } else {
              // If no availableHours, set defaults
              processedUnit.useDayWiseTimeSlots = false;
              processedUnit.timeSlots = [{ start: "09:00 AM", end: "05:00 PM" }];
            }
            
            return processedUnit;
          });
          
          setSubUnits(processedSubUnits);
          
          // If subunits exist and the feature is enabled, expand the first one
          if (processedSubUnits.length > 0) {
            setExpandedUnitIndex(0);
          }
        } else {
          // Regular service without subunits
          setHasSubUnits(false);
          setSubUnits([]);
          setExpandedUnitIndex(null);
          
          // Process the availableHours
          if (result.data.availableHours && typeof result.data.availableHours === 'object') {
            // First check if useDayWiseTimeSlots is explicitly set in the backend data
            if (result.data.useDayWiseTimeSlots !== undefined) {
              console.log("Using backend useDayWiseTimeSlots:", result.data.useDayWiseTimeSlots);
              setUseDayWiseTimeSlots(result.data.useDayWiseTimeSlots);
            } else {
              // Otherwise detect from data structure
              // Check if there are different time slots for different days
              const hasDifferentTimeSlots = Object.keys(result.data.availableHours).length > 1 && 
                Object.values(result.data.availableHours).some((daySlots: any, i, arr) => {
                  if (i === 0) return false;
                  // Compare current day slots with first day slots
                  const firstDaySlots = arr[0];
                  if (!Array.isArray(daySlots) || !Array.isArray(firstDaySlots)) return false;
                  
                  // Check if they have different length
                  if (daySlots.length !== firstDaySlots.length) return true;
                  
                  // Check if any slot is different
                  return daySlots.some((slot, slotIndex) => {
                    return slot.start !== firstDaySlots[slotIndex].start || 
                          slot.end !== firstDaySlots[slotIndex].end;
                  });
                });

              // Also check if any day has multiple time slots
              const hasMultipleSlots = Object.values(result.data.availableHours).some(
                slots => Array.isArray(slots) && slots.length > 1
              );

              // If either condition is true, we should use day-wise mode
              const shouldUseDayWiseTimeSlots = hasDifferentTimeSlots || hasMultipleSlots;
              console.log("Detected useDayWiseTimeSlots:", shouldUseDayWiseTimeSlots, 
                "based on hasDifferentTimeSlots:", hasDifferentTimeSlots, 
                "and hasMultipleSlots:", hasMultipleSlots);
              
              setUseDayWiseTimeSlots(shouldUseDayWiseTimeSlots);
            }

            // Convert availableHours to timeSlotsByDay format regardless of mode
            const convertedTimeSlotsByDay: TimeSlotsByDay = {};
            Object.keys(result.data.availableHours).forEach(day => {
              if (Array.isArray(result.data.availableHours[day])) {
                convertedTimeSlotsByDay[day] = result.data.availableHours[day];
              }
            });
            
            setTimeSlotsByDay(convertedTimeSlotsByDay);
            
            // Use first day's slots for timeSlots as a fallback
            const firstDay = Object.keys(result.data.availableHours)[0];
            if (firstDay && Array.isArray(result.data.availableHours[firstDay])) {
              setTimeSlots(result.data.availableHours[firstDay]);
            }
          } else if (result.data.timeSlots) {
            // Handle legacy format for backward compatibility
            setUseDayWiseTimeSlots(result.data.useDayWiseTimeSlots || false);
        setTimeSlots(result.data.timeSlots);
            
            if (result.data.timeSlotsByDay && Object.keys(result.data.timeSlotsByDay).length > 0) {
              setTimeSlotsByDay(result.data.timeSlotsByDay);
            } else {
              initializeDayWiseTimeSlots(result.data.selectedDays, result.data.timeSlots);
            }
          } else {
            // Default fallback
            setUseDayWiseTimeSlots(false);
            setTimeSlots([{ start: "09:00 AM", end: "05:00 PM" }]);
            initializeDayWiseTimeSlots(result.data.selectedDays, [{ start: "09:00 AM", end: "05:00 PM" }]);
          }
        }
          
        await SecureStore.setItemAsync(
          "serviceSetupData",
          JSON.stringify(result.data)
        );
        return;
      } else {
        // No setup exists - use defaults
        console.log("No existing setup, using default values");
        const defaultSetup = {
          selectedDays: weekDays.slice(1, 6).map((day) => day.full),
          timeSlots: [{ start: "09:00 AM", end: "05:00 PM" }],
          useDayWiseTimeSlots: false,
          timeSlotsByDay: {},
          servingTime: "30",
          basePrice: "",
          hasSubUnits: false,
          subUnits: [],
        };

        // Initialize day-wise time slots based on default values
        initializeDayWiseTimeSlots(
          defaultSetup.selectedDays,
          defaultSetup.timeSlots
        );

        await SecureStore.setItemAsync(
          "serviceSetupData",
          JSON.stringify(defaultSetup)
        );
        return;
      }
    }

    throw new Error("Invalid API response format");
  } catch (error) {
    console.error("Error loading setup data:", error);
    // Load from local storage as fallback
    await loadSavedSetupData();
  } finally {
    setIsLoading(false);
  }
};
  
  const loadSavedSetupData = async () => {
    try {
      const savedData = await SecureStore.getItemAsync("serviceSetupData");
      if (savedData) {
        console.log("Loading setup data from local storage");
        const data = JSON.parse(savedData);
        setSelectedDays(
          data.selectedDays || weekDays.slice(1, 6).map((day) => day.full)
        );
        setTimeSlots(
          data.timeSlots || [{ start: "09:00 AM", end: "05:00 PM" }]
        );
        setServingTime(data.servingTime || "30");
        setBasePrice(data.basePrice || "");

        // Load day-wise time slots if available
        if (data.useDayWiseTimeSlots !== undefined) {
          setUseDayWiseTimeSlots(data.useDayWiseTimeSlots);

          if (data.timeSlotsByDay) {
            setTimeSlotsByDay(data.timeSlotsByDay);
      } else {
            // Initialize day-wise time slots from regular time slots if not available
            initializeDayWiseTimeSlots(data.selectedDays, data.timeSlots);
          }
        }

        // Load sub-units data if available
        if (data.hasSubUnits !== undefined) {
          setHasSubUnits(data.hasSubUnits);
          setSubUnits(data.subUnits || []);
        }
      } else {
        console.log("No saved setup data found in local storage");
      }
    } catch (error) {
      console.error("Error loading saved setup data:", error);
    }
  };

  useEffect(() => {
    initializeData();
  }, []);

  const saveSetupToDatabase = async (setupData: StandardizedSetupData) => {
    try {
      setIsLoading(true);
      if (!serviceId) throw new Error("Service ID not found");

      console.log("Attempting to save setup data:", setupData);

      // Ensure the data structure is in the correct format expected by the backend
      const payload = {
          setupData: {
            selectedDays: setupData.selectedDays,
            servingTime: setupData.servingTime,
          basePrice: setupData.basePrice,
          hasSubUnits: setupData.hasSubUnits,
          useDayWiseTimeSlots: setupData.useDayWiseTimeSlots, // Add this flag to backend
          // Include availableHours for regular services
          ...(setupData.availableHours && { availableHours: setupData.availableHours }),
          // Include subUnits for services with subunits
          ...(setupData.subUnits && { 
            subUnits: setupData.subUnits.map(unit => ({
              ...unit,
              useDayWiseTimeSlots: unit.useDayWiseTimeSlots // Include flag for each subunit
            }))
          })
        }
      };

      const response = await fetch(
        `http://**************:3000/api/partner/service-setup/${serviceId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to save setup data");
      }

      const result = await response.json();
      console.log("Setup data saved successfully:", result);

      return result;
    } catch (error) {
      console.error("Error saving setup data:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize day-wise time slots based on selected days and default time slots
  const initializeDayWiseTimeSlots = (
    days: string[],
    defaultSlots: TimeSlot[]
  ) => {
    const slots: TimeSlotsByDay = {};

    days.forEach((day: string) => {
      slots[day] = [...defaultSlots]; // Copy the default slots for each day
    });

    setTimeSlotsByDay(slots);
  };

  // Update day-wise time slots when selected days change
  useEffect(() => {
    if (useDayWiseTimeSlots) {
      // Add new days if they don't exist in the current timeSlotsByDay
      const updatedSlots = { ...timeSlotsByDay };

      selectedDays.forEach((day) => {
        if (!updatedSlots[day] || updatedSlots[day].length === 0) {
          // Always initialize with default time slot
          updatedSlots[day] = [{ start: "09:00 AM", end: "05:00 PM" }];
        }
      });

      // Remove days that are no longer selected
      Object.keys(updatedSlots).forEach((day) => {
        if (!selectedDays.includes(day)) {
          delete updatedSlots[day];
        }
      });

      setTimeSlotsByDay(updatedSlots);
    }
  }, [selectedDays, useDayWiseTimeSlots]);

  const handleSave = async () => {
    // Basic validation
    if (hasSubUnits) {
      // Validate sub-units
      if (!Array.isArray(subUnits) || subUnits.length === 0) {
        Alert.alert("Error", "Please add at least one sub-unit");
        return;
      }

      // Validate sub-unit fields
      for (let i = 0; i < subUnits.length; i++) {
        const unit = subUnits[i];
        if (!unit.name) {
          Alert.alert("Error", `Please enter a name for sub-unit ${i + 1}`);
          return;
        }
        if (!unit.avgServeTime) {
          Alert.alert("Error", `Please enter serving time for ${unit.name}`);
          return;
        }
        if (!unit.pricePerHead || Number(unit.pricePerHead) < 50) {
          Alert.alert(
            "Error",
            `Please enter a valid price for ${unit.name} (minimum ₹50)`
          );
          return;
        }
      }
    } else {
      // Basic configuration validation
    if (!selectedDays.length) {
      Alert.alert("Error", "Please select at least one working day");
      return;
    }

      if (useDayWiseTimeSlots) {
        // Validate day-wise time slots
        for (const day of selectedDays) {
          if (!timeSlotsByDay[day] || timeSlotsByDay[day].length === 0) {
            Alert.alert(
              "Error",
              `Please add at least one time slot for ${day}`
            );
            return;
          }

          // Check for invalid time slots
          const hasInvalidTimeSlots = timeSlotsByDay[day].some(
            (slot) =>
              !slot.start ||
              !slot.end ||
              slot.start === "0:00" ||
              slot.end === "0:00"
          );

          if (hasInvalidTimeSlots) {
            Alert.alert(
              "Error",
              `Please set valid times for all time slots on ${day}`
            );
            return;
          }
        }
      } else {
        // Validate regular time slots
    if (!timeSlots.length) {
      Alert.alert("Error", "Please add at least one time slot");
      return;
    }
    
    // Validate time slots
    const hasInvalidTimeSlots = timeSlots.some(
          (slot) =>
            !slot.start ||
            !slot.end ||
            slot.start === "0:00" ||
            slot.end === "0:00"
    );

    if (hasInvalidTimeSlots) {
      Alert.alert("Error", "Please set valid times for all time slots");
      return;
        }
    }

    if (!servingTime) {
      Alert.alert("Error", "Please enter serving time");
      return;
    }
    if (!basePrice || Number(basePrice) < 50) {
      Alert.alert("Error", "Please enter a valid price (minimum ₹50)");
      return;
      }
    }

    try {
      setIsSaving(true);

      // Convert to the new standardized format using availableHours
      let standardizedSetupData: StandardizedSetupData = {
        selectedDays,
        servingTime,
        basePrice,
        hasSubUnits,
        useDayWiseTimeSlots, // Add this flag for backend
      };

      // For regular service, convert time slots to availableHours format
      if (!hasSubUnits) {
        standardizedSetupData.availableHours = {};
        
        if (useDayWiseTimeSlots) {
          // If using day-wise time slots, copy directly to availableHours
          standardizedSetupData.availableHours = { ...timeSlotsByDay };
          
          // Ensure every selected day has at least one time slot
          selectedDays.forEach(day => {
            if (!standardizedSetupData.availableHours![day] || 
                standardizedSetupData.availableHours![day].length === 0) {
              // Add default time slot for this day
              standardizedSetupData.availableHours![day] = [{ 
                start: "09:00 AM", 
                end: "05:00 PM" 
              }];
            }
          });
        } else {
          // If using regular time slots, assign them to each selected day
          selectedDays.forEach(day => {
            if (standardizedSetupData.availableHours) {
              standardizedSetupData.availableHours[day] = [...timeSlots];
            }
          });
        }
      } else {
        // For services with subunits, convert each subunit's time slots
        standardizedSetupData.subUnits = subUnits.map(unit => {
          const standardizedUnit: {
            name: string;
            avgServeTime: string;
            pricePerHead: string;
            selectedDays: string[];
            useDayWiseTimeSlots?: boolean; // Add this flag for each subunit
            availableHours: { [key: string]: Array<{ start: string; end: string }> };
          } = {
            name: unit.name,
            avgServeTime: unit.avgServeTime,
            pricePerHead: unit.pricePerHead,
            selectedDays: unit.selectedDays || [...selectedDays],
            useDayWiseTimeSlots: unit.useDayWiseTimeSlots, // Set explicit flag
            availableHours: {}
          };

          // Make sure selectedDays exists and is an array
          if (!Array.isArray(standardizedUnit.selectedDays) || standardizedUnit.selectedDays.length === 0) {
            standardizedUnit.selectedDays = [...selectedDays];
          }

          if (unit.useDayWiseTimeSlots && unit.dayWiseAvailableHours) {
            // If using day-wise slots, copy them to availableHours
            standardizedUnit.availableHours = { ...unit.dayWiseAvailableHours };
            
            // Ensure every selected day has at least one time slot
            standardizedUnit.selectedDays.forEach(day => {
              if (!standardizedUnit.availableHours[day] || 
                  standardizedUnit.availableHours[day].length === 0) {
                // Add default time slot for this day
                standardizedUnit.availableHours[day] = [{ 
                  start: "09:00 AM", 
                  end: "05:00 PM" 
                }];
              }
            });
          } else if (unit.timeSlots && unit.timeSlots.length > 0) {
            // If using regular time slots, copy to each selected day
            standardizedUnit.selectedDays.forEach(day => {
              if (unit.timeSlots) {
                standardizedUnit.availableHours[day] = [...unit.timeSlots];
              }
            });
          } else if (unit.availableHours) {
            // If unit already has availableHours format data, convert it to the right structure
            const convertedAvailableHours: { [key: string]: Array<{ start: string; end: string }> } = {};
            Object.keys(unit.availableHours).forEach(day => {
              if (unit.availableHours && Array.isArray(unit.availableHours[day])) {
                const [start, end] = unit.availableHours[day];
                convertedAvailableHours[day] = [{ start, end }];
              }
            });
            standardizedUnit.availableHours = convertedAvailableHours;
            
            // Ensure every selected day has at least one time slot
            standardizedUnit.selectedDays.forEach(day => {
              if (!standardizedUnit.availableHours[day] || 
                  standardizedUnit.availableHours[day].length === 0) {
                // Add default time slot for this day
                standardizedUnit.availableHours[day] = [{ 
                  start: "09:00 AM", 
                  end: "05:00 PM" 
                }];
              }
            });
          }

          return standardizedUnit;
        });
      }

      const email = user?.primaryEmailAddress?.emailAddress;
      if (!email) throw new Error("Email not found");

      // Get latest service details to check terms acceptance
      const serviceDetailsResponse = await fetch(
        `http://**************:3000/api/partner/service-details/${encodeURIComponent(email)}`
      );

      if (!serviceDetailsResponse.ok) {
        throw new Error("Failed to fetch service details");
      }

      const serviceData = await serviceDetailsResponse.json();
      
      // Update local storage with latest status
      await SecureStore.setItemAsync(
        "serviceStatus",
        JSON.stringify({
          ...JSON.parse(
            (await SecureStore.getItemAsync("serviceStatus")) || "{}"
          ),
          termsAccepted: serviceData.termsAccepted,
        })
      );

      console.log("Saving standardized setup data:", JSON.stringify(standardizedSetupData, null, 2));

      if (serviceData.termsAccepted) {
        // If terms are accepted, save directly to database
        await saveSetupToDatabase(standardizedSetupData);
        
        // Function to detect if configuration has changed
        const detectConfigChanges = () => {
          // Always check subunit type change as before
          const hasSubUnitsChanged = originalHasSubUnits !== null && originalHasSubUnits !== hasSubUnits;
          
          // If subunit type changed, no need to check further
          if (hasSubUnitsChanged) {
            console.log("SubUnit settings changed - Original:", originalHasSubUnits, "New:", hasSubUnits);
            return true;
          }
          
          // If no original config, can't compare
          if (!originalConfig) {
            return false;
          }
          
          // Check for changes in basic settings
          const selectedDaysChanged = JSON.stringify(originalConfig.selectedDays) !== JSON.stringify(selectedDays);
          const servingTimeChanged = originalConfig.servingTime !== servingTime;
          const basePriceChanged = originalConfig.basePrice !== basePrice;
          const useDayWiseTimeSlotsChanged = originalConfig.useDayWiseTimeSlots !== useDayWiseTimeSlots;
          
          // Check for changes in time slots
          let timeSlotsChanged = false;
          
          if (!hasSubUnits) {
            // For regular service
            if (useDayWiseTimeSlots) {
              // Compare day-wise time slots
              if (originalConfig.availableHours) {
                const originalDays = Object.keys(originalConfig.availableHours);
                const currentDays = Object.keys(timeSlotsByDay);
                
                if (JSON.stringify(originalDays.sort()) !== JSON.stringify(currentDays.sort())) {
                  timeSlotsChanged = true;
                } else {
                  // Compare each day's time slots
                  for (const day of originalDays) {
                    if (JSON.stringify(originalConfig.availableHours[day]) !== 
                        JSON.stringify(timeSlotsByDay[day])) {
                      timeSlotsChanged = true;
                      break;
                    }
                  }
                }
              } else {
                // If original didn't have availableHours but now we're using day-wise, it's changed
                timeSlotsChanged = true;
              }
            } else {
              // Compare regular time slots
              if (originalConfig.timeSlots) {
                timeSlotsChanged = JSON.stringify(originalConfig.timeSlots) !== JSON.stringify(timeSlots);
              } else if (originalConfig.availableHours) {
                // If original had availableHours but now we're using regular slots, check first day
                const firstDay = Object.keys(originalConfig.availableHours)[0];
                if (firstDay) {
                  timeSlotsChanged = JSON.stringify(originalConfig.availableHours[firstDay]) !== 
                                    JSON.stringify(timeSlots);
                }
              }
            }
          } else {
            // For service with subunits
            if (originalConfig.subUnits && Array.isArray(originalConfig.subUnits)) {
              // Check if number of subunits changed
              if (originalConfig.subUnits.length !== subUnits.length) {
                timeSlotsChanged = true;
              } else {
                // Compare each subunit
                for (let i = 0; i < subUnits.length; i++) {
                  const originalUnit = originalConfig.subUnits[i];
                  const currentUnit = subUnits[i];
                  
                  if (!originalUnit || !currentUnit) continue;
                  
                  // Check basic subunit properties
                  if (originalUnit.name !== currentUnit.name ||
                      originalUnit.avgServeTime !== currentUnit.avgServeTime ||
                      originalUnit.pricePerHead !== currentUnit.pricePerHead ||
                      JSON.stringify(originalUnit.selectedDays) !== JSON.stringify(currentUnit.selectedDays) ||
                      originalUnit.useDayWiseTimeSlots !== currentUnit.useDayWiseTimeSlots) {
                    timeSlotsChanged = true;
                    break;
                  }
                  
                  // Check time slots
                  if (currentUnit.useDayWiseTimeSlots) {
                    // Compare day-wise time slots
                    if (originalUnit.dayWiseAvailableHours && currentUnit.dayWiseAvailableHours) {
                      if (JSON.stringify(originalUnit.dayWiseAvailableHours) !== 
                          JSON.stringify(currentUnit.dayWiseAvailableHours)) {
                        timeSlotsChanged = true;
                        break;
                      }
                    } else if (originalUnit.availableHours) {
                      // Compare with converted format
                      let changed = false;
                      for (const day of currentUnit.selectedDays || []) {
                        if (!originalUnit.availableHours[day] || 
                            !currentUnit.dayWiseAvailableHours?.[day] ||
                            JSON.stringify(originalUnit.availableHours[day]) !== 
                            JSON.stringify(currentUnit.dayWiseAvailableHours[day])) {
                          changed = true;
                          break;
                        }
                      }
                      if (changed) {
                        timeSlotsChanged = true;
                        break;
                      }
                    }
                  } else {
                    // Compare regular time slots
                    if (originalUnit.timeSlots && currentUnit.timeSlots) {
                      if (JSON.stringify(originalUnit.timeSlots) !== 
                          JSON.stringify(currentUnit.timeSlots)) {
                        timeSlotsChanged = true;
                        break;
                      }
                    } else if (originalUnit.availableHours) {
                      // Compare with first day's slots
                      const firstDay = Object.keys(originalUnit.availableHours)[0];
                      if (firstDay && currentUnit.timeSlots) {
                        if (JSON.stringify(originalUnit.availableHours[firstDay]) !== 
                            JSON.stringify(currentUnit.timeSlots)) {
                          timeSlotsChanged = true;
                          break;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          
          // Log what changed
          if (selectedDaysChanged) console.log("Selected days changed");
          if (servingTimeChanged) console.log("Serving time changed");
          if (basePriceChanged) console.log("Base price changed");
          if (useDayWiseTimeSlotsChanged) console.log("Day-wise time slots setting changed");
          if (timeSlotsChanged) console.log("Time slots configuration changed");
          
          return selectedDaysChanged || servingTimeChanged || basePriceChanged || 
                 useDayWiseTimeSlotsChanged || timeSlotsChanged;
        };
        
        // Check if any configuration has changed
        const configChanged = detectConfigChanges();
        console.log("Configuration changed:", configChanged);

        if (configChanged) {
          // Show alert about restarting the app for changes to take effect
          Alert.alert(
            "Restart Required",
            "You've made changes to your service configuration. Please restart the app for these changes to take effect.",
            [
              {
                text: "Return to Dashboard",
                onPress: () => router.back()
              },
              {
                text: "Restart Now",
                style: "default",
                onPress: async () => {
                  console.log("User chose to restart app immediately");
                  try {
                    // For Expo, we use the Updates module to reload the app
                    await AsyncStorage.setItem('app_needs_reload', 'true');
                    await Updates.reloadAsync();
                  } catch (error) {
                    console.error("Failed to restart app:", error);
                    // Fallback to navigation if restart fails
                    Alert.alert(
                      "Restart Failed",
                      "Please manually close and reopen the app for changes to take effect.",
                      [{ text: "OK", onPress: () => router.back() }]
                    );
                  }
                }
              }
            ]
          );
        } else {
        router.back();
        }
      } else {
        // If terms not accepted, save locally and continue setup flow
        await SecureStore.setItemAsync(
          "serviceSetupData",
          JSON.stringify(standardizedSetupData)
        );
        await SecureStore.setItemAsync("setupComplete", "true");
        router.replace("/service-setup/step3");
      }
    } catch (error) {
      console.error("Error in handleSave:", error);
      Alert.alert(
        "Error",
        error instanceof Error ? error.message : "Failed to save setup data"
      );
    } finally {
      setIsSaving(false);
    }
  };

  const clearEditMode = async () => {
    try {
      const formData = await SecureStore.getItemAsync("serviceFormData");
      if (formData) {
        const parsedData = JSON.parse(formData);
        delete parsedData.isEdit;
        await SecureStore.setItemAsync(
          "serviceFormData",
          JSON.stringify(parsedData)
        );
      }
    } catch (error) {
      console.error("Error clearing edit mode:", error);
    }
  };

  const toggleDay = (day: string) => {
    const isAdding = !selectedDays.includes(day);
    setSelectedDays((prev) =>
      prev.includes(day) ? prev.filter((d) => d !== day) : [...prev, day]
    );

    // If we're adding a new day and using day-wise time slots,
    // immediately initialize its time slots in the UI
    if (isAdding && useDayWiseTimeSlots) {
      setTimeSlotsByDay((prev) => ({
        ...prev,
        [day]: [{ start: "09:00 AM", end: "05:00 PM" }]
      }));
    }
  };
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);
  const addTimeSlot = () => {
    setTimeSlots((prev) => [...prev, { start: "09:00 AM", end: "05:00 PM" }]);
  };

  const removeTimeSlot = (index: number) => {
    setTimeSlots((prev) => prev.filter((_, i) => i !== index));
  };

  const updateTimeSlot = (
    index: number,
    field: "start" | "end",
    value: string
  ) => {
    setTimeSlots((prev) =>
      prev.map((slot, i) => (i === index ? { ...slot, [field]: value } : slot))
    );
  };

  // Day-wise time slot management
  const addDayTimeSlot = (day: string) => {
    setTimeSlotsByDay((prev) => ({
      ...prev,
      [day]: [...(prev[day] || []), { start: "09:00 AM", end: "05:00 PM" }],
    }));
  };

  const removeDayTimeSlot = (day: string, index: number) => {
    setTimeSlotsByDay((prev) => ({
      ...prev,
      [day]: prev[day].filter((_, i) => i !== index),
    }));
  };

  const updateDayTimeSlot = (
    day: string,
    index: number,
    field: "start" | "end",
    value: string
  ) => {
    setTimeSlotsByDay((prev) => ({
      ...prev,
      [day]: prev[day].map((slot, i) =>
        i === index ? { ...slot, [field]: value } : slot
      ),
    }));
  };

  // Copy time slots from one day to all other selected days
  const copyDayTimeSlotsToAll = (sourceDay: string) => {
    Alert.alert(
      "Copy to All Days",
      `Are you sure you want to copy time slots from ${sourceDay} to all other selected days?`,
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Copy",
          onPress: () => {
            const sourceDaySlots = timeSlotsByDay[sourceDay] || [];

            setTimeSlotsByDay((prev) => {
              const updated = { ...prev };

              selectedDays.forEach((day) => {
                if (day !== sourceDay) {
                  updated[day] = [...sourceDaySlots];
                }
              });

              return updated;
            });
          },
        },
      ]
    );
  };

  // Toggle between regular and day-wise time slots
  const toggleDayWiseTimeSlots = (value: boolean) => {
    if (value && !useDayWiseTimeSlots) {
      // Switching from regular to day-wise
      initializeDayWiseTimeSlots(selectedDays, timeSlots);
    }

    setUseDayWiseTimeSlots(value);
  };

  const handleTimeSelect = (time: string) => {
    if (!activeTimeSlot) return;

    if (activeTimeSlot.unitIndex !== undefined) {
      // Handle sub-unit time selection
      const unitIndex = activeTimeSlot.unitIndex;
      
      if (activeTimeSlot.day) {
        // Handle day-wise time slots for subunit
        const updatedUnit = { ...subUnits[unitIndex] };
        if (!updatedUnit.dayWiseAvailableHours) {
          updatedUnit.dayWiseAvailableHours = {};
        }
        
        if (!updatedUnit.dayWiseAvailableHours[activeTimeSlot.day]) {
          updatedUnit.dayWiseAvailableHours[activeTimeSlot.day] = [];
        }
        
        // Create updated time slots array
        const updatedTimeSlots = [...updatedUnit.dayWiseAvailableHours[activeTimeSlot.day]];
        
        // Update the specific slot
        if (updatedTimeSlots[activeTimeSlot.index]) {
          updatedTimeSlots[activeTimeSlot.index] = {
            ...updatedTimeSlots[activeTimeSlot.index],
            [activeTimeSlot.field]: time
          };
        }
        
        // Update the unit's dayWiseAvailableHours
        updatedUnit.dayWiseAvailableHours[activeTimeSlot.day] = updatedTimeSlots;
        updateSubUnit(unitIndex, "dayWiseAvailableHours", updatedUnit.dayWiseAvailableHours);
      } else {
        // Handle regular time slots for subunit
        const updatedUnit = { ...subUnits[unitIndex] };
        
        if (updatedUnit.timeSlots && updatedUnit.timeSlots.length > 0) {
          const updatedTimeSlots = [...updatedUnit.timeSlots];
          
          if (updatedTimeSlots[activeTimeSlot.index]) {
            updatedTimeSlots[activeTimeSlot.index] = {
              ...updatedTimeSlots[activeTimeSlot.index],
              [activeTimeSlot.field]: time
            };
            
            updateSubUnit(unitIndex, "timeSlots", updatedTimeSlots);
    }
        } else {
          // Legacy handling for availableHours format
          const day = Object.keys(subUnits[unitIndex].availableHours)[0];
          const field = activeTimeSlot.field === "start" ? "0" : "1";
          updateSubUnitTimeSlot(unitIndex, day, field as "0" | "1", time);
        }
      }
    } else if (activeTimeSlot.day) {
      // Handle day-wise time slot selection
      updateDayTimeSlot(
        activeTimeSlot.day,
        activeTimeSlot.index,
        activeTimeSlot.field,
        time
      );
    } else {
      // Handle regular time slot selection
      updateTimeSlot(activeTimeSlot.index, activeTimeSlot.field, time);
    }

    setShowTimePicker(false);
    setActiveTimeSlot(null);
  };

  const openTimePicker = (index: number, field: "start" | "end") => {
    const currentTime = timeSlots[index][field];
    setActiveTimeSlot({ index, field });
    setShowTimePicker(true);
  };

  const openDayTimePicker = (
    day: string,
    index: number,
    field: "start" | "end"
  ) => {
    const currentTime = timeSlotsByDay[day]?.[index]?.[field] || "09:00 AM";
    setActiveTimeSlot({ index, field, day });
    setShowTimePicker(true);
  };

  const handleServingTimeChange = (text: string) => {
    // Only allow numbers and max 3 characters
    const numericOnly = text.replace(/[^0-9]/g, "");
    if (numericOnly.length <= 3) {
      setServingTime(numericOnly);
    }
  };

  const ServingTimeInfo = () => (
    <Modal
      visible={showServingTimeInfo}
      transparent
      animationType="fade"
      onRequestClose={() => setShowServingTimeInfo(false)}
    >
      <TouchableOpacity 
        className="flex-1 bg-black/50 justify-center items-center"
        activeOpacity={1}
        onPress={() => setShowServingTimeInfo(false)}
      >
        <View className="bg-white rounded-2xl p-6 m-4 w-[320px]">
          <Text className="text-lg font-[Poppins-Medium] mb-3">
            Serving Time
          </Text>
          <Text className="text-secondary-600 mb-4 leading-5">
            This is the maximum time you typically spend serving one customer.
            We use this to:
            {"\n\n"}• Calculate total queue slots
            {"\n"}• Estimate waiting times
            {"\n"}• Optimize queue management
          </Text>
          <TouchableOpacity 
            onPress={() => setShowServingTimeInfo(false)}
            className="self-end"
          >
            <Text className="text-primary-500 font-[Poppins-Medium]">
              Got it
            </Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  const SubUnitInfo = () => (
    <Modal
      visible={showSubUnitInfo}
      transparent
      animationType="fade"
      onRequestClose={() => setShowSubUnitInfo(false)}
    >
      <TouchableOpacity
        className="flex-1 bg-black/50 justify-center items-center"
        activeOpacity={1}
        onPress={() => setShowSubUnitInfo(false)}
      >
        <View className="bg-white rounded-2xl p-6 m-4 w-[320px]">
          <Text className="text-lg font-[Poppins-Medium] mb-3">Sub-Unit</Text>
          <Text className="text-secondary-600 mb-4 leading-5">
            Does your service have multiple departments, counters, chairs, or
            sections?
            {"\n\n"}
            If Yes, you can toggle on the sub-unit option.
          </Text>
          <TouchableOpacity
            onPress={() => setShowSubUnitInfo(false)}
            className="self-end"
          >
            <Text className="text-primary-500 font-[Poppins-Medium]">
              Got it
            </Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  // New functions for sub-unit management
  const addSubUnit = () => {
    // Calculate the next subunit number
    const nextUnitNumber = subUnits.length + 1;

    const newSubUnit: SubUnit = {
      name: `Sub Unit ${nextUnitNumber}`,
      availableHours: {
        Monday: ["09:00 AM", "05:00 PM"],
        Tuesday: ["09:00 AM", "05:00 PM"],
        Wednesday: ["09:00 AM", "05:00 PM"],
        Thursday: ["09:00 AM", "05:00 PM"],
        Friday: ["09:00 AM", "05:00 PM"],
      },
      avgServeTime: "15",
      pricePerHead: "150",
      selectedDays: selectedDays,
      timeSlots: [{ start: "09:00 AM", end: "05:00 PM" }],
      useDayWiseTimeSlots: false,
    };

    setSubUnits((prev) => [...prev, newSubUnit]);
    // Expand the newly added unit
    setExpandedUnitIndex(subUnits.length);
  };

  const removeSubUnit = (index: number) => {
    setSubUnits((prev) => prev.filter((_, i) => i !== index));
    // If the removed unit was expanded, collapse it
    if (expandedUnitIndex === index) {
      setExpandedUnitIndex(null);
    } else if (expandedUnitIndex !== null && expandedUnitIndex > index) {
      // Adjust expanded index if a unit before it was removed
      setExpandedUnitIndex(expandedUnitIndex - 1);
    }
  };

  const updateSubUnit = (index: number, field: keyof SubUnit, value: any) => {
    setSubUnits((prev) =>
      prev.map((unit, i) => (i === index ? { ...unit, [field]: value } : unit))
    );
  };

  const copySubUnit = (index: number) => {
    setCopiedSubUnit({ ...subUnits[index] });
    Alert.alert("Success", "Sub-unit config copied!");
  };

  const pasteSubUnit = (index: number) => {
    if (!copiedSubUnit) return;

    setSubUnits((prev) =>
      prev.map((unit, i) =>
        i === index ? { ...copiedSubUnit, name: unit.name } : unit
      )
    );
  };

  const toggleSubUnitExpansion = (index: number) => {
    setExpandedUnitIndex(expandedUnitIndex === index ? null : index);
  };

  const updateSubUnitTimeSlot = (
    unitIndex: number,
    day: string,
    field: "0" | "1",
    time: string
  ) => {
    setSubUnits((prev) => {
      const updatedUnits = [...prev];
      const unit = { ...updatedUnits[unitIndex] };
      const hours = { ...unit.availableHours };

      if (!hours[day]) {
        hours[day] = ["09:00", "17:00"] as [string, string];
      }

      const newHours = [...hours[day]];
      newHours[parseInt(field)] = time;
      hours[day] = newHours as [string, string];

      unit.availableHours = hours;
      updatedUnits[unitIndex] = unit;

      return updatedUnits;
    });
  };

  // Add the openSubUnitTimePicker function
  const openSubUnitTimePicker = (
    unitIndex: number,
    day: string,
    field: "0" | "1"
  ) => {
    const unit = subUnits[unitIndex];
    const currentTime = unit.availableHours[day]
      ? unit.availableHours[day][parseInt(field)]
      : "09:00 AM";

    setActiveTimeSlot({
      index: parseInt(field),
      field: field === "0" ? "start" : "end",
      unitIndex,
    });
    setShowTimePicker(true);
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      {/* Loading Overlay */}
      {isLoading && (
        <View className="absolute w-full h-full bg-black/30 z-50 items-center justify-center">
          <View className="bg-white p-8 rounded-xl items-center">
            <ActivityIndicator size="large" color="#159AFF" className="mb-2" />
            <Text className="font-poppins-medium text-secondary-500 text-base">Loading...</Text>
          </View>
        </View>
      )}

      {/* Header */}
      <View className="px-8 pt-8">
        <View className="w-full flex-row">
          <TouchableOpacity
            className="flex-row items-center absolute left-0 border border-secondary-600 w-12 h-12 rounded-full justify-center"
            onPress={() => router.back()}
          >
            <Image source={images.back} className="w-4 h-4" />
          </TouchableOpacity>
          <View className="flex-1 items-center mt-2 mb-4">
            <Text className="font-poppins-medium text-xl mb-4">
              Complete Service Setup
            </Text>
          </View>
        </View>
      </View>

      <ScrollView className="flex-1 px-3">
        <View className="p-6">
          {/* Sub-Units Toggle */}
          <View className="mb-8">
            <TouchableOpacity
              onPress={() => {
                const newValue = !hasSubUnits;
                
                if (newValue) {
                  // Turning ON sub-units
                  setHasSubUnits(true);
                  
                  // If no subunits exist, add a default one
                  if (subUnits.length === 0) {
                    const defaultSubUnit: SubUnit = {
                      name: "Sub Unit 1",
                      availableHours: {
                        Monday: ["09:00 AM", "05:00 PM"],
                        Tuesday: ["09:00 AM", "05:00 PM"],
                        Wednesday: ["09:00 AM", "05:00 PM"],
                        Thursday: ["09:00 AM", "05:00 PM"],
                        Friday: ["09:00 AM", "05:00 PM"],
                      },
                      avgServeTime: "15",
                      pricePerHead: "150",
                      selectedDays: [...selectedDays], // Create a copy of selectedDays
                      timeSlots: [{ start: "09:00 AM", end: "05:00 PM" }],
                      useDayWiseTimeSlots: false,
                    };
                    setSubUnits([defaultSubUnit]);
                    setExpandedUnitIndex(0);
                  }
                } else {
                  // Turning OFF sub-units - clean up properly
                  setHasSubUnits(false);
                  setExpandedUnitIndex(null);
                  
                  // Important: Don't clear the subUnits array immediately to prevent render issues
                  // Instead, schedule it for after this render cycle
                  setTimeout(() => {
                    // Only reset the subUnits array if hasSubUnits is still false
                    if (!hasSubUnits) {
                      setSubUnits([]);
                    }
                  }, 0);
                }
              }}
              className="flex-row p-6 rounded-3xl justify-between w-full bg-primary-500 items-center drop-shadow-lg shadow-lg shadow-primary-500"
            >
              <View className="flex-row items-center">
                <Text className="text-lg text-white font-poppins-medium mr-2">
                  Use Sub-Units?
                </Text>
                <TouchableOpacity
                  onPress={() => setShowSubUnitInfo(true)}
                  className="p-1"
                >
                  <Image
                    source={images.info}
                    className="w-6 h-6 mb-1"
                    style={{ tintColor: "#ffff" }}
                  />
                </TouchableOpacity>
              </View>
              <View
                className={`w-7 h-7 rounded-lg items-center justify-center ${hasSubUnits ? "bg-white" : "border-2 border-white"}`}
              >
                {hasSubUnits && (
                  <Image
                    source={images.check}
                    className="w-4 h-3"
                    tintColor={"#159AFF"}
                  />
                )}
              </View>
            </TouchableOpacity>
            <SubUnitInfo />
          </View>

          {/* Simple Config (if no sub-units) */}
          {!hasSubUnits && (
            <>
          {/* Working Days */}
          <View className="mb-6">
            <Text className="text-sm mb-4 text-secondary-600 font-[Poppins-Medium]">
              Working Days
            </Text>
            <View className="flex-row justify-between mb-4">
              {weekDays.map((day) => (
                <TouchableOpacity
                  key={day.full}
                  onPress={() => toggleDay(day.full)}
                  className={`w-11 h-11 rounded-full border-2 justify-center items-center
                    ${
                      selectedDays.includes(day.full)
                        ? "bg-primary-500 border-primary-500"
                        : "border-gray-300"
                    }`}
                >
                  <Text
                    className={`font-[Poppins-Medium] text-base
                    ${selectedDays.includes(day.full) ? "text-white" : "text-gray-500"}
                  `}
                  >
                    {day.short}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

              {/* Day-wise Time Slots Toggle */}
              <TouchableOpacity
                onPress={() => toggleDayWiseTimeSlots(!useDayWiseTimeSlots)}
                className="mb-4"
              >
                <View className="flex-row p-5 bg-secondary-600/10 rounded-xl justify-center items-center mb-2">
                  <Text className="text-base text-secondary-500 font-[Poppins-Medium] mr-4">
                    Add day-wise time slots
                  </Text>
                  <View
                    style={{
                      width: 50,
                      flexDirection: "row",
                      alignItems: "center",
                      backgroundColor: useDayWiseTimeSlots
                        ? "#159AFF"
                        : "#E5E5E5",
                      borderRadius: 20,
                      paddingVertical: 4,
                      paddingHorizontal: 4,
                    }}
                  >
                    {useDayWiseTimeSlots ? (
                      <>
                        <View style={{ flex: 1 }} />
                        <View
                          style={{
                            width: 17,
                            height: 17,
                            borderRadius: 10,
                            backgroundColor: "white",
                          }}
                        />
                      </>
                    ) : (
                      <>
                        <View
                          style={{
                            width: 17,
                            height: 17,
                            borderRadius: 10,
                            backgroundColor: "white",
                          }}
                        />
                        <View style={{ flex: 1 }} />
                      </>
                    )}
                  </View>
                </View>
              </TouchableOpacity>

              {/* Regular Time Slots (if day-wise toggle is OFF) */}
              {!useDayWiseTimeSlots && (
          <View className="mb-8">
            <Text className="text-sm mb-4 text-secondary-600 font-[Poppins-Medium]">
              Time Slots
            </Text>
            {timeSlots.map((slot, index) => (
              <View key={index} className="flex-row items-center mb-4">
                <TouchableOpacity
                        className="border border-gray-300 rounded-xl p-5 w-36 flex-row justify-between items-center"
                  onPress={() => openTimePicker(index, "start")}
                >
                  <Text>{slot.start}</Text>
                        <Image
                          source={images.down}
                          className="w-[16px] h-[10px] "
                          tintColor="#6B7280"
                        />
                </TouchableOpacity>

                      <Text className="mx-4">to</Text>

                <TouchableOpacity
                  className="border border-gray-300 rounded-xl p-5 w-36 flex-row justify-between items-center"
                  onPress={() => openTimePicker(index, "end")}
                >
                  <Text>{slot.end}</Text>
                        <Image
                          source={images.down}
                          className="w-[16px] h-[10px] "
                          tintColor="#6B7280"
                        />
                </TouchableOpacity>

                {index > 0 && (
                  <TouchableOpacity
                    onPress={() => removeTimeSlot(index)}
                    className="ml-2 p-2"
                  >
                    <Image
                      source={images.trash}
                      className="w-5 h-5"
                      style={{ tintColor: "#FF4B4B" }}
                    />
                  </TouchableOpacity>
                )}
              </View>
            ))}
            <TouchableOpacity
              onPress={addTimeSlot}
              className="flex-row items-center mt-2"
            >
              <Image
                source={images.plus}
                      className="w-4 h-4 mr-2 mb-1"
                tintColor="#159AFF"
              />
              <Text className="text-primary-500 font-[Poppins-Medium]">
                Add Time Slot
              </Text>
            </TouchableOpacity>
          </View>
              )}

              {/* Day-wise Time Slots (if day-wise toggle is ON) */}
              {useDayWiseTimeSlots && (
                <View className="">
                  <Text className="text-sm mb-6 text-secondary-600 font-[Poppins-Medium]">
                    Time Slots by Day
                  </Text>

                  {selectedDays.map((day) => (
                    <View
                      key={day}
                      className="mb-8 border border-gray-200 p-4 rounded-xl"
                    >
                      <View className="flex-row justify-between items-center mb-3">
                        <Text className="text-sm text-secondary-600 absolute left-0 bottom-1 bg-white p-1 font-[Poppins-Medium]">
                          {day}
                        </Text>
                      </View>

                      {!timeSlotsByDay[day] ||
                      timeSlotsByDay[day].length === 0 ? (
                        <Text className="text-secondary-500 mb-3">
                          No time slots added
                        </Text>
                      ) : (
                        timeSlotsByDay[day].map((slot, index) => (
                          <View
                            key={index}
                            className="flex-row items-center mb-4"
                          >
                            <TouchableOpacity
                              className="border border-gray-300 rounded-xl p-5  flex-row justify-between items-center"
                              onPress={() =>
                                openDayTimePicker(day, index, "start")
                              }
                            >
                              <Text>{slot.start}</Text>
                              <Image
                                source={images.down}
                                className="w-[16px] h-[10px] ml-3"
                                tintColor="#6B7280"
                              />
                            </TouchableOpacity>

                            <Text className="mx-4">to</Text>

                            <TouchableOpacity
                              className="border border-gray-300 rounded-xl p-5 flex-row justify-between items-center"
                              onPress={() =>
                                openDayTimePicker(day, index, "end")
                              }
                            >
                              <Text>{slot.end}</Text>
                              <Image
                                source={images.down}
                                className="w-[16px] h-[10px] ml-3"
                                tintColor="#6B7280"
                              />
                            </TouchableOpacity>

                            {index > 0 && (
                              <TouchableOpacity
                                onPress={() => removeDayTimeSlot(day, index)}
                                className="ml-2 p-2"
                              >
                                <Image
                                  source={images.trash}
                                  className="w-5 h-5"
                                  style={{ tintColor: "#FF4B4B" }}
                                />
                              </TouchableOpacity>
                            )}
                          </View>
                        ))
                      )}
                      <View className="flex-row w-full items-center justify-between">
                        <TouchableOpacity
                          onPress={() => addDayTimeSlot(day)}
                          className="flex-row items-center mt-2"
                        >
                          <Image
                            source={images.plus}
                            className="w-4 h-4 mr-2 mb-1"
                            tintColor="#159AFF"
                          />
                          <Text className="text-primary-500 font-[Poppins-Medium]">
                            Add Time Slot
                          </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          onPress={() => copyDayTimeSlotsToAll(day)}
                          className="bg-gray-100 flex-row items-center rounded-lg px-3 py-1"
                        >
                          <Image
                            source={images.copy}
                            className="w-[14.5px] h-[17px] mr-1"
                            tintColor={"#62727A"}
                          />
                          <Text className="text-xs font-poppins-medium text-secondary-600">
                            Copy to All Days
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  ))}
                </View>
              )}

          {/* Service Time */}
          <View className="mb-8">
            <View className="flex-row items-center justify-start">
              <Text className="text-sm text-secondary-600 mr-1 font-[Poppins-Medium]">
                Maximum Serving Time
              </Text>
              <TouchableOpacity
                onPress={() => setShowServingTimeInfo(true)}
                className="p-1"
              >
                <Image 
                  source={images.info} 
                  className="w-5 h-5"
                      style={{ tintColor: "#159AFF" }}
                />
              </TouchableOpacity>
            </View>
            <View className="mb-4">
                    <Text className="font-poppins-light text-xs text-secondary-500/50">
                    We use it to Calculate the Queue Limit
                    </Text>
                  </View>
            <View className="flex-row justify-between items-center border border-gray-300 rounded-xl py-[4px] px-5 w-36">
              <TextInput
                className="text-lg"
                value={servingTime}
                onChangeText={handleServingTimeChange}
                keyboardType="numeric"
                maxLength={3}
                placeholder="00"
              />
              <Text className="text-secondary-600">mins</Text>
            </View>
            <ServingTimeInfo />
          </View>

          {/* Pricing */}
          <View className="mb-8">
            <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
              Price per head
            </Text>
            <View className="mb-4">
                    <Text className="font-poppins-light text-xs text-secondary-500/50">
                    Minimum price: 50rs
                    </Text>
                  </View>
            <View className="flex-row items-center">
              <Text className="text-2xl mr-3">₹</Text>
              <TextInput
                    className="border border-gray-300 rounded-xl p-5 w-36"
                    value={basePrice}
                    onChangeText={setBasePrice}
                    keyboardType="numeric"
                maxLength={6}
                placeholder="0.00"
              />
            </View>
          </View>
            </>
          )}

          {/* Sub-Units Configuration */}
          {hasSubUnits && (
            <View className="mb-8">
              <Text className="text-lg font-[Poppins-Medium] ">
                Sub-Unit Configuration
              </Text>
              <Text className="text-sm text-secondary-600 mb-6">
                Add details for each section of your service
              </Text>

              {/* Sub-Units List */}
              {subUnits.map((unit, index) => (
                <View
                  key={index}
                  className="mb-6 border border-gray-200 rounded-xl overflow-hidden"
                >
                  {/* Header - Always visible */}
                  <TouchableOpacity
                    onPress={() => toggleSubUnitExpansion(index)}
                    className="bg-gray-50 p-4 flex-row justify-between items-center"
                  >
                    <View className="flex-1">
                      <View className="flex-row items-center">
                        <TextInput
                          ref={(input) => {
                            subunitNameRefs.current[`subunit-${index}`] = input;
                          }}
                          className="text-base font-[Poppins-Medium]"
                          value={unit.name}
                          onChangeText={(text) =>
                            updateSubUnit(index, "name", text)
                          }
                          onTouchEnd={(e) => e.stopPropagation()}
                        />
                        <TouchableOpacity 
                          onPress={(e) => {
                            e.stopPropagation();
                            if (subunitNameRefs.current[`subunit-${index}`]) {
                              subunitNameRefs.current[`subunit-${index}`].focus();
                            }
                          }}
                        >
                          <Image
                            source={images.edit}
                            className="w-4 h-4 ml-2"
                            tintColor="#6B7280"
                          />
                        </TouchableOpacity>
                      </View>
                    </View>
                    <View className="flex-row items-center justify-center">
                      {/* Only show delete button if not the first subunit */}
                      {index > 0 && (
                        <TouchableOpacity
                          onPress={() => removeSubUnit(index)}
                          className="mr-4 p-2 bg-danger-100 rounded-full"
                          hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
                        >
                          <Image
                            source={images.trash}
                            className="w-5 h-5"
                            style={{ tintColor: "#FF4B4B" }}
                          />
                        </TouchableOpacity>
                      )}
                      <TouchableOpacity
                        hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
                        onPress={() => toggleSubUnitExpansion(index)}
                      >
                        <Image
                          source={images.down}
                          className={`w-[18px] h-[12px] ${expandedUnitIndex === index ? "rotate-180" : ""}`}
                          tintColor="#6B7280"
                        />
                      </TouchableOpacity>
                    </View>
                  </TouchableOpacity>

                  {/* Expanded Content */}
                  {expandedUnitIndex === index && (
                    <View className="p-4 bg-white">
                      {/* Working Days */}
                      <View className="mb-6">
                        <Text className="text-sm mb-4 text-secondary-600 font-[Poppins-Medium]">
                          Working Days
                        </Text>
                        <View className="flex-row justify-between mb-4">
                          {weekDays.map((day) => {
                            // Create a safe reference to selectedDays that's guaranteed to be an array
                            const safeSelectedDays = Array.isArray(selectedDays) ? selectedDays : weekDays.slice(1, 6).map(d => d.full);
                            
                            // Safe access to unit.selectedDays with fallback to safeSelectedDays
                            const unitSelectedDays = 
                              (unit && unit.selectedDays && Array.isArray(unit.selectedDays)) 
                                ? unit.selectedDays 
                                : [...safeSelectedDays];
                            
                            // Now it's safe to call includes
                            const isSelected = unitSelectedDays.includes(day.full);

                            return (
                              <TouchableOpacity
                                key={day.full}
                                onPress={() => {
                                  const isAdding = !unitSelectedDays.includes(day.full);
                                  const newSelectedDays = isAdding
                                    ? [...unitSelectedDays, day.full]
                                    : unitSelectedDays.filter((d) => d !== day.full);

                                  // Update selected days
                                  updateSubUnit(index, "selectedDays", newSelectedDays);

                                  // If adding a new day and using day-wise time slots,
                                  // initialize its time slots
                                  if (isAdding && unit.useDayWiseTimeSlots) {
                                    const updatedUnit = { ...unit };
                                    
                                    // Initialize dayWiseAvailableHours if it doesn't exist
                                    if (!updatedUnit.dayWiseAvailableHours) {
                                      updatedUnit.dayWiseAvailableHours = {};
                                    }

                                    // Add default time slot for the new day
                                    updatedUnit.dayWiseAvailableHours[day.full] = [
                                      { start: "09:00 AM", end: "05:00 PM" }
                                    ];

                                    updateSubUnit(
                                      index,
                                      "dayWiseAvailableHours",
                                      updatedUnit.dayWiseAvailableHours
                                    );
                                  }
                                }}
                                className={`w-10 h-10 rounded-full border-2 justify-center items-center
                                  ${
                                    isSelected
                                      ? "bg-primary-500 border-primary-500"
                                      : "border-gray-300"
                                  }`}
                              >
                                <Text
                                  className={`font-[Poppins-Medium] text-sm
                                  ${isSelected ? "text-white" : "text-gray-500"}
                                `}
                                >
                                  {day.short}
                                </Text>
                              </TouchableOpacity>
                            );
                          })}
                        </View>
                      </View>

                      {/* Day-wise Time Slots Toggle */}
                      <TouchableOpacity
                        onPress={() => {
                          const updatedUnit = { ...unit };

                          // Initialize dayWiseAvailableHours if toggling on
                          if (!unit.useDayWiseTimeSlots) {
                            updatedUnit.useDayWiseTimeSlots = true;
                            updatedUnit.dayWiseAvailableHours = {};

                            // Ensure selectedDays is initialized
                            const unitSelectedDays = (updatedUnit.selectedDays && Array.isArray(updatedUnit.selectedDays)) 
                              ? updatedUnit.selectedDays 
                              : [...selectedDays]; // Use global selectedDays as fallback
                            
                            unitSelectedDays.forEach((day) => {
                              // Convert from the availableHours format to TimeSlot format
                              const start = updatedUnit.availableHours[day]?.[0] || "09:00 AM";
                              const end = updatedUnit.availableHours[day]?.[1] || "05:00 PM";
                              updatedUnit.dayWiseAvailableHours![day] = [{ start, end }];
                            });
                          } else {
                            // If toggling off, simply set the flag to false
                            updatedUnit.useDayWiseTimeSlots = false;
                          }

                          updateSubUnit(index, "useDayWiseTimeSlots", updatedUnit.useDayWiseTimeSlots);
                          updateSubUnit(index, "dayWiseAvailableHours", updatedUnit.dayWiseAvailableHours);
                        }}
                        className="mb-4"
                      >
                        <View className="flex-row p-5 bg-secondary-600/10 rounded-xl justify-center items-center mb-2">
                          <Text className="text-base text-secondary-500 font-[Poppins-Medium] mr-4">
                            Add day-wise time slots
                          </Text>
                          <View
                            style={{
                              width: 50,
                              flexDirection: "row",
                              alignItems: "center",
                              backgroundColor: unit.useDayWiseTimeSlots
                                ? "#159AFF"
                                : "#E5E5E5",
                              borderRadius: 20,
                              paddingVertical: 4,
                              paddingHorizontal: 4,
                            }}
                          >
                            {unit.useDayWiseTimeSlots ? (
                              <>
                                <View style={{ flex: 1 }} />
                                <View
                                  style={{
                                    width: 17,
                                    height: 17,
                                    borderRadius: 10,
                                    backgroundColor: "white",
                                  }}
                                />
                              </>
                            ) : (
                              <>
                                <View
                                  style={{
                                    width: 17,
                                    height: 17,
                                    borderRadius: 10,
                                    backgroundColor: "white",
                                  }}
                                />
                                <View style={{ flex: 1 }} />
                              </>
                            )}
                          </View>
                        </View>
                      </TouchableOpacity>

                      {/* Regular Time Slots (if day-wise toggle is OFF) */}
                      {!unit.useDayWiseTimeSlots && (
                        <View className="mb-6">
                          <Text className="text-sm mb-4 text-secondary-600 font-[Poppins-Medium]">
                            Time Slots
                          </Text>
                          {!unit.timeSlots || unit.timeSlots.length === 0 ? (
                            <View className="mb-3">
                              <TouchableOpacity
                                onPress={() => {
                                  const updatedUnit = { ...unit };
                                  updatedUnit.timeSlots = [
                                    { start: "09:00 AM", end: "05:00 PM" },
                                  ];
                                  updateSubUnit(
                                    index,
                                    "timeSlots",
                                    updatedUnit.timeSlots
                                  );
                                }}
                                className="flex-row items-center mt-2"
                              >
                                <Image
                                  source={images.plus}
                                  className="w-4 h-4 mr-2 mb-1"
                                  tintColor="#159AFF"
                                />
                                <Text className="text-primary-500 font-[Poppins-Medium]">
                                  Add First Time Slot
                                </Text>
                              </TouchableOpacity>
                            </View>
                          ) : (
                            unit.timeSlots.map((slot, slotIndex) => (
                              <View
                                key={slotIndex}
                                className="flex-row items-center mb-4"
                              >
                                <TouchableOpacity
                                  className="border border-gray-300 rounded-xl p-5 flex-row justify-between items-center"
                                  onPress={() => {
                                    setActiveTimeSlot({
                                      index: slotIndex,
                                      field: "start",
                                      unitIndex: index,
                                    });
                                    setShowTimePicker(true);
                                  }}
                                >
                                  <Text>{slot.start}</Text>
                                  <Image
                                    source={images.down}
                                    className="w-[16px] h-[10px] ml-2"
                                    tintColor="#6B7280"
                                  />
                                </TouchableOpacity>

                                <Text className="mx-3">to</Text>

                                <TouchableOpacity
                                  className="border border-gray-300 rounded-xl p-5 flex-row justify-between items-center"
                                  onPress={() => {
                                    setActiveTimeSlot({
                                      index: slotIndex,
                                      field: "end",
                                      unitIndex: index,
                                    });
                                    setShowTimePicker(true);
                                  }}
                                >
                                  <Text>{slot.end}</Text>
                                  <Image
                                    source={images.down}
                                    className="w-[16px] h-[10px] ml-2"
                                    tintColor="#6B7280"
                                  />
                                </TouchableOpacity>

                                {slotIndex > 0 && (
                                  <TouchableOpacity
                                    onPress={() => {
                                      const updatedUnit = { ...unit };
                                      updatedUnit.timeSlots =
                                        unit.timeSlots!.filter(
                                          (_, i) => i !== slotIndex
                                        );
                                      updateSubUnit(
                                        index,
                                        "timeSlots",
                                        updatedUnit.timeSlots
                                      );
                                    }}
                                    className="ml-2 p-2"
                                  >
                                    <Image
                                      source={images.trash}
                                      className="w-5 h-5"
                                      style={{ tintColor: "#FF4B4B" }}
                                    />
                                  </TouchableOpacity>
                                )}
                              </View>
                            ))
                          )}

                          {unit.timeSlots && unit.timeSlots.length > 0 && (
                            <TouchableOpacity
                              onPress={() => {
                                const updatedUnit = { ...unit };
                                if (!updatedUnit.timeSlots) {
                                  updatedUnit.timeSlots = [];
                                }
                                updatedUnit.timeSlots.push({
                                  start: "09:00 AM",
                                  end: "05:00 PM",
                                });
                                updateSubUnit(
                                  index,
                                  "timeSlots",
                                  updatedUnit.timeSlots
                                );
                              }}
                              className="flex-row items-center mt-2"
                            >
                              <Image
                                source={images.plus}
                                className="w-4 h-4 mr-2 mb-1"
                                tintColor="#159AFF"
                              />
                              <Text className="text-primary-500 font-[Poppins-Medium]">
                                Add Time Slot
                              </Text>
                            </TouchableOpacity>
                          )}
                        </View>
                      )}

                      {/* Day-wise Time Slots (if day-wise toggle is ON) */}
                      {unit.useDayWiseTimeSlots && (
                        <View className="mb-6">
                          <Text className="text-sm mb-6 text-secondary-600 font-[Poppins-Medium]">
                            Time Slots by Day
                          </Text>

                          {(Array.isArray(unit.selectedDays) ? unit.selectedDays : [...selectedDays]).map((day) => (
                            <View
                              key={day}
                              className="mb-8 border border-gray-200 p-4 rounded-xl"
                            >
                              <View className="flex-row justify-between items-center mb-2">
                                <Text className="text-sm text-secondary-600 absolute left-0 bottom-1 bg-white p-1 font-[Poppins-Medium]">
                                  {day}
                                </Text>
                              </View>

                              {!unit.dayWiseAvailableHours || 
                               !unit.dayWiseAvailableHours[day] ||
                               !Array.isArray(unit.dayWiseAvailableHours[day]) ||
                               unit.dayWiseAvailableHours[day].length === 0 ? (
                                <Text className="text-secondary-500 mb-2">
                                  No time slots added
                                </Text>
                              ) : (
                                unit.dayWiseAvailableHours[day].map(
                                  (slot, slotIndex) => (
                                    <View
                                      key={slotIndex}
                                      className="flex-row items-center mb-4"
                                    >
                                      <TouchableOpacity
                                        className="border border-gray-300 rounded-xl p-5 mr-1 flex-row justify-between items-center"
                                        onPress={() => {
                                          setActiveTimeSlot({
                                            index: slotIndex,
                                            field: "start",
                                            unitIndex: index,
                                            day,
                                          });
                                          setShowTimePicker(true);
                                        }}
                                      >
                                        <Text>{slot.start}</Text>
                                        <Image
                                          source={images.down}
                                          className="w-[16px] h-[10px] ml-2"
                                          tintColor="#6B7280"
                                        />
                                      </TouchableOpacity>

                                      <Text className="mx-2">to</Text>

                                      <TouchableOpacity
                                        className="border border-gray-300 rounded-xl p-5  flex-row justify-between items-center"
                                        onPress={() => {
                                          setActiveTimeSlot({
                                            index: slotIndex,
                                            field: "end",
                                            unitIndex: index,
                                            day,
                                          });
                                          setShowTimePicker(true);
                                        }}
                                      >
                                        <Text>{slot.end}</Text>
                                        <Image
                                          source={images.down}
                                          className="w-[16px] h-[10px] ml-2"
                                          tintColor="#6B7280"
                                        />
                                      </TouchableOpacity>

                                      {slotIndex > 0 && (
                                        <TouchableOpacity
                                          onPress={() => {
                                            const updatedUnit = { ...unit };
                                            if (
                                              !updatedUnit.dayWiseAvailableHours
                                            ) {
                                              updatedUnit.dayWiseAvailableHours =
                                                {};
                                            }
                                            if (
                                              !updatedUnit
                                                .dayWiseAvailableHours[day]
                                            ) {
                                              updatedUnit.dayWiseAvailableHours[
                                                day
                                              ] = [];
                                            } else {
                                              updatedUnit.dayWiseAvailableHours[
                                                day
                                              ] =
                                                updatedUnit.dayWiseAvailableHours[
                                                  day
                                                ].filter(
                                                  (_, i) => i !== slotIndex
                                                );
                                            }
                                            updateSubUnit(
                                              index,
                                              "dayWiseAvailableHours",
                                              updatedUnit.dayWiseAvailableHours
                                            );
                                          }}
                                          className="ml-2 p-1"
                                        >
                                          <Image
                                            source={images.trash}
                                            className="w-5 h-5"
                                            style={{ tintColor: "#FF4B4B" }}
                                          />
                                        </TouchableOpacity>
                                      )}
                                    </View>
                                  )
                                )
                              )}

                              <View className="flex-row mt-2 items-center justify-between w-full">
                                <TouchableOpacity
                                  onPress={() => {
                                    const updatedUnit = { ...unit };
                                    if (!updatedUnit.dayWiseAvailableHours) {
                                      updatedUnit.dayWiseAvailableHours = {};
                                    }
                                    if (!updatedUnit.dayWiseAvailableHours[day]) {
                                      updatedUnit.dayWiseAvailableHours[day] = [];
                                    }
                                    updatedUnit.dayWiseAvailableHours[day].push({
                                      start: "09:00 AM",
                                      end: "05:00 PM",
                                    });
                                    updateSubUnit(
                                      index,
                                      "dayWiseAvailableHours",
                                      updatedUnit.dayWiseAvailableHours
                                    );
                                  }}
                                  className="flex-row items-center mt-1"
                                >
                                  <Image
                                    source={images.plus}
                                    className="w-3 h-3 mr-1 mb-1"
                                    tintColor="#159AFF"
                                  />
                                  <Text className="text-primary-500 text-sm font-[Poppins-Medium]">
                                    Add Time Slot
                                  </Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                  onPress={() => {
                                    Alert.alert(
                                      "Copy to All Days",
                                      `Copy ${day}'s time slots to all other days?`,
                                      [
                                        {
                                          text: "Cancel",
                                          style: "cancel",
                                        },
                                        {
                                          text: "Copy",
                                          onPress: () => {
                                            const updatedUnit = { ...unit };
                                            if (!updatedUnit.dayWiseAvailableHours) {
                                              updatedUnit.dayWiseAvailableHours = {};
                                            }

                                            const sourceDaySlots =
                                              updatedUnit.dayWiseAvailableHours[day] || [];
                                            const unitSelectedDays =
                                              updatedUnit.selectedDays || selectedDays;

                                            unitSelectedDays.forEach((targetDay) => {
                                              if (targetDay !== day) {
                                                updatedUnit.dayWiseAvailableHours![targetDay] = [
                                                  ...sourceDaySlots,
                                                ];
                                              }
                                            });

                                            updateSubUnit(
                                              index,
                                              "dayWiseAvailableHours",
                                              updatedUnit.dayWiseAvailableHours
                                            );
                                          }
                                        }
                                      ]
                                    );
                                  }}
                                  className="bg-gray-100 flex-row rounded-lg px-3 py-1"
                                >
                                  <Image
                                    source={images.copy}
                                    className="w-[12.5px] h-[15px] mr-1"
                                    tintColor={"#62727A"}
                                  />
                                  <Text className="text-secondary-600 text-sm font-poppins-medium">
                                    Copy to All Days
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            </View>
                          ))}
                        </View>
                      )}

                      {/* Service Time */}
                      <View className="mb-6">
                        <View className="flex-row items-center justify-start">
                          <Text className="text-sm text-secondary-600 mr-1 font-[Poppins-Medium]">
                            Average Serve Time
                          </Text>
                          <TouchableOpacity
                            onPress={() => setShowServingTimeInfo(true)}
                            className="p-1"
                          >
                            <Image
                              source={images.info}
                              className="w-5 h-5"
                              style={{ tintColor: "#159AFF" }}
                            />
                          </TouchableOpacity>
                        </View>
                        <View className="mb-3">
                          <Text className="font-poppins-light text-xs text-secondary-500/50">
                            We use it to calculate the queue limit
                          </Text>
                        </View>
                        <View className="flex-row justify-between items-center border border-gray-300 rounded-xl py-[4px] px-5 w-36">
                          <TextInput
                            className="text-lg"
                            value={unit.avgServeTime}
                            onChangeText={(text) => {
                              const numericOnly = text.replace(/[^0-9]/g, "");
                              if (numericOnly.length <= 3) {
                                updateSubUnit(
                                  index,
                                  "avgServeTime",
                                  numericOnly
                                );
                              }
                            }}
                            keyboardType="numeric"
                            maxLength={3}
                            placeholder="00"
                          />
                          <Text className="text-secondary-600">mins</Text>
                        </View>
                      </View>

                      {/* Pricing */}
                      <View className="mb-6">
                        <Text className="text-sm mb-1 text-secondary-600 font-[Poppins-Medium]">
                          Price per Booking
                        </Text>
                        <View className="mb-3">
                          <Text className="font-poppins-light text-xs text-secondary-500/50">
                            Minimum price: ₹50
                          </Text>
                        </View>
                        <View className="flex-row items-center">
                          <Text className="text-2xl mr-3">₹</Text>
                          <TextInput
                            className="border border-gray-300 rounded-xl p-4 w-32"
                            value={unit.pricePerHead}
                            onChangeText={(text) =>
                              updateSubUnit(index, "pricePerHead", text)
                            }
                            keyboardType="numeric"
                            maxLength={6}
                            placeholder="0.00"
                          />
                        </View>
                      </View>

                      {/* Copy to All Units Button */}
                      <View className="flex-row mt-6">
                        <TouchableOpacity 
                          onPress={() => {
                            Alert.alert(
                              "Copy to All Units",
                              `Copy ${unit.name}'s configuration to all other units?`,
                              [
                                {
                                  text: "Cancel",
                                  style: "cancel"
                                },
                                {
                                  text: "Copy",
                                  onPress: () => {
                                    const currentUnitConfig = {...unit};
                                    // Copy configuration to all other units
                                    setSubUnits(prev => 
                                      prev.map((u, i) => 
                                        i !== index ? {
                                          ...currentUnitConfig,
                                          name: u.name // Keep the original name
                                        } : u
                                      )
                                    );
                                    Alert.alert("Success", "Configuration copied to all units!");
                                  }
                                }
                              ]
                            );
                          }}
                          className="bg-gray-100 flex-row  w-full items-center justify-center rounded-lg px-4 py-3"
                        >
                          <Image
                            source={images.copy}
                            className="w-[14.5px] h-[17px] mr-2"
                            tintColor={"#62727A"}
                          />
                          <Text className="text-secondary-600 font-poppins-medium">
                            Copy to All Units
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  )}
                </View>
              ))}

              {/* Add Sub-Unit Button */}
              <TouchableOpacity
                onPress={addSubUnit}
                className="flex-row items-center justify-center mx-32 bg-primary-500/10 p-4 rounded-2xl mt-2"
              >
                <Image
                  source={images.plus}
                  className="w-4 h-4 mr-2 mb-1"
                  tintColor="#159AFF"
                />
                <Text className="text-primary-500 font-[Poppins-Medium]">
                  Add Sub-Unit
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Save Button */}
      {!keyboardVisible && (
        <View className="p-4 border-t justify-center items-center border-gray-200">
          <ButtonBlueMain
            label={isSaving ? "Saving..." : "Save & Continue"}
            onPress={handleSave}
            bgVariant="primary"
            textVariant="primary"
            className="w-[350px] h-[80px]"
            disabled={isSaving}
          />
        </View>
      )}
      <TimePicker
        visible={showTimePicker}
        onClose={() => {
          setShowTimePicker(false);
          setActiveTimeSlot(null);
        }}
        onSelect={handleTimeSelect}
        initialTime={
          activeTimeSlot
            ? activeTimeSlot.unitIndex !== undefined
              ? activeTimeSlot.day && subUnits[activeTimeSlot.unitIndex]?.dayWiseAvailableHours?.[activeTimeSlot.day]?.[activeTimeSlot.index]?.[activeTimeSlot.field] // Day-wise subunit
                ? subUnits[activeTimeSlot.unitIndex]?.dayWiseAvailableHours?.[activeTimeSlot.day]?.[activeTimeSlot.index]?.[activeTimeSlot.field]
                : subUnits[activeTimeSlot.unitIndex]?.timeSlots?.[activeTimeSlot.index]?.[activeTimeSlot.field] // Regular subunit
                  ? subUnits[activeTimeSlot.unitIndex]?.timeSlots?.[activeTimeSlot.index]?.[activeTimeSlot.field]
                  : subUnits[activeTimeSlot.unitIndex]?.availableHours?.[
                      Object.keys(
                        subUnits[activeTimeSlot.unitIndex]?.availableHours || {}
                      )[0]
                    ]?.[activeTimeSlot.index === 0 ? 0 : 1]
              : activeTimeSlot.day
                ? timeSlotsByDay?.[activeTimeSlot.day]?.[activeTimeSlot.index]?.[activeTimeSlot.field] // Day-wise main
                : timeSlots?.[activeTimeSlot.index]?.[activeTimeSlot.field] // Regular main
            : undefined
        }
      />
    </SafeAreaView>
  );
}
