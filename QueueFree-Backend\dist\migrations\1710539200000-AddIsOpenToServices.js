"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddIsOpenToServices1710539200000 = void 0;
class AddIsOpenToServices1710539200000 {
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "services" ADD COLUMN "isOpen" boolean DEFAULT false`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "services" DROP COLUMN "isOpen"`);
    }
}
exports.AddIsOpenToServices1710539200000 = AddIsOpenToServices1710539200000;
//# sourceMappingURL=1710539200000-AddIsOpenToServices.js.map