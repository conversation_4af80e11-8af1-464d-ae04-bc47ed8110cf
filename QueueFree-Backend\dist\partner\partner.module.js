"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartnerModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const service_entity_1 = require("./entities/service.entity");
const service_setup_entity_1 = require("./entities/service-setup.entity");
const bank_details_entity_1 = require("./entities/bank-details.entity");
const queue_entity_1 = require("./entities/queue.entity");
const review_entity_1 = require("./entities/review.entity");
const partner_service_1 = require("./partner.service");
const partner_controller_1 = require("./partner.controller");
const upload_module_1 = require("../services/upload.module");
const redis_module_1 = require("../services/redis/redis.module");
const queue_flow_module_1 = require("../services/queue-flow/queue-flow.module");
const scheduler_module_1 = require("../services/scheduler/scheduler.module");
let PartnerModule = class PartnerModule {
};
exports.PartnerModule = PartnerModule;
exports.PartnerModule = PartnerModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([service_entity_1.Service, service_setup_entity_1.ServiceSetup, bank_details_entity_1.BankDetails, queue_entity_1.Queue, review_entity_1.Review]),
            upload_module_1.UploadModule,
            redis_module_1.RedisModule,
            queue_flow_module_1.QueueFlowModule,
            scheduler_module_1.SchedulerModule,
        ],
        providers: [partner_service_1.PartnerService],
        controllers: [partner_controller_1.PartnerController],
        exports: [partner_service_1.PartnerService],
    })
], PartnerModule);
//# sourceMappingURL=partner.module.js.map