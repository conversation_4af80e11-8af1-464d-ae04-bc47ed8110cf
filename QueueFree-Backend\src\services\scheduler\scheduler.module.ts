import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { SchedulerService } from './scheduler.service';
import { RedisModule } from '../redis/redis.module';

/**
 * This module has been largely replaced by the CommonServicesModule.
 * It's kept here for backward compatibility but should be gradually phased out.
 */
@Module({
  imports: [
    ScheduleModule.forRoot(),
    RedisModule,
  ],
  providers: [],
  exports: [],
})
export class SchedulerModule {}
