import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  TouchableOpacity,
  Image,
  StatusBar,
  TextInput,
  ScrollView,
  Alert,
  Platform,
  Keyboard,
  Animated,
  TouchableWithoutFeedback,
  PanResponder,
  Dimensions,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { images } from '@/constants';
import ButtonBlueMain from '@/components/ButtonBlueMain';
import * as SecureStore from 'expo-secure-store';
import BottomSheetModal from '@/components/BottomSheetModal';

const indianStates = [
  "Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh",
  "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka",
  "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "Meghalaya", "Mizoram",
  "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu",
  "Telangana", "Tripura", "Uttar Pradesh", "Uttarakhand", "West Bengal",
  "Andaman and Nicobar Islands", "Chandigarh", "Dadra and Nagar Haveli",
  "Daman and Diu", "Delhi", "Jammu and Kashmir", "Ladakh", "Lakshadweep",
  "Puducherry"
];

export default function EnterAddressScreen() {
  const params = useLocalSearchParams();
  const { latitude, longitude, area, fullAddress } = params;

  // Initialize address state
  const [address, setAddress] = useState({
    buildingNo: '',
    locality: (area as string) || '',
    city: '',
    state: 'Select State', // Default state text
    pincode: '',
    googleMapsLink: '', // Add new field
  });

  // Add new state to track edit mode
  const [isEditMode, setIsEditMode] = useState(false);

  useEffect(() => {
    checkEditMode();
  }, []);

  const checkEditMode = async () => {
    const formData = await SecureStore.getItemAsync('serviceFormData');
    if (formData) {
      const data = JSON.parse(formData);
      setIsEditMode(!!data.isEdit);
    }
  };

  // Load existing address if available
  useEffect(() => {
    const loadExistingAddress = async () => {
      try {
        const savedAddress = await SecureStore.getItemAsync('serviceAddress');
        if (savedAddress) {
          const parsedAddress = JSON.parse(savedAddress);
          if (parsedAddress.details) {
            setAddress({
              buildingNo: parsedAddress.details.buildingNo || '',
              locality: (area as string) || parsedAddress.details.locality || '',
              city: parsedAddress.details.city || '',
              state: parsedAddress.details.state || 'Select State',
              pincode: parsedAddress.details.pincode || '',
              googleMapsLink: parsedAddress.details.googleMapsLink || ''
            });
          }
        }
      } catch (error) {
        console.error('Error loading existing address:', error);
      }
    };

    loadExistingAddress();
  }, [area]);

  const [keyboardVisible, setKeyboardVisible] = useState(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true); // Close picker when keyboard shows
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);
  const [showStates, setShowStates] = useState(false);
  const slideAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;

  const screenHeight = Dimensions.get('window').height;
  const [isExpanded, setIsExpanded] = useState(false);

  const expandSheet = () => {
    setIsExpanded(true);
    Animated.spring(translateY, {
      toValue: -screenHeight*0,
      damping: 20,
      mass: 0.8,
      stiffness: 100,
      useNativeDriver: true,
    }).start();
  };

  const collapseSheet = () => {
    setIsExpanded(false);
    Animated.spring(translateY, {
      toValue: 0,
      damping: 20,
      mass: 0.8,
      stiffness: 100,
      useNativeDriver: true,
    }).start();
  };

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderMove: (_, gestureState) => {
        const newPosition = Math.max(-screenHeight * 0.4, Math.min(gestureState.dy, 50));
        translateY.setValue(newPosition);
      },
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dy > 100) {
          hideStatesList();
        } else if (gestureState.dy < -50 && !isExpanded) {
          expandSheet();
        } else if (gestureState.dy > 50 && isExpanded) {
          collapseSheet();
        } else {
          Animated.spring(translateY, {
            toValue: isExpanded ? -screenHeight * 0.4 : 0,
            damping: 20,
            mass: 0.8,
            stiffness: 100,
            useNativeDriver: true,
          }).start();
        }
      },
    })
  ).current;

  const showStatesList = () => {
    setShowStates(true);
    setIsExpanded(false);
    translateY.setValue(0);
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 1,
        damping: 20,
        mass: 0.8,
        stiffness: 100,
        useNativeDriver: true,
      })
    ]).start();
  };

  const hideStatesList = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        damping: 20,
        mass: 0.8,
        stiffness: 100,
        useNativeDriver: true,
      })
    ]).start(() => {
      setShowStates(false);
    });
  };

  const handleStatePress = () => {
    setShowStates(true);
  };

  const validateForm = () => {
    if (!address.buildingNo.trim()) {
      Alert.alert('Error', 'Please enter building number/floor');
      return false;
    }
    if (!address.locality.trim()) {
      Alert.alert('Error', 'Please enter area/sector/locality');
      return false;
    }
    if (!address.city.trim()) {
      Alert.alert('Error', 'Please enter city');
      return false;
    }
    if (!address.pincode.trim() || address.pincode.length !== 6) {
      Alert.alert('Error', 'Please enter valid 6-digit pincode');
      return false;
    }
    return true;
  };

  useEffect(() => {
    loadSavedLocation();
  }, []);

  const loadSavedLocation = async () => {
    try {
      const savedLocationData = await SecureStore.getItemAsync('selectedLocation');
      if (savedLocationData) {
        const parsed = JSON.parse(savedLocationData);
        if (parsed.area) {
          setAddress(prev => ({
            ...prev,
            locality: parsed.area
          }));
        }
        console.log('Saved location loaded:', parsed);
      }
    } catch (error) {
      console.error('Error loading saved location:', error);
    }
  };

  const handleSaveAddress = async () => {
    if (!validateForm()) return;
  
    try {
      // Get saved location data for coordinates and full address
      const savedLocationData = await SecureStore.getItemAsync('selectedLocation');
      const locationDetails = savedLocationData ? JSON.parse(savedLocationData) : null;
  
      const completeAddress = {
        details: {
          buildingNo: address.buildingNo,
          locality: address.locality,
          city: address.city,
          state: address.state,
          pincode: address.pincode
        },
        coordinates: locationDetails?.coordinates || {
          latitude: Number(latitude),
          longitude: Number(longitude)
        },
        area: address.locality, // Use locality as area for consistency
        fullAddress: locationDetails?.fullAddress || fullAddress || '',
        googleMapsLink: address.googleMapsLink // Add Google Maps link
      };
  
      // Save complete address
      await SecureStore.setItemAsync('serviceAddress', JSON.stringify(completeAddress));
  
      // Clear temporary location data
      await SecureStore.deleteItemAsync('selectedLocation');
      
      if (isEditMode) {
        router.push('/(root)/edit-service');
      } else {
        router.push('/service-setup/step1');
      }
    } catch (error) {
      console.error('Error saving address:', error);
      Alert.alert('Error', 'Failed to save address');
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <View className="px-8 pt-8 pb-4 border-secondary-400">
        <View className="w-full flex-row">
          <TouchableOpacity
            className="flex-row items-center absolute left-0 border border-secondary-600 w-12 h-12 rounded-full justify-center"
            onPress={() => router.back()}
          >
            <Image source={images.back} className="w-4 h-4" />
          </TouchableOpacity>
          <View className="flex-1 items-center mt-2">
            <Text className="font-poppins-medium text-2xl mb-4">
              Enter Complete Address
            </Text>
          </View>
        </View>
      </View>

      <ScrollView className="flex-1 px-6 py-4">
        <Text className="text-sm mb-2 text-secondary-600 font-poppins-medium">
          Building No. / Floor
        </Text>
        <TextInput
          className="border border-gray-300 rounded-xl p-4 mb-8 font-poppins-regular"
          value={address.buildingNo}
          onChangeText={(text) => setAddress({ ...address, buildingNo: text })}
          placeholder="Enter building number or floor"
        />

        <Text className="text-sm mb-2 text-secondary-600 font-poppins-medium">
          Area / Sector / Locality
        </Text>
        <TextInput
          className="border border-gray-300 rounded-xl p-4 mb-8 font-poppins-regular"
          value={address.locality}
          onChangeText={(text) => setAddress({ ...address, locality: text })}
          placeholder="Enter area, sector or locality"
        />

        <View className="flex-row justify-between mb-8">
          <View className="flex-1 mr-2">
            <Text className="text-sm mb-2 text-secondary-600 font-poppins-medium">
              City
            </Text>
            <TextInput
              className="border border-gray-300 rounded-xl p-4 font-poppins-regular"
              value={address.city}
              onChangeText={(text) => setAddress({ ...address, city: text })}
              placeholder="Enter city"
            />
          </View>

          <View className="flex-1 ml-2">
            <Text className="text-sm mb-2 text-secondary-600 font-poppins-medium">
              State
            </Text>
            <TouchableOpacity
              className="border border-gray-300 rounded-xl p-4 justify-between flex-row items-center"
              onPress={handleStatePress}
            >
              <Text className="font-poppins-regular">
                {address.state}
              </Text>
              <Image 
                source={images.down} 
                className="w-[16px] h-[10px]"
                style={{ tintColor: '#6B7280' }}
              />
            </TouchableOpacity>
          </View>
        </View>

        <Text className="text-sm mb-2 text-secondary-600 font-poppins-medium">
          PIN Code
        </Text>
        <TextInput
          className="border border-gray-300 rounded-xl p-4 mb-8 font-poppins-regular"
          value={address.pincode}
          onChangeText={(text) => setAddress({ ...address, pincode: text })}
          placeholder="Enter 6-digit PIN code"
          keyboardType="numeric"
          maxLength={6}
        />

        <Text className="text-sm mb-2 text-secondary-600 font-poppins-medium">
          Google Maps Location Link (Optional)
        </Text>
        <View className="mb-2">
          <Text className="font-poppins-light text-xs text-secondary-500/50">
            Adding your Google Maps location link will help customers find your location faster
          </Text>
        </View>
        <TextInput
          className="border border-gray-300 rounded-xl p-4 mb-8 font-poppins-regular"
          value={address.googleMapsLink}
          onChangeText={(text) => setAddress({ ...address, googleMapsLink: text })}
          placeholder="Paste your Google Maps location link here"
          autoCapitalize="none"
        />
      </ScrollView>

      {!keyboardVisible && (
      <View 
        className="px-4 py-4 border-t justify-center items-center border-gray-200 bg-white"
        style={{
          paddingBottom: Platform.OS === 'ios' ? 20 : 15,
          position: 'relative', // Lower z-index
        }}
      >
        <ButtonBlueMain
          label="Save Service Address"
          onPress={handleSaveAddress}
          bgVariant="primary"
          textVariant="primary"
          className="w-[350px] h-[80px]"
        />
      </View>)}

      <BottomSheetModal
        visible={showStates}
        onClose={() => setShowStates(false)}
        headerText="Select State"
        items={indianStates}
        onSelectItem={(state) => setAddress({ ...address, state })}
        defaultHeight={45}
        expandedHeight={65}
      />
    </SafeAreaView>
  );
}
