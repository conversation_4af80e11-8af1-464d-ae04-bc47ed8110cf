import { CustomerService } from './customer.service';
import { JoinQueueDto } from './dto/join-queue.dto';
import { QueueFlowService } from '../services/queue-flow/queue-flow.service';
import { SchedulerService } from '../services/scheduler/scheduler.service';
export declare class CustomerController {
    private readonly customerService;
    private readonly queueFlowService;
    private readonly schedulerService;
    constructor(customerService: CustomerService, queueFlowService: QueueFlowService, schedulerService: SchedulerService);
    registerUser(body: {
        email: string;
    }): Promise<{
        status: string;
        message: string;
        user: import("./user.entity").User;
        isExisting: boolean;
    }>;
    updateLocation(data: {
        email: string;
        latitude: number;
        longitude: number;
    }): Promise<{
        success: boolean;
        message: string;
    }>;
    updateVIPStatus(data: {
        email: string;
        isVIP: boolean;
    }): Promise<{
        success: boolean;
        message: string;
        isVIP: boolean;
    }>;
    getUserProfile(email: string): Promise<{
        success: boolean;
        user: {
            id: number;
            email: string;
            fullName: string;
            mobileNumber: string;
            isVIP: boolean;
            lastLocationUpdate: Date;
        };
    }>;
    updateUserDetails(data: {
        email: string;
        fullName?: string;
        mobileNumber?: string;
    }): Promise<{
        success: boolean;
        message: string;
        user: {
            id: number;
            email: string;
            fullName: string;
            mobileNumber: string;
            isVIP: boolean;
        };
    }>;
    updateClerkId(data: {
        email: string;
        clerkId: string;
    }): Promise<{
        success: boolean;
        message: string;
        user: {
            id: number;
            email: string;
            clerkId: string;
        };
    }>;
    addToWishlist(data: {
        email: string;
        serviceId: string;
    }): Promise<{
        success: boolean;
        message: string;
        wishlist: string[];
    }>;
    removeFromWishlist(data: {
        email: string;
        serviceId: string;
    }): Promise<{
        success: boolean;
        message: string;
        wishlist: string[];
    }>;
    getWishlist(email: string): Promise<{
        status: string;
        wishlist: {
            serviceId: string;
        }[];
    }>;
    joinQueue(joinQueueDto: JoinQueueDto): Promise<{
        status: string;
        message: string;
        queueId: number;
        serviceId: number;
        uniqueSlotId: string;
        timeSlot: string;
        date: Date;
        hasSubUnits: boolean;
        subUnitId: string;
        subUnitName: string;
        position: number;
        timestamp: string;
    }>;
    getUserQueues(userId: string): Promise<{
        status: string;
        queues: any;
    }>;
    getActiveUserQueues(userId: string): Promise<{
        status: string;
        queues: any[];
    }>;
    getQueuesByStatusQuery(userId: string, status: string): Promise<{
        status: string;
        queues: any[];
    }>;
    getQueuesByStatus(userId: string, status: string): Promise<{
        status: string;
        queues: any[];
    }>;
    getQueueById(queueId: string): Promise<{
        status: string;
        queue: any;
    }>;
    getEstimatedWaitTime(queueId: string): Promise<{
        status: string;
        data: {
            queueId: number;
            waitTimeMinutes: number;
            waitTimeStatus: string;
            estimatedServeTime: string;
            position: number;
            initialPositionAtJoin: number;
        };
    }>;
    getActualServiceTime(queueId: string): Promise<{
        status: string;
        data: {
            queueId: number;
            serviceTimeMinutes: number;
            servingStartedAt: string | null;
            statusUpdatedAt: string | null;
            status: string;
        };
    }>;
    checkInQueue(queueId: string, body: {
        userId: string;
    }): Promise<any>;
    toggleCheckInQueue(queueId: string, body: {
        userId: string;
    }): Promise<any>;
    cancelQueue(queueId: string, body: {
        userId: string;
    }): Promise<{
        status: string;
        message: string;
        data: import("../partner/entities/queue.entity").Queue;
    }>;
    getServiceActiveQueues(serviceId: string, date: string): Promise<{
        status: string;
        queues: any[];
    }>;
    completeQueue(queueId: string, body: {
        userId: string;
        isCheckedIn?: boolean;
    }): Promise<{
        status: string;
        message: string;
        data: import("../partner/entities/queue.entity").Queue;
    }>;
    getServiceQueueCounts(serviceId: string, date: string, subUnitId?: string): Promise<{
        status: string;
        counts: Record<string, {
            normalCount: number;
            vipCount: number;
        }>;
    }>;
    getServiceQueuesFromRedis(serviceId: string, subUnitId?: string): Promise<{
        status: string;
        queues: any[];
    }>;
    getServiceQueuesAllStatuses(serviceId: string): Promise<{
        status: string;
        queues: any[];
    }>;
    getServiceQueuesFromRedisByStatus(serviceId: string, status: string, subUnitId?: string): Promise<{
        status: string;
        queues: any[];
    }>;
    updateExpiredQueues(): Promise<{
        updated: number;
        message: string;
    }>;
    getUserProfileByClerkId(clerkId: string): Promise<{
        status: string;
        user: {
            id: number;
            email: string;
            fullName: string;
            mobileNumber: string;
            isVIP: boolean;
            lastLocationUpdate: Date;
            clerkId: string;
        };
    }>;
    getUserMobileByClerkId(clerkId: string): Promise<{
        status: string;
        data: {
            mobileNumber: string | null;
            fullName: string | null;
            email: string;
        };
    }>;
    getQueueMobileNumber(queueId: string): Promise<{
        status: string;
        data: {
            mobileNumber: any;
            fullName: any;
            userId: any;
        };
    }>;
    getQueueByUniqueSlotId(uniqueSlotId: string): Promise<{
        status: string;
        queue: any;
    }>;
    ensureQueueConsistency(serviceId: string): Promise<{
        status: string;
        message: string;
    }>;
    getGracePeriodStatus(queueId: string): Promise<{
        status: string;
        data: any;
        message?: undefined;
    } | {
        status: string;
        message: any;
        data?: undefined;
    }>;
    markGracePeriodExpired(queueId: string): Promise<{
        status: string;
        message: any;
    }>;
    confirmPresence(queueId: string, body: {
        isPresent: boolean;
        userId: string;
    }): Promise<{
        status: string;
        message: string;
        data: import("../partner/entities/queue.entity").Queue;
    }>;
    getServingStatus(queueId: string): Promise<{
        status: string;
        data: {
            queueId: any;
            serviceId: any;
            isServing: boolean;
            servingStartedAt: any;
            estimatedEndTime: string;
            servingTimeMinutes: number;
            remainingMinutes: number;
            remainingSeconds: number;
        };
    } | {
        status: string;
        data: {
            queueId: number;
            serviceId: number;
            isServing: boolean;
            servingStartedAt: null;
            estimatedEndTime: null;
            servingTimeMinutes: number;
            remainingMinutes: number;
            remainingSeconds: number;
        };
    }>;
    getServingQueues(userId: string): Promise<{
        status: string;
        queues: any[];
    }>;
}
