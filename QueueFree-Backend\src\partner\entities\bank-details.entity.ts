import { Entity, PrimaryGeneratedColumn, Column, OneToOne, JoinColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Service } from './service.entity';

@Entity('bank_details')
export class BankDetails {
    @PrimaryGeneratedColumn()
    id: number;

    @OneToOne(() => Service)
    @JoinColumn()
    service: Service;

    @Column()
    accountNumber: string;

    @Column()
    accountHolderName: string;


    @Column()
    ifscCode: string;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}
