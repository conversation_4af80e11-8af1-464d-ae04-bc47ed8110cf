import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ScrollView,
  ActivityIndicator,
  Alert,
  Image,
} from "react-native";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import { useUser } from "@clerk/clerk-expo";
import { images } from "@/constants";

interface TimeSlot {
  timeSlot: string;
  normalQueueCount: number;
  vipQueueCount: number;
}

interface SubUnit {
  name: string;
  availableHours: {
    [day: string]: Array<{
      start: string;
      end: string;
    }>;
  };
  avgServeTime: string;
  pricePerHead: string;
  selectedDays: string[];
}

interface ServiceSetup {
  selectedDays: string[];
  timeSlots: Array<{
    start: string;
    end: string;
  }>;
  servingTime: string;
  basePrice: string;
  hasSubUnits?: boolean;
  subUnits?: SubUnit[];
  availableHours?: {
    [day: string]: Array<{
      start: string;
      end: string;
    }>;
  };
}

interface ServiceDetails {
  id: string;
  serviceName: string;
  serviceType: string;
  isOpen: boolean;
  queueInfo: {
    cost: number;
  };
}

interface MarkedDates {
  [date: string]: {
    selected?: boolean;
    marked?: boolean;
    disabled?: boolean;
    disableTouchEvent?: boolean;
    selectedColor?: string;
    dotColor?: string;
  };
}

interface CurrentQueueDetails {
  id: number;
  serviceId: number;
  serviceName: string;
  serviceType: string;
  date: string;
  timeSlot: string;
  status: string;
  isVIP: boolean;
  hasSubUnits?: boolean;
  subUnitId?: string;
  subUnitName?: string;
}

const RescheduleQueueScreen = () => {
  const router = useRouter();
  const { queueId, serviceId } = useLocalSearchParams();
  const { user } = useUser();

  const [isLoading, setIsLoading] = useState(true);
  const [currentQueueDetails, setCurrentQueueDetails] = useState<CurrentQueueDetails | null>(null);
  const [serviceDetails, setServiceDetails] = useState<ServiceDetails | null>(null);
  const [serviceSetup, setServiceSetup] = useState<ServiceSetup | null>(null);
  const [leaveDays, setLeaveDays] = useState<string[]>([]);
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string | null>(null);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [markedDates, setMarkedDates] = useState<MarkedDates>({});
  const [workingDays, setWorkingDays] = useState<number[]>([]);
  const [isCalendarLoading, setIsCalendarLoading] = useState(true);
  const [isTimeSlotsLoading, setIsTimeSlotsLoading] = useState(false);
  const [hasSubUnits, setHasSubUnits] = useState(false);
  const [subUnits, setSubUnits] = useState<SubUnit[]>([]);
  const [selectedSubUnit, setSelectedSubUnit] = useState<SubUnit | null>(null);

  useEffect(() => {
    fetchCurrentQueueDetails();
    fetchServiceDetails();
    fetchServiceSetup();
    fetchLeaveDays();
  }, []);

  useEffect(() => {
    if (serviceSetup && leaveDays !== null) {
      generateMarkedDates();
    }
  }, [serviceSetup, leaveDays, selectedSubUnit, currentQueueDetails]);

  useEffect(() => {
    if (selectedDate) {
      fetchTimeSlotsForDate(selectedDate);
    }
  }, [selectedDate]);

  const fetchCurrentQueueDetails = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(
        `http://192.168.74.138:3000/api/customer/queue/${queueId}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch current queue details");
      }

      const data = await response.json();
      if (data.status === "success" && data.queue) {
        setCurrentQueueDetails(data.queue);
        console.log("Current queue details:", data.queue);
      }
    } catch (error) {
      console.error("Error fetching current queue details:", error);
      Alert.alert("Error", "Failed to load current queue details");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchServiceDetails = async () => {
    try {
      const response = await fetch(
        `http://192.168.74.138:3000/api/partner/services/${serviceId}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch service details");
      }

      const data = await response.json();
      setServiceDetails(data);
    } catch (error) {
      console.error("Error fetching service details:", error);
      Alert.alert("Error", "Failed to load service details");
    }
  };

  const fetchServiceSetup = async () => {
    try {
      console.log(`Fetching service setup for service ID: ${serviceId}`);
      const response = await fetch(
        `http://192.168.74.138:3000/api/partner/service-setup/${serviceId}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch service setup");
      }

      const data = await response.json();
      console.log("Service setup API response:", JSON.stringify(data));

      if (data.status === "success" && data.hasSetup && data.data) {
        setServiceSetup(data.data);

        // Check if service has subunits
        if (data.data.hasSubUnits === true && Array.isArray(data.data.subUnits) && data.data.subUnits.length > 0) {
          console.log("Service has subunits:", data.data.subUnits.length);
          setHasSubUnits(true);
          setSubUnits(data.data.subUnits);

          // If current queue has a subunit, set it as selected
          if (currentQueueDetails?.hasSubUnits && currentQueueDetails?.subUnitId) {
            const subUnitIndex = parseInt(currentQueueDetails.subUnitId, 10);
            if (!isNaN(subUnitIndex) && subUnitIndex >= 0 && subUnitIndex < data.data.subUnits.length) {
              setSelectedSubUnit(data.data.subUnits[subUnitIndex]);
              console.log("Selected current subunit:", data.data.subUnits[subUnitIndex].name);
            }
          } else {
            // Default to first subunit
            setSelectedSubUnit(data.data.subUnits[0]);
            console.log("Selected default subunit:", data.data.subUnits[0].name);
          }
        } else {
          // Regular service without subunits
          setHasSubUnits(false);
          setSelectedSubUnit(null);
          console.log("Service does not have subunits");
        }

        setIsCalendarLoading(false);
      }
    } catch (error) {
      console.error("Error fetching service setup:", error);
      Alert.alert("Error", "Failed to load service setup");
      setIsCalendarLoading(false);
    }
  };

  const fetchLeaveDays = async () => {
    console.log(`Fetching leave days for service ID: ${serviceId}`);
    setIsCalendarLoading(true);
    try {
      const response = await fetch(
        `http://192.168.74.138:3000/api/partner/leave-days/${serviceId}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch leave days");
      }

      const data = await response.json();
      if (data.status === "success") {
        if (hasSubUnits && selectedSubUnit) {
          // For subunits, check if there are specific leave days for this subunit
          if (data.data && data.data.subUnits) {
            const subUnitIndex = subUnits.findIndex(unit => unit.name === selectedSubUnit.name);
            const subUnitKey = `subunit-${subUnitIndex}`;

            if (data.data.subUnits[subUnitKey] && Array.isArray(data.data.subUnits[subUnitKey])) {
              console.log(`Found leave days for subunit ${selectedSubUnit.name}:`, data.data.subUnits[subUnitKey].length);
              setLeaveDays(data.data.subUnits[subUnitKey]);
            } else {
              console.log(`No specific leave days for subunit ${selectedSubUnit.name}, using empty array`);
              setLeaveDays([]);
            }
          } else {
            console.log("No subUnits structure in leave days data, using empty array");
            setLeaveDays([]);
          }
        } else {
          // For regular services, use main leave days or legacy format
          if (data.data && data.data.main && Array.isArray(data.data.main)) {
            console.log("Using main leave days:", data.data.main.length);
            setLeaveDays(data.data.main);
          } else {
            setLeaveDays(Array.isArray(data.data) ? data.data : []);
          }
        }
      } else {
        setLeaveDays([]);
      }
    } catch (error) {
      console.error("Error fetching leave days:", error);
      setLeaveDays([]);
      setIsCalendarLoading(false);
    }
  };

  const generateMarkedDates = () => {
    if (!serviceSetup) {
      console.log("Cannot generate marked dates: serviceSetup is null");
      return;
    }

    // Determine which days are working days based on selected subunit or regular service
    let selectedDays: string[] = [];
    if (hasSubUnits && selectedSubUnit) {
      console.log(`Generating marked dates for subunit: ${selectedSubUnit.name}`);
      selectedDays = selectedSubUnit.selectedDays || [];
    } else if (serviceSetup.selectedDays) {
      console.log("Generating marked dates for regular service");
      selectedDays = serviceSetup.selectedDays;
    }

    // Map day names to day indices (0 = Sunday, 1 = Monday, etc.)
    const dayNameToIndex: Record<string, number> = {
      "Sunday": 0,
      "Monday": 1,
      "Tuesday": 2,
      "Wednesday": 3,
      "Thursday": 4,
      "Friday": 5,
      "Saturday": 6
    };

    // Convert selected days to day indices
    const selectedDayIndices = selectedDays.map(day => dayNameToIndex[day]).filter(index => index !== undefined);

    // Use either the server-determined working days or the selected days from setup
    const effectiveWorkingDays = workingDays.length > 0 ? workingDays : selectedDayIndices;
    console.log("Generating marked dates using working days:", effectiveWorkingDays);

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize today to start of day
    const nextMonth = new Date();
    nextMonth.setDate(today.getDate() + 7); // Only show next 7 days

    const marked: MarkedDates = {};
    const leaveDaysSet = new Set((leaveDays || []).map(d => new Date(d).toISOString().split('T')[0]));
    console.log(`Using ${leaveDaysSet.size} leave days for ${hasSubUnits && selectedSubUnit ? selectedSubUnit.name : 'regular service'} in generateMarkedDates`);

    // Get current queue date for comparison
    const currentQueueDate = currentQueueDetails?.date ? new Date(currentQueueDetails.date).toISOString().split('T')[0] : null;

    // Generate available dates for next 7 days
    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      const dateString = date.toISOString().split("T")[0];
      const dayOfWeek = date.getDay();

      // Check if this day is a working day
      const isWorkingDay = effectiveWorkingDays.includes(dayOfWeek);

      // Check if this day is a leave day
      const isLeaveDay = leaveDaysSet.has(dateString);

      // Check if this is the current queue date
      const isCurrentQueueDate = dateString === currentQueueDate;

      if (isWorkingDay && !isLeaveDay) {
        marked[dateString] = {
          marked: true,
          disabled: isCurrentQueueDate, // Disable current queue date
          disableTouchEvent: isCurrentQueueDate,
          dotColor: isCurrentQueueDate ? "#FF6B6B" : "#159AFF",
        };
      }
    }

    setMarkedDates(marked);
    setIsCalendarLoading(false);

    // Auto-select first available date (excluding current queue date)
    const availableDates = Object.entries(marked)
      .filter(([_, dateData]) => dateData.marked && !dateData.disabled)
      .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime());

    if (availableDates.length > 0) {
      const firstAvailableDate = availableDates[0][0];
      console.log(`Auto-selecting first available date: ${firstAvailableDate}`);
      setSelectedDate(firstAvailableDate);

      // Mark the date as selected in the marked dates
      marked[firstAvailableDate] = {
        ...marked[firstAvailableDate],
        selected: true,
        selectedColor: "#159AFF",
      };
      setMarkedDates(marked);
    }
  };

  const fetchTimeSlotsForDate = async (date: string) => {
    try {
      setIsTimeSlotsLoading(true);
      console.log(`Fetching time slots for date: ${date}`);

      // Determine the API endpoint based on whether we're using subunits
      let apiUrl = `http://192.168.74.138:3000/api/partner/services/${serviceId}/time-slots/${date}`;

      // If using subunits, add the subunit parameter
      if (hasSubUnits && selectedSubUnit) {
        const subUnitIndex = subUnits.findIndex(unit => unit.name === selectedSubUnit.name);
        if (subUnitIndex !== -1) {
          apiUrl += `?subUnitId=${subUnitIndex}`;
          console.log(`Using subunit-specific time slots API for ${selectedSubUnit.name}`);
        }
      }

      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error("Failed to fetch time slots");
      }

      const data = await response.json();
      console.log(`Time slots API response for ${date}:`, JSON.stringify(data));

      if (data.status === "success" && Array.isArray(data.timeSlots)) {
        // Convert API response to our TimeSlot interface
        const slots: TimeSlot[] = data.timeSlots.map((slot: any) => ({
          timeSlot: slot.timeSlot,
          normalQueueCount: slot.normalQueueCount || 0,
          vipQueueCount: slot.vipQueueCount || 0,
        }));

        // Filter out time slots that have already passed for today
        const filteredSlots = filterPastTimeSlots(date, slots);

        console.log(`Setting ${filteredSlots.length} time slots for ${date}`);
        setTimeSlots(filteredSlots);
      } else {
        setTimeSlots([]);
      }
    } catch (error) {
      console.error("Error fetching time slots:", error);
      setTimeSlots([]);
    } finally {
      setIsTimeSlotsLoading(false);
    }
  };

  // Filter time slots that have already passed for today
  const filterPastTimeSlots = (date: string, slots: TimeSlot[]): TimeSlot[] => {
    // If the date is not today, return all slots
    const today = new Date().toISOString().split('T')[0];
    if (date !== today) {
      return slots;
    }

    // If it's today, check against the end time of each slot
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    return slots.filter(slot => {
      // Extract the end time from the time slot (format: "HH:MM AM/PM - HH:MM AM/PM")
      const timeSlotParts = slot.timeSlot.split(' - ');

      if (timeSlotParts.length !== 2) {
        return true; // Keep slot if format is unexpected
      }

      // Parse end time (handle AM/PM format)
      const endTime = timeSlotParts[1].trim();
      const endTimeParts = endTime.split(' ');

      if (endTimeParts.length !== 2) {
        return true;
      }

      const timeComponents = endTimeParts[0].split(':');
      if (timeComponents.length !== 2) {
        return true;
      }

      let endHour = parseInt(timeComponents[0], 10);
      const endMinute = parseInt(timeComponents[1], 10);
      const isPM = endTimeParts[1].toUpperCase() === 'PM';

      // Convert to 24-hour format
      if (isPM && endHour < 12) {
        endHour += 12;
      } else if (!isPM && endHour === 12) {
        endHour = 0;
      }

      // Allow booking if current time is before the end time
      return endHour > currentHour || (endHour === currentHour && endMinute > currentMinute);
    });
  };

  const getDayName = (dayIndex: number): string => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[dayIndex] || 'Unknown';
  };

  const handleDateSelect = (date: { dateString: string; day: number; month: number; year: number }) => {
    const dateString = date.dateString;
    const selectedDateObj = new Date(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize today to start of day

    // Prevent selection of past dates
    if (selectedDateObj < today) {
      return;
    }

    // Check if the date is disabled (current queue date)
    if (markedDates[dateString]?.disabled) {
      return;
    }

    // Update selected date
    setSelectedDate(dateString);

    // Update marked dates to highlight the selected date
    const updatedMarkedDates = { ...markedDates };

    // Reset previously selected date
    Object.keys(updatedMarkedDates).forEach((key) => {
      if (updatedMarkedDates[key].selected) {
        updatedMarkedDates[key] = {
          ...updatedMarkedDates[key],
          selected: false,
        };
      }
    });

    // Mark new selected date
    updatedMarkedDates[dateString] = {
      ...updatedMarkedDates[dateString],
      selected: true,
      selectedColor: "#159AFF",
    };

    setMarkedDates(updatedMarkedDates);

    // Reset time slot selection
    setSelectedTimeSlot(null);

    // Fetch time slots for the selected date
    fetchTimeSlotsForDate(dateString);
  };

  const handleTimeSlotSelect = (timeSlot: string) => {
    setSelectedTimeSlot(timeSlot);
  };

  const handleReschedule = async () => {
    if (!selectedDate || !selectedTimeSlot) {
      Alert.alert("Error", "Please select a new date and time slot");
      return;
    }

    if (!currentQueueDetails) {
      Alert.alert("Error", "Current queue details not found");
      return;
    }

    // Check if user is trying to reschedule to the same date and time
    const currentDate = new Date(currentQueueDetails.date).toISOString().split('T')[0];
    if (selectedDate === currentDate && selectedTimeSlot === currentQueueDetails.timeSlot) {
      Alert.alert("Error", "Please select a different date or time slot");
      return;
    }

    try {
      setIsLoading(true);

      const rescheduleData: any = {
        queueId: queueId,
        newDate: selectedDate,
        newTimeSlot: selectedTimeSlot,
        userId: user?.id,
      };

      // Add subunit information if applicable
      if (hasSubUnits && selectedSubUnit) {
        const subUnitIndex = subUnits.findIndex(unit => unit.name === selectedSubUnit.name);
        if (subUnitIndex !== -1) {
          rescheduleData.subUnitId = subUnitIndex.toString();
          rescheduleData.subUnitName = selectedSubUnit.name;
        }
      }

      const response = await fetch(
        `http://192.168.74.138:3000/api/customer/queue/${queueId}/reschedule`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(rescheduleData),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to reschedule queue");
      }

      const data = await response.json();

      if (data.status === "success") {
        Alert.alert(
          "Success",
          "Your queue has been rescheduled successfully!",
          [
            {
              text: "OK",
              onPress: () => {
                // Navigate back to queue status with the same queueId
                router.replace({
                  pathname: "/(root)/queue-status",
                  params: {
                    queueId: queueId,
                    serviceId: serviceId,
                  },
                });
              },
            },
          ]
        );
      } else {
        throw new Error(data.message || "Failed to reschedule queue");
      }
    } catch (error) {
      console.error("Error rescheduling queue:", error);
      Alert.alert("Error", "Failed to reschedule queue. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading && !currentQueueDetails) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#159AFF" />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <View className="bg-white pb-8">
        <View className="px-8 pt-12">
          <View className="flex-row justify-start mt-4">
            <TouchableOpacity
              className="flex-row items-center absolute left-0 -top-2 border border-secondary-600 w-12 h-12 rounded-full justify-center"
              onPress={() => router.back()}
            >
              <Image source={images.back} className="w-4 h-4" />
            </TouchableOpacity>

            <View className="flex ml-20 items-center">
              <Text className="font-poppins-medium text-xl mb-2">
                Reschedule Queue
              </Text>
            </View>
          </View>
        </View>
      </View>

      <ScrollView className="flex-1 px-6">
        {/* Current Queue Details */}
        {currentQueueDetails && (
          <View className="mb-6 p-4 bg-gray-50 rounded-xl">
            <Text className="font-poppins-medium text-secondary-600 text-base mb-3">
              Current Queue Details
            </Text>
            <View className="space-y-2">
              <View className="flex-row items-center">
                <Text className="text-secondary-600 text-sm w-20">Service:</Text>
                <Text className="font-poppins-medium text-secondary-800">
                  {currentQueueDetails.serviceName}
                </Text>
              </View>
              {currentQueueDetails.hasSubUnits && currentQueueDetails.subUnitName && (
                <View className="flex-row items-center">
                  <Text className="text-secondary-600 text-sm w-20">Unit:</Text>
                  <Text className="font-poppins-medium text-secondary-800">
                    {currentQueueDetails.subUnitName}
                  </Text>
                </View>
              )}
              <View className="flex-row items-center">
                <Text className="text-secondary-600 text-sm w-20">Date:</Text>
                <Text className="font-poppins-medium text-secondary-800">
                  {new Date(currentQueueDetails.date).toLocaleDateString()}
                </Text>
              </View>
              <View className="flex-row items-center">
                <Text className="text-secondary-600 text-sm w-20">Time:</Text>
                <Text className="font-poppins-medium text-secondary-800">
                  {currentQueueDetails.timeSlot}
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Subunit selector for services with subunits */}
        {hasSubUnits && subUnits.length > 0 && (
          <View className="mb-6">
            <Text className="font-poppins-medium text-secondary-600 text-base p-4 mb-2">
              Select Service Unit
            </Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              className="pb-2"
            >
              {subUnits.map((unit, index) => {
                const isCurrentUnit = currentQueueDetails?.hasSubUnits &&
                  currentQueueDetails?.subUnitName === unit.name;
                const isSelected = selectedSubUnit?.name === unit.name;

                return (
                  <TouchableOpacity
                    key={index}
                    className={`mr-4 px-6 py-3 rounded-xl ${
                      isCurrentUnit
                        ? 'bg-red-100 border border-red-300'
                        : isSelected
                        ? 'bg-primary-500'
                        : 'bg-gray-100'
                    }`}
                    onPress={() => {
                      if (!isCurrentUnit) {
                        setSelectedSubUnit(unit);
                        setSelectedDate("");
                        setSelectedTimeSlot(null);
                        setTimeSlots([]);
                      }
                    }}
                    disabled={isCurrentUnit}
                  >
                    <Text
                      className={`font-poppins-medium text-base ${
                        isCurrentUnit
                          ? 'text-red-600'
                          : isSelected
                          ? 'text-white'
                          : 'text-secondary-600'
                      }`}
                    >
                      {unit.name} {isCurrentUnit && '(Current)'}
                    </Text>
                    <Text
                      className={`text-xs ${
                        isCurrentUnit
                          ? 'text-red-500'
                          : isSelected
                          ? 'text-white'
                          : 'text-secondary-600'
                      }`}
                    >
                      ₹{unit.pricePerHead} • {unit.avgServeTime} mins
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </View>
        )}

        {/* Date Selection */}
        <View className="mb-4">
          <Text className="font-poppins-medium text-secondary-600 text-base p-4 mb-4">
            Select New Date
          </Text>

          <View className="mb-6">
            {isCalendarLoading ? (
              <View className="h-[150px] justify-center items-center">
                <ActivityIndicator size="large" color="#159AFF" />
                <Text className="mt-4 text-secondary-600">Loading available dates...</Text>
              </View>
            ) : (
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                className="pb-4"
              >
                {/* Filter and display only available dates */}
                {Object.entries(markedDates)
                  .filter(([_, dateData]) =>
                    // Only show dates that are marked (available)
                    dateData.marked
                  )
                  .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())
                  .map(([dateString, dateData]) => {
                    const date = new Date(dateString);
                    const dayName = getDayName(date.getDay()).substring(0, 3);
                    const dayNumber = date.getDate();
                    const month = date.toLocaleString('default', { month: 'short' });
                    const isToday = dateString === new Date().toISOString().split('T')[0];
                    const isSelected = dateString === selectedDate;
                    const isCurrentDate = dateData.disabled;

                    return (
                      <TouchableOpacity
                        key={dateString}
                        className={`mr-4 px-6 py-4 rounded-xl border ${
                          isCurrentDate
                            ? 'bg-red-100 border-red-300'
                            : isSelected
                            ? 'bg-primary-500 border-primary-500'
                            : isToday
                            ? 'bg-white border-primary-500'
                            : 'bg-white border-gray-200'
                        }`}
                        onPress={() => {
                          if (!isCurrentDate) {
                            handleDateSelect({ dateString, day: dayNumber, month: date.getMonth(), year: date.getFullYear() });
                          }
                        }}
                        disabled={isCurrentDate}
                      >
                        <View className="items-center justify-center">
                          <Text className={`font-poppins-medium text-base ${
                            isCurrentDate
                              ? 'text-red-600'
                              : isSelected
                              ? 'text-white'
                              : 'text-secondary-600'
                          }`}>
                            {dayName}
                          </Text>
                          <Text className={`text-2xl font-poppins-medium mt-1 ${
                            isCurrentDate
                              ? 'text-red-600'
                              : isSelected
                              ? 'text-white'
                              : 'text-secondary-600'
                          }`}>
                            {dayNumber}
                          </Text>
                          <Text className={`text-xs mt-1 ${
                            isCurrentDate
                              ? 'text-red-500'
                              : isSelected
                              ? 'text-white'
                              : 'text-secondary-400'
                          }`}>
                            {month}
                          </Text>
                          {isCurrentDate && (
                            <Text className="text-xs text-red-500 mt-1">Current</Text>
                          )}
                        </View>
                      </TouchableOpacity>
                    );
                  })}

                {/* Show message if no available dates */}
                {Object.entries(markedDates).filter(([_, dateData]) => dateData.marked && !dateData.disabled).length === 0 && (
                  <View className="justify-center items-center px-4 py-6">
                    <Text className="text-secondary-600 text-center">
                      No available dates for rescheduling in the next 7 days.
                    </Text>
                    <Text className="text-secondary-400 text-center mt-2">
                      Please contact the service provider for more options.
                    </Text>
                  </View>
                )}
              </ScrollView>
            )}
          </View>

          <View className="mb-4 px-4">
            <Text className="text-secondary-300 text-sm">
              <Text className="font-poppins-medium">Note:</Text> Your current queue date is disabled. Only available dates for the next 7 days are shown.
            </Text>
          </View>
        </View>

        {/* Time Slot Selection */}
        {selectedDate && (
          <View className="mb-6">
            <Text className="font-poppins-medium text-secondary-600 text-base p-4 mb-2">
              Select New Time Slot
            </Text>

            {isTimeSlotsLoading ? (
              <View className="justify-center items-center py-8">
                <ActivityIndicator size="large" color="#159AFF" />
                <Text className="mt-4 text-secondary-600">Loading available time slots...</Text>
              </View>
            ) : timeSlots.length === 0 ? (
              <Text className="text-secondary-600 px-4">
                No time slots available for this date
              </Text>
            ) : (
              <View className="flex-col flex-wrap">
                {timeSlots.map((slot, index) => {
                  // Check if this is the current queue's time slot
                  const isCurrentTimeSlot = currentQueueDetails?.timeSlot === slot.timeSlot &&
                    selectedDate === new Date(currentQueueDetails?.date || '').toISOString().split('T')[0];

                  return (
                    <TouchableOpacity
                      key={index}
                      className={`w-full flex-row mr-3 mb-6 px-4 py-4 rounded-3xl ${
                        isCurrentTimeSlot
                          ? "bg-red-100 border border-red-300"
                          : selectedTimeSlot === slot.timeSlot
                          ? "bg-primary-500/20 border border-primary-500"
                          : "bg-white border border-primary-200"
                      }`}
                      onPress={() => !isCurrentTimeSlot && handleTimeSlotSelect(slot.timeSlot)}
                      disabled={isCurrentTimeSlot}
                    >
                      <View className="flex-row justify-between items-center w-full">
                        <Text
                          className={`font-poppins-medium text-base ml-2 text-center ${
                            isCurrentTimeSlot
                              ? "text-red-600"
                              : selectedTimeSlot === slot.timeSlot
                              ? "text-secondary-500"
                              : "text-secondary-600"
                          }`}
                        >
                          {slot.timeSlot} {isCurrentTimeSlot && '(Current)'}
                        </Text>

                        <View className={`flex-row ${isCurrentTimeSlot ? "bg-red-50" : "bg-white"} px-4 py-2 rounded-xl justify-center items-center`}>
                          <View className="flex-row items-center mr-3">
                            <Image
                              source={images.run}
                              className="w-4 h-5 mr-2"
                              style={{ tintColor: isCurrentTimeSlot ? "#DC2626" : "#159AFF" }}
                            />
                            <Text
                              className={`text-base font-poppins-medium ${
                                isCurrentTimeSlot
                                  ? "text-red-600"
                                  : selectedTimeSlot === slot.timeSlot
                                  ? "text-secondary-600"
                                  : "text-secondary-600"
                              }`}
                            >
                              {slot.normalQueueCount}
                            </Text>
                          </View>
                          <Text className={`text-lg font-poppins-medium mr-3 ${
                            isCurrentTimeSlot ? "text-red-600" : "text-secondary-600"
                          }`}>
                            |
                          </Text>
                          <View className="flex-row items-center">
                            <Image
                              source={images.run}
                              className="w-4 h-5 mr-2"
                              style={{ tintColor: isCurrentTimeSlot ? "#DC2626" : "#FFB800" }}
                            />
                            <Text
                              className={`text-base font-poppins-medium ${
                                isCurrentTimeSlot
                                  ? "text-red-600"
                                  : selectedTimeSlot === slot.timeSlot
                                  ? "text-secondary-600"
                                  : "text-secondary-600"
                              }`}
                            >
                              {slot.vipQueueCount}
                            </Text>
                          </View>
                        </View>
                      </View>
                    </TouchableOpacity>
                  );
                })}
              </View>
            )}
          </View>
        )}
      </ScrollView>

      <View className="p-6 border-t flex justify-center items-center border-gray-200">
        <TouchableOpacity
          className={`w-[390px] py-6 rounded-2xl items-center ${
            selectedDate && selectedTimeSlot ? "bg-primary-500" : "bg-gray-300"
          }`}
          onPress={handleReschedule}
          disabled={!selectedDate || !selectedTimeSlot || isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Text className="text-white font-poppins-medium text-lg">
              Reschedule Queue
            </Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default RescheduleQueueScreen;
