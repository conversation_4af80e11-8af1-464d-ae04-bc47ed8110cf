"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddTermsAcceptedToServices1710539100000 = void 0;
class AddTermsAcceptedToServices1710539100000 {
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "services" ADD COLUMN "termsAccepted" boolean DEFAULT false`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "services" DROP COLUMN "termsAccepted"`);
    }
}
exports.AddTermsAcceptedToServices1710539100000 = AddTermsAcceptedToServices1710539100000;
//# sourceMappingURL=1710539100000-AddTermsAcceptedToServices.js.map