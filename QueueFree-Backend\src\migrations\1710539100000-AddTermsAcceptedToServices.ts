import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTermsAcceptedToServices1710539100000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "services" ADD COLUMN "termsAccepted" boolean DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "services" DROP COLUMN "termsAccepted"`);
    }
}
