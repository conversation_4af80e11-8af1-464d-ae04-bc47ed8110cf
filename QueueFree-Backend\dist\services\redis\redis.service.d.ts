export declare class RedisService {
    private redis;
    constructor();
    get(key: string): Promise<any>;
    set(key: string, value: any, options?: {
        ex?: number;
    }): Promise<void>;
    getQueue(queueId: number | string): Promise<any>;
    saveQueue(queueId: number | string, queueData: any, ttlSeconds?: number): Promise<void>;
    private cleanupOldQueueKeys;
    updateQueueStatus(queueId: number | string, status: string, isPriority?: boolean): Promise<void>;
    updateQueueCheckInStatus(queueId: number | string, isCheckedIn: boolean, isPriority?: boolean): Promise<void>;
    deleteQueue(queueId: number | string): Promise<void>;
    saveQueuePosition(serviceId: number | string, date: string, timeSlot: string, positionData: any): Promise<void>;
    getQueuePosition(serviceId: number | string, date: string, timeSlot: string): Promise<any>;
    saveServiceQueues(serviceId: number | string, date: string, queuesData: any): Promise<void>;
    getServiceQueues(serviceId: number | string, date: string): Promise<any>;
    invalidateServiceQueues(serviceId: number | string, date: string): Promise<void>;
    del(key: string): Promise<void>;
    getKeys(pattern: string): Promise<string[]>;
    mget(...keys: string[]): Promise<any[]>;
    invalidateQueuePosition(serviceId: number | string, date: string, timeSlot: string): Promise<void>;
    migrateAllQueueKeys(): Promise<void>;
    refreshServingQueuesTTL(): Promise<void>;
}
